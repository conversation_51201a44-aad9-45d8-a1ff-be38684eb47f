# Magento 2 German LocalePack fr_FR

Deutsches Sprachpaket für Magento 2 Community Edition (Version 2.2.6)

# Installation
 - Alle Dateien nach `/app/i18n/xortex/fr_FR/` kopieren

Aus dem Magento-Root-Verzeichnis folgende Befehle aufrufen:
```bash
rm pub/static/frontend/Magento/luma/fr_FR/js-translation.json
php bin/magento setup:static-content:deploy fr_FR
php bin/magento setup:upgrade
rm -rf var/di
php bin/magento setup:di:compile
```

# Installation mit Composer
```bash
composer require splendidinternet/mage2-locale-de-de
rm pub/static/frontend/Magento/luma/fr_FR/js-translation.json
php bin/magento setup:static-content:deploy fr_FR
```

# Add new phrases

To translate new phrases follow these steps:

### Get phrases from Magento

Run this in your Magento 2 installation:

```bash
php bin/magento i18n:collect-phrases -m > phrases.csv
```

### Compare phrases with old translation file

Copy the `phrases.csv` into this repository and run:

```bash
php check_new.php
```

This will output a new file `fr_FR_new.csv` which only contains the
phrases that are not yet translated in `fr_FR.csv`.

### Translate phrases

Now you should translate these phrases. Enter the translated phrase
in the *second* column.

### Copy new phrases and create a pull request

Copy the new phrases to `fr_FR.csv`.

**IMPORTANT**: sort the file alphabetically based on the first column, e.g. with LibreOffice.