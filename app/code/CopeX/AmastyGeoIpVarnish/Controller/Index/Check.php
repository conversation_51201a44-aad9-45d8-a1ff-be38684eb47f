<?php
/**
 * Copyright © Andreas Pointner All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace CopeX\AmastyGeoIpVarnish\Controller\Index;

use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\ActionInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Session\SessionManagerInterface;
use Magento\Store\Model\ScopeInterface;

class Check implements ActionInterface, HttpGetActionInterface
{

    private SessionManagerInterface $sessionManager;
    private JsonFactory $jsonFactory;
    private ScopeConfigInterface $scopeConfig;

    public function __construct(
        SessionManagerInterface $sessionManager,
        JsonFactory $jsonFactory,
        ScopeConfigInterface $scopeConfig
    ) {
        $this->sessionManager = $sessionManager;
        $this->jsonFactory = $jsonFactory;
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * Execute view action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        try {
            $needShow = $this->sessionManager->getNeedShow() ?? false;
            if ($needShow && $this->sessionManager->getAmPopupCountry()) {
                $countries = $this->scopeConfig->getValue(
                    'amgeoipredirect/country_store/affected_countries',
                    ScopeInterface::SCOPE_STORE
                );
                // \Magento\Framework\App\ObjectManager::getInstance()->get(\Psr\Log\LoggerInterface::class)->error($this->sessionManager->getAmPopupCountry());
                $needShow = strpos($countries,$this->sessionManager->getAmPopupCountry() ?? "") === false;
            }
            return $this->jsonResponse(['show' => $needShow ]);
        } catch (\Exception $e) {
            return $this->jsonResponse($e->getMessage());
        }
    }

    /**
     * Create json response
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function jsonResponse($response = '')
    {
        return $this->jsonFactory->create()->setData($response);
    }
}
