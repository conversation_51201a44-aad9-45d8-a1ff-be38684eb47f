<?php
/** @var \Magento\Framework\Escaper $escaper */
/** @var $block \Magento\Framework\View\Element\Template */
?>
<script>
    function initPopper (element, tooltip) {
        return {
        popper: null,
        el: element,
        tooltip: tooltip,
        isOpen: false,
        init() {
            Object.entries({
                'mouseenter': () => this.show(),
                'mouseleave': () => this.hide(),
                'focus': () => this.show(),
                'blur': () => this.hide()
            }).forEach(([key, value]) => {
                this.el.addEventListener(key, value);
            });
            document.addEventListener('mousemove',(e) => {
                if(this.isOpen && !this.el.contains(e.target)){ this.hide();}
            });
        },
        loadPopper() {
            let self = this;
            if (this.popper === null) {
                lazyLoadCss("<?= $block->getViewFileUrl("CopeX_HyvaTheme::css/popper.css") ?>");
                lazyLoadJs("<?= $block->getViewFileUrl("CopeX_HyvaTheme::js/popper.min.js") ?>").then(function () {
                    self.popper = Popper.createPopper(self.el, self.tooltip, {
                        modifiers: [
                            {
                                name: 'offset',
                                options: {
                                    offset: [0, 4],
                                },
                            },
                        ],
                    });
                    self.show();
                });
            }
        },
        show() {
            if (this.popper === null) {
                this.loadPopper();
            }
            else {
                this.tooltip.setAttribute('data-show', '');
                this.popper.setOptions((options) => ({
                    ...options,
                    modifiers: [
                        ...options.modifiers,
                        {name: 'eventListeners', enabled: true},
                    ],
                }));
                this.popper.update();
            }
            this.isOpen = true;
        },
        hide() {
            this.tooltip.removeAttribute('data-show');
            this.popper && this.popper.setOptions((options) => ({
                ...options,
                modifiers: [
                    ...options.modifiers,
                    {name: 'eventListeners', enabled: false},
                ],
            }));
            this.isOpen = false;
        }
    }
    }
</script>