<?php
namespace CopeX\CheckoutPaymentErrorDiscount\Controller\Checkout;

use Magento\Framework\App\Action\HttpPostActionInterface as HttpPostActionInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Store\Model\ScopeInterface;

/**
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class CouponPost implements HttpPostActionInterface
{
    /**
     * Sales quote repository
     *
     * @var \Magento\Quote\Api\CartRepositoryInterface
     */
    protected $quoteRepository;

    /**
     * Coupon factory
     *
     * @var \Magento\SalesRule\Model\CouponFactory
     */
    protected $couponFactory;
    private RequestInterface $request;
    private \Magento\Checkout\Model\Cart $cart;
    private JsonFactory $jsonFactory;
    private ScopeConfigInterface $scopeConfig;

    public function __construct(
        \Magento\SalesRule\Model\CouponFactory $couponFactory,
        \Magento\Quote\Api\CartRepositoryInterface $quoteRepository,
        RequestInterface $request,
        \Magento\Checkout\Model\Cart $cart,
        JsonFactory $jsonFactory,
        ScopeConfigInterface $scopeConfig
    ) {
        $this->couponFactory = $couponFactory;
        $this->quoteRepository = $quoteRepository;
        $this->request = $request;
        $this->cart = $cart;
        $this->jsonFactory = $jsonFactory;
        $this->scopeConfig = $scopeConfig;
    }

    public function execute()
    {
        $cartQuote = $this->cart->getQuote();
        $couponCode = $this->getCouponCode();
        $oldCouponCode = $cartQuote->getCouponCode();

        $codeLength = strlen($couponCode);
        if (!$codeLength && !strlen($oldCouponCode)) {
            return $this->jsonFactory->create([false]);
        }

        try {
            $isCodeLengthValid = $codeLength && $codeLength <= \Magento\Checkout\Helper\Cart::COUPON_CODE_MAX_LENGTH;

            $itemsCount = $cartQuote->getItemsCount();
            if ($itemsCount) {
                $cartQuote->getShippingAddress()->setCollectShippingRates(true);
                $cartQuote->setCouponCode($isCodeLengthValid ? $couponCode : '')->collectTotals();
                $this->quoteRepository->save($cartQuote);
            }

            if ($codeLength) {
                $coupon = $this->couponFactory->create();
                $coupon->load($couponCode, 'code');
                if (!$itemsCount) {
                    if ($isCodeLengthValid && $coupon->getId()) {
                        $this->cart->getCheckoutSession()->getQuote()->setCouponCode($couponCode)->save();
                    }
                } else {
                    if ($isCodeLengthValid && $coupon->getId() && $couponCode == $cartQuote->getCouponCode()) {
                        return $this->jsonFactory->create([true]);
                    }
                }
            }
        } catch (\Exception $e) {
        }
        return $this->jsonFactory->create([false]);
    }

    private function getConfigValue($key){
        return $this->scopeConfig->getValue("checkout/payment_error_group/".$key, ScopeInterface::SCOPE_STORE);
    }

    public function getCouponCode()
    {
        $couponCode = $this->request->getParam('remove') == 1
            ? ''
            : trim($this->request->getParam('coupon_code'));

        $oldCouponCode = $this->cart->getQuote()->getCouponCode();
        $couponCodeReplace = $this->getConfigValue('coupon_code_discounted_replace');
        if ($couponCode && $couponCodeReplace &&  $oldCouponCode === $couponCodeReplace
            && $couponCode === $this->getConfigValue('coupon_code')) {
            $couponCode = $this->getConfigValue("coupon_code_discounted");
        }
        return $couponCode;
    }
}
