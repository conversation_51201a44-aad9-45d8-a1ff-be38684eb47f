<?php

namespace CopeX\ProductDetailSeoLink\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Store\Model\ScopeInterface;

/**
 * Class Data
 * @package CopeX\ProductDetailSeoLink\Helper
 */
class Data extends AbstractHelper
{
    const CONFIG_PATH_ENABLED = "seolink/settings/enabled";
    const CONFIG_PATH_EXCLUDED_CATEGORIES = "seolink/settings/exclude_categories";
    const CONFIG_PATH_CATEGORIES_DEPTH = "seolink/settings/category_depth";
    const CONFIG_PATH_LIMIT = "seolink/settings/item_limit";

    /**
     * @return mixed
     */
    public function isEnabled()
    {
        return $this->getConfig(self::CONFIG_PATH_ENABLED);
    }

    /**
     * @return mixed
     */
    public function getExcludedCategories()
    {
        return $this->getConfig(self::CONFIG_PATH_EXCLUDED_CATEGORIES);
    }

    /**
     * @return mixed
     */
    public function getLimit()
    {
        return $this->getConfig(self::CONFIG_PATH_LIMIT);
    }

    /**
     * @return mixed
     */
    public function getDepth()
    {
        return $this->getConfig(self::CONFIG_PATH_CATEGORIES_DEPTH);
    }

    /**
     * @param string $config_path
     * @return mixed
     */
    protected function getConfig($config_path)
    {
        return $this->scopeConfig->getValue($config_path, ScopeInterface::SCOPE_STORE);
    }


}
