<?php

/** @var $block \Magento\Framework\View\Element\Template */
/** @var $viewModel \CopeX\ExitIntent\ViewModel\Popup\StaticBlock */
$viewModel = $block->getViewModel();
$blockContent = $viewModel->getBlockContent();
?>
<?php if($blockContent) : ?>
<div class="exit-intent-popup" data-mage-init='{"exitIntent":{"seconds":"<?= $viewModel->showWithinSeconds() ?>","firstVisit":"<?= $viewModel->getOnlyShowOnFirstVisit() ?>","showOnce":"<?= $viewModel->getShowOnce() ?>"}}'>
    <div class="content">
        <span class="close"></span>
        <?= $blockContent; ?>
    </div>
</div>
<?php endif; ?>