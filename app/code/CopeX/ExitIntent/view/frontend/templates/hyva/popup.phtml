<?php
/** @var \Magento\Framework\Escaper $escaper */
/** @var $block \Magento\Framework\View\Element\Template */
/** @var $viewModel \CopeX\ExitIntent\ViewModel\Popup\StaticBlock */
$viewModel = $block->getViewModel();
$blockContent = $viewModel->getBlockContent();
?>
<?php if($blockContent) : ?>
<div id="exit-intent-container" x-cloak x-data="initExitIntent()" x-init="init()">
    <template x-if="exitIntent">
        <div class="exit-intent-popup" :class="{'visible' : isOpen }">
            <div class="content max-w-screen-2xl">
                <span class="close"></span>
                <?= $blockContent; ?>
            </div>
        </div>
    </template>
</div>
<script>
    function initExitIntent() {
        return {
            exitIntent: false,
            isOpen: false,
            now: new Date(),
            showOnFirstVisit: parseInt("<?= $viewModel->getOnlyShowOnFirstVisit() ?>"),
            seconds:  <?= $viewModel->showWithinSeconds() ?>,
            showOnce: <?= !$viewModel->getShowOnce() ? "true" : "false" ?>,
            boundMouseEvent: null,
            open (){
                this.exitIntent = true;
                this.isOpen = true;
            },
            close(){
                this.isOpen = false;
            },
            exit (e, self) {
                const shouldExit =
                    [...e.target.classList].includes('exit-intent-popup') || // user clicks on mask
                    e.target.className === 'close' || // user clicks on the close icon
                    e.keyCode === 27; // user hits escape

                if (shouldExit) {
                    self.close();
                }
            },
            mouseEvent (e, self) {
                const shouldShowExitIntent =
                    !e.toElement &&
                    !e.relatedTarget &&
                    e.clientY < 10;

                if (shouldShowExitIntent){
                    if( firstVisit === undefined || self.showOnFirstVisit === 0 || (firstVisit && self.showOnFirstVisit === 1 ) ) {
                        const elapsed = new Date(), dif = (elapsed.getTime() - self.now.getTime()) / 1000;
                        if (self.seconds === 0 || dif < self.seconds) {
                            document.removeEventListener('mouseout', self.boundMouseEvent);
                            lazyLoadCss('<?= $escaper->escapeUrl($block->getViewFileUrl("CopeX_ExitIntent::css/exitintent.css")) ?>');
                            self.open();
                            if (<?= $viewModel->getShowOnce() ?>) {
                                hyva.setCookie('exitIntentShown', true, 30);
                            }
                        }
                    }
                }
            },
            init(){
                if (!hyva.getCookie('exitIntentShown') || this.showOnce) {
                    setTimeout(() => {
                        this.boundMouseEvent = (e) => {this.mouseEvent(e,this)};
                        document.addEventListener('mouseout', this.boundMouseEvent);
                        document.addEventListener('keydown',  (e) => {this.exit(e,this)});
                        document.getElementById('exit-intent-container').addEventListener('click',  (e) => {this.exit(e,this)});
                    }, 0);
                }
            }
        }
    }
</script>
<?php endif; ?>