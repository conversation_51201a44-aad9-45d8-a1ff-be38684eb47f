<?php

use Hyva\Theme\Model\ViewModelRegistry;

/** @var \Magento\Framework\Escaper $escaper */
/** @var \Magento\Framework\View\Element\Template $block */

/** @var ViewModelRegistry $viewModels */

/** @var \CopeX\Swiper\ViewModel\Swiper $swiper */

$swiper = $block->getViewModel();

$imageTag = $viewModels->require(\CopeX\HyvaTheme\ViewModel\ImageTag::class);

$pagination = $block->getPagination() ?? false;
$headline = $block->getHeadline();
$lightbox = $block->getLightbox() ?? true;
$galleryImages = $swiper->prepareImages($block->getGalleryImages());

$thumbs = $block->getThumbs() ?? true;
$thumbs = $thumbs && count($galleryImages) > 1;

$width = $block->getWidth() ?? "w-10/12";
$numberThumbs = $block->getNumberThumbs() ?? $swiper->getNumberThumbs() ?? 5;
$sliderId = time() . uniqid();
$mainArrowColor = $block->getMainArrowColor() ?? 'currentColor';
$itemsCount = count($galleryImages);
$additionalImages = [];
$htmlImageCount = $block->getMaxHtmlImageCount() ?? 10;
if($itemsCount > $htmlImageCount){
    $additionalImages = array_slice($galleryImages, $htmlImageCount);
    $galleryImages = array_slice($galleryImages, 0, $htmlImageCount);
}
$slidesPerView = 1;
if($block->getSlidesPerView()){
    $slidesPerView = min($itemsCount, $block->getSlidesPerView());
}
$enabled = $itemsCount > $slidesPerView;

$loadMainLazy = $block->getLoadMainLazy() ?? true;

?>
<div class="min-w-0 ">
    <?php if ($itemsCount): ?>
        <?php if ($headline): ?>
            <h2><?= $headline ?></h2>
        <?php endif; ?>
        <div x-data="initSwiper<?= $sliderId ?>()"
             x-intersect.once.margin.-100px="init()"
             x-defer="intersect"
             x-bind="eventListeners" <?= $block->getHtmlId() ? ("id='" . $block->getHtmlId() . "'") : "" ?>
             <?php if($additionalImages) : ?> data-additional='<?= $swiper->getSerializer()->serialize($additionalImages)?>'<?php endif; ?>
            class="bg-white">
            <div class="swiper-main relative  mx-auto swiper overflow-hidden <?= $block->getMainClasses() ?? "" ?>">
                <?php if($enabled): ?>
                    <div class="absolute inset-y-0 -left-6 md:left-0 z-10 flex items-center">
                    <button @click="mainPrev" aria-label="prev"
                            class="flex justify-center items-center ml-4 w-10 h-10 focus:outline-none">
                        <svg viewBox="0 0 20 20" fill="<?= $mainArrowColor ?>" class="chevron-left w-20 h-20">
                            <use href="#chevron-left"></use>
                        </svg>
                    </button>
                </div>
                <?php endif; ?>
                <div class="swiper-main-container mb-6 md:mb-0 swiper-container" x-ref="container_<?= $sliderId ?>">
                    <div class="swiper-wrapper items-center flex">
                        <?php foreach ($galleryImages as $id => $image) :
                            $imageFile = $image["url"];
                            $imgLabel = $image["label"];
                            ?>
                            <div class="swiper-slide flex flex-col text-center overflow-hidden" @click="openLightbox" <?= $image['additional_attributes'] ?? ""?>
                            >
                                <?php if ($id == 0): ?>
                                    <?= $imageTag->getImageTag([
                                            "class" => array_key_exists('class', $image) ? $image['class'] : "flex-shrink-0",
                                    "lazyload" => $loadMainLazy,
                                    "fetchpriority" => !$loadMainLazy ? "high" : "",
                                    "src" => $imageFile,
                                    "title" => $imgLabel,
                                    "alt" => $imgLabel,
                                    "style" => $image['style'] ?? "",
                                    "resize" => array_key_exists('resize', $image) ? $image['resize']:false,
                                    "width" => array_key_exists('width', $image) ? $image['width'] : 780,
                                    "height" => array_key_exists('height', $image) ? $image['height'] : 561,
                                    "responsive" => array_key_exists('responsive', $image) ? $image['responsive'] : true
                                    ]) ?>
                                <?php else: ?>
                                    <?= $imageTag->getImageTag(["class" => array_key_exists('class', $image) ? $image['class'] : "flex-shrink-0",
                                                                "lazyload" => true,
                                                                "src" => $imageFile,
                                                                "title" => $imgLabel,
                                                                "alt" => $imgLabel,
                                                                "style" => $image['style'] ?? "",
                                                                "additional_attributes" => "x-cloak",
                                                                "resize" => array_key_exists('resize', $image) ? $image['resize']:false,
                                                                "width" => array_key_exists('width', $image) ? $image['width'] : 780,
                                                                "height" => array_key_exists('height', $image) ? $image['height'] : 561,
                                                                "responsive" => array_key_exists('responsive', $image) ? $image['responsive'] : true
                                    ]) ?>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <?php if($pagination || $thumbs) : ?>
                    <div class="swiper-pagination <?= $thumbs && !$pagination ? "md:hidden" : "" ?>"></div>
                    <?php endif; ?>
                    <?php if($block->getAfterMain()) : ?>
                        <?= $block->getAfterMain(); ?>
                    <?php endif; ?>
                </div>
                <?php if($enabled): ?>
                <div class="absolute inset-y-0 -right-6 md:right-0 mr-4 z-10 flex items-center">
                    <button @click="mainNext" aria-label="next"
                            class="flex justify-center items-center mr-4 w-10 h-10 focus:outline-none">
                        <svg viewBox="0 0 20 20" fill="<?= $mainArrowColor ?>" class="chevron-right w-20 h-20">
                            <use href="#chevron-right"></use>
                        </svg>
                    </button>
                </div>
                <?php endif; ?>
            </div>
            <?php if ($thumbs): ?>
                <div class="swiper-thumbnail hidden md:block relative w-full md:<?= $width ?> mx-auto">
                    <?php if($itemsCount > $numberThumbs) : ?>
                        <div class="absolute inset-y-0 left-0 md:left-2 z-0 flex items-center">
                        <button @click="thumbPrev" aria-label="prev"
                                class="-ml-2 lg:-ml-4 flex justify-center items-center w-10 h-10 focus:outline-none">
                            <svg viewBox="0 0 20 20" fill="currentColor" class="chevron-left w-6 h-6">
                                <use href="#chevron-left"></use>
                            </svg>
                        </button>
                    </div>
                    <?php endif; ?>
                    <div class="swiper-thumbnail-container swiper-container<?= $itemsCount > $numberThumbs ? " mx-8":""?> py-2 px-1 overflow-hidden" x-ref="container_<?= $sliderId ?>_thumb">
                        <div class="swiper-wrapper items-center flex">
                            <?php foreach ($galleryImages as $id => $image) :
                                $imageFile = $image["thumb"] ?? $image["url"];
                                $imgLabel = $image["label_thumbnail"] ?? $image["label"] ?? "";
                                ?>
                                <div class="swiper-slide flex flex-col text-center overflow-hidden p-0.5<?= $id < $swiper->getNumberThumbs()?" w-1/".$swiper->getNumberThumbs():"" ?>">
                                    <?php if($id < $swiper->getNumberThumbs()) : ?>
                                        <?= $imageTag->getImageTag(["class" => "lex-shrink-0",
                                                                    "lazyload" => true,
                                                                    "src" => $imageFile,
                                                                    "title" => !isset($image['use_label']) || $image['use_label'] ? $imgLabel : "",
                                                                    "alt" => $imgLabel,
                                                                    "resize" => false,
                                                                    "width" => 125,
                                                                    "height" => 95
                                        ]) ?>
                                    <?php else: ?>
                                        <?= $imageTag->getImageTag(["class" => "lex-shrink-0",
                                                                    "lazyload" => true,
                                                                    "src" => $imageFile,
                                                                    "title" => !isset($image['use_label']) || $image['use_label'] ? $imgLabel : "",
                                                                    "alt" => $imgLabel,
                                                                    "resize" => false,
                                                                    "width" => 125,
                                                                    "height" => 95,
                                                                    "additional_attributes" => "x-cloak"
                                        ]) ?>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php if($itemsCount > $numberThumbs) : ?>
                    <div class="absolute inset-y-0 right-0 md:right-2 z-0 flex items-center">
                        <button @click="thumbNext" aria-label="next"
                                class="-mr-2 lg:-mr-4 flex justify-center items-center w-10 h-10 focus:outline-none">
                            <svg viewBox="0 0 20 20" fill="currentColor" class="chevron-right w-6 h-6">
                                <use href="#chevron-right"></use>
                            </svg>
                        </button>
                    </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            <?php if ($lightbox): ?>
                <div class="swiper-lightbox lightbox p-0 fixed flex flex-col h-screen w-screen justify-center items-center inset-0 bg-black bg-opacity-90 z-50 transition" x-transition x-cloak x-show="lightboxOpen !== false" style="display: none;">
                    <div class="mx-auto relative max-width w-full swiper ">
                        <div class="swiper-lightbox-container swiper-container flex flex-col" x-ref="container_<?= $sliderId ?>_lightbox">
                            <div class="swiper-wrapper items-center">
                                <?php foreach ($galleryImages as $id => $image) :
                                    $imageFile = $image["lightbox"] ?? $image["url"];
                                    $imgLabel = $image["label"];
                                    ?>
                                    <div class="swiper-slide flex flex-col text-center overflow-hidden">
                                        <img class="flex-shrink-1" loading="lazy" style="max-height: 90vh; max-width: 100vw;" src="<?= $imageFile; ?>" title="<?= $imgLabel; ?>" alt="<?= $imgLabel; ?>"/>
                                    </div>
                                <?php endforeach; ?>
<!--                                <div class="swiper-lazy-preloader swiper-lazy-preloader-white"></div>-->
                            </div>
                            <div class="flex flex-row flex-nowrap mx-auto justify-center pt-4 w-full md:w-1/2 relative">
                                <div class="flex flex-row flex-nowrap w-5/6 md:w-full items-center justify-between<?= count($galleryImages) < 2 ? " hidden": ""?>">
                                    <div class="flex items-center">
                                        <button @click="lightboxPrev" aria-label="prev"
                                                class="-ml-2 lg:-ml-4 flex justify-center items-center w-10 h-10 focus:outline-none">
                                            <svg viewBox="0 0 20 20" fill="white" class="chevron-left w-10 h-10">
                                                <use href="#chevron-left"></use>
                                            </svg>
                                        </button>
                                    </div>
                                    <div class="swiper-pagination"></div>
                                    <div class="flex items-center">
                                        <button @click="lightboxNext" aria-label="next"
                                                class="-mr-2 lg:-mr-4 flex justify-center items-center w-10 h-10 focus:outline-none">
                                            <svg viewBox="0 0 20 20" fill="white" class="chevron-right w-10 h-10">
                                                <use href="#chevron-right"></use>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div class="fixed top-2 right-2 z-10">
                                    <button @click="closeLightbox" aria-label="close" class="float-left outline-none focus:outline-none">
                                        <svg class="fill-current text-white w-8 h-8" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18">
                                            <path d="M14.53 4.53l-1.06-1.06L9 7.94 4.53 3.47 3.47 4.53 7.94 9l-4.47 4.47 1.06 1.06L9 10.06l4.47 4.47 1.06-1.06L10.06 9z">
                                            </path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        <script>
            function initSwiper<?= $sliderId ?>() {
                return Object.assign( {
                    swiper: null,
                    thumb: null,
                    lightbox: null,
                    lightboxOpen: false,
                    initialized: false,
                    init() {
                        !this.initialized && swiperInit.init( () => {
                            Object.assign(this, Object.assign({}, window.swiperHelper, this));
                            <?php if($thumbs): ?>
                            this.thumb = new Swiper(this.$refs.container_<?= $sliderId ?>_thumb, Object.assign({}, this.baseConfig,
                                {
                                    slidesPerView: <?= $numberThumbs ?>,
                                    loop: <?= count($galleryImages) >= $swiper->getNumberThumbs() ? "true" : "false" ?>,
                                    enabled: <?= count($galleryImages) > 1 ? "true" : "false";?>,
                                }
                            ));
                            <?php endif; ?>
                            this.swiper = new Swiper(this.$refs.container_<?= $sliderId ?>, Object.assign({}, this.baseConfig,
                                {
                                    slidesPerView: <?= $slidesPerView ?>,
                                    <?php if($thumbs): ?>
                                    thumbs: {swiper: this.thumb, multipleActiveThumbs: false},
                                    <?php endif; ?>
                                    pagination: {
                                        el: '.swiper-main .swiper-pagination',
                                        dynamicBullets: true,
                                        dynamicMainBullets: <?= min(5,count($galleryImages)) ?>,
                                        clickable: true
                                    },
                                    enabled: <?= count($galleryImages) > 1 ? "true" : "false";?>,
                                },
                                <?= $block->getExtraConfig() ?: "{}"?>
                            ));
                            <?php if($lightbox): ?>
                            this.lightbox = new Swiper(this.$refs.container_<?= $sliderId ?>_lightbox, Object.assign({}, this.baseConfig,
                                {
                                    slidesPerView: 1,
                                    init: false,
                                    pagination: {
                                        el: '.swiper-lightbox .swiper-pagination',
                                        dynamicBullets: true,
                                        dynamicMainBullets: <?= min(5,count($galleryImages)) ?>,
                                        clickable: true
                                    },
                                    enabled: <?= count($galleryImages) > 1 ? "true" : "false";?>,
                                },
                                <?= $block->getLightboxExtraConfig() ?: "{}"?>
                            ));
                            <?php endif; ?>
                            if(typeof this.additionalInit === 'function')  this.additionalInit();
                            this.initialized = true;
                        });
                    },
                    eventListeners: {
                        ['@keydown.window.escape']() {
                            this.closeLightbox();
                        }
                    }
                }, <?= $block->getInitScript() ?: "{}"?>);
            }
        </script>
    <?php else: ?>
        <span class="no-available-data hidden"></span>
    <?php endif; ?>
</div>
<?php
$swiper->resetBlock($block);
?>
