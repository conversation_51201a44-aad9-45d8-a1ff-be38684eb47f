<?php
declare(strict_types=1);

namespace CopeX\ProductInheritanceXortexTabs\Model\Processor;

use CopeX\ProductInheritance\Api\InheritanceProcessorInterface;
use CopeX\ProductInheritance\Model\Config;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Framework\Module\Manager as ModuleManager;
use Psr\Log\LoggerInterface;
use Xortex\Xproducttabs\Model\ResourceModel\Product as TabsResource;

class TabsProcessor implements InheritanceProcessorInterface
{
    private const XML_PATH_ENABLE_XORTEX_TABS_PROCESSOR = 'copex_product_inheritance/processors/enable_xortex_tabs_processor';

    public function __construct(
        private readonly Config $config,
        private readonly ModuleManager $moduleManager,
        private readonly LoggerInterface $logger,
        private readonly TabsResource $tabsResource
    ) {
    }

    /**
     * Process
     */
    public function process(ProductInterface $targetProduct, ProductInterface $sourceProduct): bool
    {
        if (! $this->isEnabled()) {
            return false;
        }
        try {
            $tabs = $this->tabsResource->getBlocksByProduct($sourceProduct->getId());
            $tabs = array_map(static function ($tab) {
                return [
                    'id' => $tab['block_id'],
                    'position' => $tab['position'],
                ];
            }, $tabs);
            $this->tabsResource->saveBlockRelation($targetProduct->getId(), $tabs);
            return true;
        } catch (\Exception $e) {
            $this->logger->error(
                'Error processing tabs inheritance: ' . $e->getMessage(),
                ['target_product_id' => $targetProduct->getId(), 'source_product_id' => $sourceProduct->getId()]
            );
            return false;
        }
    }

    /**
     * Get processor name
     */
    public function getName(): string
    {
        return 'Tabs (Xortex) Processor';
    }

    /**
     * Check if processor is enabled
     */
    public function isEnabled(): bool
    {
        return $this->config->isEnabled() &&
               $this->config->getConfigValue(self::XML_PATH_ENABLE_XORTEX_TABS_PROCESSOR);
    }
}
