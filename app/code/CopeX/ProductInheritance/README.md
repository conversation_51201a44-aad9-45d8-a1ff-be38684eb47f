# CopeX Product Inheritance Module

## Overview

The CopeX Product Inheritance Module enables automatic synchronization of product attributes based on SKU references. This is particularly useful for configurable products where simple products should inherit attributes from their parent product.

## Features

- **Inherit From SKU Attribute**: New product attribute to specify the source SKU
- **Configurable Attributes**: Backend configuration to select which attributes to synchronize
- **Automatic Synchronization**: Automatic synchronization when saving products
- **Extensible Architecture**: Plugin system for additional synchronizations
- **Bulk Synchronization**: Console command for mass processing
- **Logging**: Complete tracking of all operations

## Installation

1. Place module in `app/code/CopeX/ProductInheritance/`
2. Run Magento setup:
   ```bash
   php bin/magento setup:upgrade
   php bin/magento setup:di:compile
   php bin/magento cache:flush
   ```

## Configuration

### Backend Configuration
Navigate to: **Stores > Configuration > Catalog > Product Inheritance**

#### General Settings
- **Enable Product Inheritance**: Enable/disable the module
- **Auto Sync on Product Save**: Automatic synchronization when saving
- **Enable Logging**: Enable logging for debugging

#### Attribute Configuration
- **Attributes to Inherit**: Select which attributes to synchronize
- **Skip Empty Values**: Skip empty values
- **Overwrite Existing Values**: Overwrite existing values

#### Additional Processors
- **Enable Image Inheritance**: Copy product images
- **Enable Attachment Inheritance**: Copy product attachments (Amasty)

## Usage

### 1. Set Product Attribute
In the product edit form, fill the "Inherit From SKU" field with the desired source SKU.

### 2. Automatic Synchronization
With auto-synchronization enabled, attributes are automatically inherited when saving.

### 3. Manual Synchronization
```bash
# Synchronize all products with inherit_from_sku
php bin/magento copex:product-inheritance:sync

# Synchronize specific product IDs
php bin/magento copex:product-inheritance:sync --product-ids=1,2,3


# With batch size
php bin/magento copex:product-inheritance:sync --batch-size=50
```

## Erweiterung

### Eigene Processor erstellen

1. Interface implementieren:
```php
<?php
namespace YourNamespace\YourModule\Model\Processor;

use CopeX\ProductInheritance\Api\InheritanceProcessorInterface;
use Magento\Catalog\Api\Data\ProductInterface;

class CustomProcessor implements InheritanceProcessorInterface
{
    public function process(ProductInterface $targetProduct, ProductInterface $sourceProduct): bool
    {
        // Ihre Logik hier
        return true;
    }

    public function getName(): string
    {
        return 'Custom Processor';
    }

    public function isEnabled(): bool
    {
        return true; // Oder Konfigurationswert
    }
}
```

2. In di.xml registrieren:
```xml
<type name="CopeX\ProductInheritance\Service\ProductInheritanceService">
    <arguments>
        <argument name="processors" xsi:type="array">
            <item name="custom_processor" xsi:type="object">YourNamespace\YourModule\Model\Processor\CustomProcessor</item>
        </argument>
    </arguments>
</type>
```

## Beispiel-Workflow

1. **Konfigurierbares Produkt**: SKU "SHIRT-PARENT"
2. **Simple Product 1**: SKU "SHIRT-RED-S", inherit_from_sku = "SHIRT-PARENT"
3. **Simple Product 2**: SKU "SHIRT-BLUE-M", inherit_from_sku = "SHIRT-PARENT"

Beim Speichern der Simple Products werden automatisch die konfigurierten Attribute vom Parent Product übernommen.


## Technische Details

- **Magento Version**: 2.4.x
- **PHP Version**: 8.1+
- **Abhängigkeiten**: Magento_Catalog, Magento_Eav, Magento_Backend

## Support

Bei Fragen oder Problemen wenden Sie sich an das CopeX Team.
