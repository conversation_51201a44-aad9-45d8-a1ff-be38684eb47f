<?php
declare(strict_types=1);

namespace CopeX\AllTopProductOptions\Plugin\Magento\Sales\Block\Adminhtml\Items\Column;

use CopeX\AllTopProductOptions\Helper\Options;

class DefaultColumn
{
    /** @var Options */
    private $optionsHelper;

    /**
     * @param Options $optionsHelper
     */
    public function __construct(Options $optionsHelper)
    {
        $this->optionsHelper = $optionsHelper;
    }

    public function afterGetOrderOptions(
        \Magento\Sales\Block\Adminhtml\Items\Column\DefaultColumn $subject,
                                                                  $options
    ) {
        return $this->optionsHelper->updateOptionsText($options);
    }
}
