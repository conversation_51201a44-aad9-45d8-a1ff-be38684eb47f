<?php

namespace CopeX\AllTopProductOptions\Plugin;

use CopeX\ProductDetailLazyLoad\Controller\Product\Get;
use Magento\Framework\View\LayoutInterface;
use Yireo\NextGenImages\Config\Config;
use Yireo\NextGenImages\Util\HtmlReplacer;

class AddNextgenImages
{

    /**
     * @var HtmlReplacer
     */
    private $htmlReplacer;

    /**
     * @var Config
     */
    private $config;

    /**
     * ReplaceTags constructor.
     *
     * @param HtmlReplacer $htmlReplacer
     * @param Config $config
     */
    public function __construct(
        HtmlReplacer $htmlReplacer,
        Config $config
    ) {
        $this->htmlReplacer = $htmlReplacer;
        $this->config = $config;
    }

    /**
     * @param Get $subject
     * @param string $result
     * @param LayoutInterface $layout
     * @return string
     */
    public function afterGetResponseContent(Get $subject, $result, LayoutInterface $layout): string
    {
        /** @var $result \Magento\Framework\App\Response\Http; */
        if ($result && $this->config->enabled()) {
            return $this->htmlReplacer->replace((string)$result);
        }
        return $result;
    }
}
