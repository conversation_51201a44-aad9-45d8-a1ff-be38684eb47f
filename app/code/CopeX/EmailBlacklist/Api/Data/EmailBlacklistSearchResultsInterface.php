<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace CopeX\EmailBlacklist\Api\Data;

interface EmailBlacklistSearchResultsInterface extends \Magento\Framework\Api\SearchResultsInterface
{

    /**
     * Get EmailBlacklist list.
     * @return \CopeX\EmailBlacklist\Api\Data\EmailBlacklistInterface[]
     */
    public function getItems();

    /**
     * Set email list.
     * @param \CopeX\EmailBlacklist\Api\Data\EmailBlacklistInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}

