<?php

declare(strict_types=1);

namespace CopeX\TaxInfo\Model\Checkout;

use Magento\Checkout\Model\ConfigProviderInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Tax\Model\Config;

class Config<PERSON>rovider implements ConfigProviderInterface
{
    /**
     * @var ScopeConfigInterface
     */
    private ScopeConfigInterface $scopeConfig;

    /**
     * ConfigProvider constructor.
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        ScopeConfigInterface $scopeConfig
    ) {
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * Add custom config to checkout
     *
     * @return array
     */
    public function getConfig()
    {
        $config = [];
        $config['tax_display_price'] = $this->scopeConfig->getValue(
            Config::XML_PATH_DISPLAY_CART_PRICE, ScopeInterface::SCOPE_STORE
        );

        return $config;
    }
}
