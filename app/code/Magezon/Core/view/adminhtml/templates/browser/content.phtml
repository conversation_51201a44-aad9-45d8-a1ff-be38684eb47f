<?php
 /**
 * @var $block \Magento\Cms\Block\Adminhtml\Wysiwyg\Images\Content
 */
$options                = json_decode($block->getFilebrowserSetupObject(), true);
$options['onInsertUrl'] = str_replace("/cms", "/mgzcore", $options['onInsertUrl']);
$options                = json_encode($options);
?>

<div data-mage-init='{"mgzmediabrowser": <?php echo $block->escapeHtml($options); ?>}'>
    <div class="side-col"><?php echo $block->getChildHtml('wysiwyg_images.tree') ?></div>
    <div class="main-col">
        <div class="insert-title" id="content_header">
            <div class="insert-title-inner">
                <div class="insert-actions">
                    <?php echo $block->getButtonsHtml() ?>
                </div>
                <div class="title"><?php /* @escapeNotVerified */ echo $block->getHeaderText() ?></div>
            </div>
        </div>
        <div id="error-message" data-action="show-error"></div>
        <div id="contents-uploader" class="contents-uploader"><?php echo $block->getChildHtml('wysiwyg_images.uploader') ?></div>
        <div id="contents"></div>
    </div>
</div>
