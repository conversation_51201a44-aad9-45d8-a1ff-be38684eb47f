<?php
/**
 * Copyright (c) 2008-2016 dotSource GmbH.
 * All rights reserved.
 * http://www.dotsource.de
 *
 * Contributors:
 * Sebastian Ninse - initial contents
 */
namespace Payolution\Payments\Model;

use Magento\Framework\DataObject;
use Payolution\Payments\Helper\Data;
use Payolution\Payments\Helper\PreCheck as PrecheckHelper;
use Magento\Framework\App\ObjectManager;

/**
 * Class Instalment
 *
 * @package Payolution\Payments\Model
 */
class Instalment extends AbstractMethod
{
    /**
     * @var String
     */
    protected $_preAuthApiType = 'Payolution\Payments\Model\Api\PreAuth\Instalment';

    /**
     * PreCheck checkout session key
     *
     * @var string
     */
    protected $_precheckSessionKey = PrecheckHelper::PRECHECK_INSTALMENT;

    /**
     * @var array
     */
    protected $_payolutionAdditionalData = [
        'dob',
        'email',
        'calculation_id',
        'duration',
        'account_holder',
        'iban',
    ];

    /**
     * Payment method code
     *
     * @var string
     */
    protected $_code = Data::INSTALMENT_PAYMENT_CODE;

    /**
     * @var string
     */
    protected $_formBlockType = 'Payolution\Payments\Block\Form\Instalment';

    /**
     * @var string
     */
    protected $_infoBlockType = 'Payolution\Payments\Block\Info\Instalment';

    /**
     * Availability option
     *
     * @var bool
     */
    protected $_isOffline = true;

    /**
     * Availability option
     *
     * @var bool
     */
    protected $_isGateway = true;

    /**
     * Availability option
     *
     * @var bool
     */
    protected $_canAuthorize = true;

    /**
     * Availability option
     *
     * @var bool
     */
    protected $_canCapture = true;

    /**
     * Availability option
     *
     * @var bool
     */
    protected $_canRefund = true;

    /**
     * Availability option
     *
     * @var bool
     */
    protected $_canCancelInvoice = true;

    /**
     * Availability option
     *
     * @var bool
     */
    protected $_canVoid = true;

    /**
     * Instalment only available for B2C customer
     *
     * @param \Magento\Quote\Api\Data\CartInterface $quote
     * @return bool
     */
    public function isAvailable(\Magento\Quote\Api\Data\CartInterface $quote = null)
    {
        $isAvailable = parent::isAvailable($quote);
        if ($isAvailable) {
            $helper = ObjectManager::getInstance()->create('Payolution\\Payments\\Helper\\Data');
            $isAvailable = !$helper->isCustomerB2B($quote->getCustomer());
        }
        return $isAvailable;

    }

    /**
     * Retrieve form data for preauth request
     *
     * @return array
     */
    protected function getPaymentFormData()
    {
        return [
            'dob'           => $this->getCustomerDob(),
            'email'         => $this->getCustomerEmail(),
            'iban'          => $this->getIban(),
            'duration'      => $this->getInstalmentDuration(),
            'account_holder' => $this->getAccountHolder(),
            'calculation_id' => $this->getInstalmentCalculationId(),
        ];
    }

    /**
     * Encrypt account holder value
     *
     * @param $value
     * @return string
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function setAccountHolder($value)
    {
        return $this->getInfoInstance()->encrypt($value);
    }

    /**
     * Retrieve account holder
     *
     * @return string
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function getAccountHolder()
    {
        $value = '';
        $data = $this->getInfoInstance()->getAdditionalData();
        if (!empty($data['account_holder'])) {
            $value = (string)$this->getInfoInstance()->decrypt($data['account_holder']);
        }
        return $value;
    }

    /**
     * Encrypt iban value
     *
     * @param $value
     * @return string
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function setIban($value)
    {
        return $this->getInfoInstance()->encrypt($value);
    }

    /**
     * Retrieve iban
     *
     * @return string
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function getIban()
    {
        $value = '';
        $data = $this->getInfoInstance()->getAdditionalData();
        if (!empty($data['iban'])) {
            $value = (string)$this->getInfoInstance()->decrypt($data['iban']);
        }
        return $value;
    }

    /**
     * Retrieve instalment duration
     *
     * @return int|string
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function getInstalmentDuration()
    {
        $data = $this->getInfoInstance()->getAdditionalData();
        return !empty($data['duration']) ? (int)$data['duration'] : '';
    }

    /**
     * Retrieve calculation_id
     *
     * @return int|string
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function getInstalmentCalculationId()
    {
        $data = $this->getInfoInstance()->getAdditionalData();
        return !empty($data['calculation_id']) ? (string)$data['calculation_id'] : '';
    }
}
