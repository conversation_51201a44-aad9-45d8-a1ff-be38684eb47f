<?php
/**
 * Copyright (c) 2008-2016 dotSource GmbH.
 * All rights reserved.
 * http://www.dotsource.de
 *
 * Contributors:
 * Sebastian Ninse <<EMAIL>> - initial contents
 */
namespace Payolution\Payments\Model\Api\PreCheck;

/**
 * Class Invoice
 * @package Payolution\Payments\Model\Api\PreCheck
 */
class Invoice extends \Payolution\Payments\Model\Api\PreCheck
{
    const COMPANY_SOLE_TRADER = 'SOLE';

    /**
     * @var string
     */
    protected $_brand = 'PAYOLUTION_INVOICE';

    /**
     * Set customer b2b data if required
     *
     * @return $this
     */
    protected function _setRequestCustomerData()
    {
        parent::_setRequestCustomerData();
        $xml = $this->_getXMLTemplate();

        if ($this->_helper->isCustomerB2B($this->_customer)) {
            $xml->Transaction->Customer->Company->Name = $this->_getFormData('company');
            if ($vatId = $this->_getFormData('vat_id')) {
                $xml->Transaction->Customer->Company->UID = $vatId;
            }
            if ($regNo = $this->_getFormData('reg_number')) {
                $xml->Transaction->Customer->Company->TradeRegistryNumber = $regNo;
            }
        }

        return $this;
    }

    /**
     * Set b2b criterion data if required
     *
     * @return $this
     */
    protected function _setRequestCriterionData()
    {
        parent::_setRequestCriterionData();
        $xml = $this->_getXMLTemplate();

        //user is either group retailer or has company set -> b2b mode
        if ($this->_helper->isCustomerB2B($this->_customer)
        ) {
            //add default company data to request
            $criterion = $xml->Transaction->Analysis->addChild('Criterion', 'B2B');
            $criterion->addAttribute('name', 'PAYOLUTION_TRX_TYPE');
            if ($companyName = $this->_getFormData('company')) {
                $criterion = $xml->Transaction->Analysis->addChild('Criterion', $companyName);
                $criterion->addAttribute('name', 'PAYOLUTION_COMPANY_NAME');
            }
            if ($vatId = $this->_getFormData('vat_id')) {
                $criterion = $xml->Transaction->Analysis->addChild('Criterion', $vatId);
                $criterion->addAttribute('name', 'PAYOLUTION_COMPANY_TRADEREGISTRY_NUMBER');
            }
            if ($regNo = $this->_getFormData('reg_number')) {
                $criterion = $xml->Transaction->Analysis->addChild('Criterion', $regNo);
                $criterion->addAttribute('name', 'PAYOLUTION_COMPANY_UID');
            }

            //add type of organization to request
            if ($organization = $this->_getFormData('organization')) {
                $criterion = $xml->Transaction->Analysis->addChild('Criterion', $organization);
                $criterion->addAttribute('name', 'PAYOLUTION_COMPANY_TYPE');
            }

            //add sole trader specific company data to request if sole trader is set
            if ($organization === self::COMPANY_SOLE_TRADER) {
                if ($soleCompanyName = $this->_getFormData('company_name')) {
                    $criterion = $xml->Transaction->Analysis->addChild('Criterion', $soleCompanyName);
                    $criterion->addAttribute('name', 'PAYOLUTION_COMPANY_NAME');
                }
                if ($soleOwnerGiven = $this->_getFormData('company_owner_given')) {
                    $criterion = $xml->Transaction->Analysis->addChild('Criterion', $soleOwnerGiven);
                    $criterion->addAttribute('name', 'PAYOLUTION_COMPANY_OWNER_GIVEN');
                }
                if ($soleOwnerFamily = $this->_getFormData('company_owner_family')) {
                    $criterion = $xml->Transaction->Analysis->addChild('Criterion', $soleOwnerFamily);
                    $criterion->addAttribute('name', 'PAYOLUTION_COMPANY_OWNER_FAMILY');
                }
                if ($soleOwnerBirthdate = $this->_getFormData('dob')) {
                    $criterion = $xml->Transaction->Analysis->addChild('Criterion', $soleOwnerBirthdate);
                    $criterion->addAttribute('name', 'PAYOLUTION_COMPANY_OWNER_BIRTHDATE');
                }
            }
        }

        return $this;
    }
}
