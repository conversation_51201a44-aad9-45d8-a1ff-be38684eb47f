<?php
/**
 * Copyright (c) 2008-2016 dotSource GmbH.
 * All rights reserved.
 * http://www.dotsource.de
 *
 * Contributors:
 * Sebastian Ninse - initial contents
 */
namespace Payolution\Payments\Test\Integration\Model;

/**
 * Class PayolutionElvRefundTest
 * @package Payolution\Payments\Test\Integration\Model
 */
class PayolutionElvRefundTest extends PayolutionElvTestCase
{
    /**
     * @var string
     */
    protected $_refundApiType   = 'Payolution\Payments\Model\Api\Refund\Elv';

    /**
     * Test perform complete Instalment Order with Capture and Refund again
     */
    public function testRefund()
    {
        $this->performCapture();
        foreach ($this->_captureIds[$this->_methodName] as $preAuthId) {
            $this->_preAuthOrder->setPayolutionUniqueId($preAuthId);

            $refundApi = $this->_apiFactory->create(
                $this->_refundApiType,
                [
                    'order'     => $this->_preAuthOrder,
                    'amount'    => $this->_preAuthOrder->getGrandTotal(),
                ]
            )->setPwdLogin(self::TEST_PWD)->run();

            $this->assertTrue(
                $refundApi->isSuccess(),
                'Refund request ' .  $this->_methodName. ' did not return as "ACK".'
            );
        }
    }
}
