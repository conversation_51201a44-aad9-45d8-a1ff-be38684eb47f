<?php

/**
 * Copyright (c) 2008-2016 dotSource GmbH
 * All rights reserved.
 * http://www.dotsource.de
 *
 * Contributors:
 * Stefan Jauck
 */

namespace Payolution\Payments\Test\Integration\Model;

/**
 * Class PayolutionInvoiceTest
 * @package Payolution\Payments\Test\Integration\Model
 */
class PayolutionInvoiceTest extends PayolutionAbstractApiTest
{
    /**
     * @var string
     */
    protected $_preCheckApiType = 'Payolution\Payments\Model\Api\PreCheck\Invoice';

    /**
     * @var String
     */
    protected $_preAuthApiType = 'Payolution\Payments\Model\Api\PreAuth\Invoice';

    /**
     * @var string
     */
    protected $_methodName = \Payolution\Payments\Helper\Data::INVOICE_PAYMENT_CODE;

    /**
     * {@inheritdoc}
     */
    public function setUp()
    {
        parent::setUp();
        $this->_formDataPreCheck = array(
            'dob'           => '1978-10-01',
            'email'         => '<EMAIL>',
            'company'       => 'TestCompany',
            'vat_id'        => '*********',
            'reg_nummer'    => 'gh789'
        );

        $this->_formDataPreAuth = array(
            'dob'           => '1978-10-01',
            'email'         => '<EMAIL>',
            'company'       => 'TestCompany',
            'vat_id'        => '*********',
            'reg_nummer'    => 'gh789'
        );
    }
}
