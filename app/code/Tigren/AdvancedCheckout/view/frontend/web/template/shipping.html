<!--
	/**
	 * <AUTHOR> Team
	 * @copyright Copyright (c) 2015 Tigren (https://www.tigren.com)
	 * @package Tigren_AdvancedCheckout
	 */
	-->
<li id="shipping" class="checkout-shipping-address shipping-address-container" data-bind="fadeVisible: visible()">
    <div class="step-title shipping-address-title" translate="'Shipping Address '" data-role="title"></div>
    <div id="checkout-step-shipping"
         class="step-content"
         data-role="content">

        <each if="!quoteIsVirtual" args="getRegion('customer-email')" render=""></each>
        <each args="getRegion('address-list')" render=""></each>
        <each args="getRegion('address-list-additional-addresses')" render=""></each>

        <!-- Address form pop up -->
        <if args="!isFormInline">
            <button type="button"
                    class="opc-new-shipping-address-button themeicon-plus action-show-popup"
                    click="showFormPopUp"
                    visible="!isNewAddressAdded()">
                <span translate="'New Address'"></span>
            </button>
            <div id="opc-new-shipping-address"
                 visible="isFormPopUpVisible()"
                 render="shippingFormTemplate"></div>
        </if>

        <each args="getRegion('before-form')" render=""></each>

        <!-- Inline address form -->
        <render if="isFormInline" args="shippingFormTemplate"></render>
    </div>
</li>

<!--Shipping method template-->
<li id="opc-shipping_method"
    class="checkout-shipping-method shipping-method-container"
    data-bind="fadeVisible: visible(), blockLoader: isLoading"
    role="presentation">

    <div id="billing-address-container">
        <div class="step-title billing-address-title" translate="'Billing Address'" data-role="title"></div>
        <div class="billing-address-same-as-shipping-block field choice">
            <input type="checkbox" name="billing-address-same-as-shipping"
                   data-bind="checked: isAddressSameAsShipping, click: useShippingAddress, attr: {id: 'billing-address-same-as-shipping-shared'}"/>
            <label data-bind="attr: {for: 'billing-address-same-as-shipping-shared'}"><span
                data-bind="i18n: 'Gleiche Liefer- und Rechnungsadresse'"></span></label>
            <span data-bind="css: {hidden: isAddressSameAsShipping}" class="payolution-payment-addinfotext"><!-- ko i18n: 'INVOICE IS ONLY AVAILABLE WITH IDENTICAL BILLING AND SHIPPING ADDRESS.' --><!-- /ko --></span>
        </div>
        <div class="form-billing-address billing-form-container" data-bind="visible: isShowBillingForm">
            <each args="getRegion('billing-address')" render=""></each>
        </div>
    </div>

    <div class="checkout-shipping-method">
        <div class="step-title"
             translate="'Shipping Methods'"
             data-role="title"></div>

        <each args="getRegion('before-shipping-method-form')" render=""></each>

        <div id="checkout-step-shipping_method"
             class="step-content shipping-form-container"
             data-role="content"
             role="tabpanel"
             aria-hidden="false">
            <form id="co-shipping-method-form"
                  class="form methods-shipping"
                  if="rates().length"
                  submit="setShippingInformation"
                  novalidate="novalidate">

                <render args="shippingMethodListTemplate"></render>

                <div id="onepage-checkout-shipping-method-additional-load">
                    <each args="getRegion('shippingAdditional')" render=""></each>
                </div>
                <div role="alert"
                     if="errorValidationMessage().length"
                     class="message notice">
                    <span text="errorValidationMessage()"></span>
                </div>
                <div class="actions-toolbar" id="shipping-method-buttons-container">
                    <div class="primary shipping-button-container">
                        <button data-role="opc-continue" type="submit" class="button action opc-continue-button">
                            <span translate="'Weiter zu Zahlung & Versand'"></span>
                        </button>
                    </div>
                </div>
            </form>
            <div class="no-quotes-block"
                 ifnot="rates().length > 0"
                 translate="'Sorry, no quotes are available for this order at this time'"></div>
        </div>
    </div>
</li>
