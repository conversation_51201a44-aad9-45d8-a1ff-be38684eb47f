<?php declare(strict_types=1);

namespace Hyva\MagentoCoreFixes\Observer;

use Hyva\GraphqlViewModel\Model\GraphqlQueryEditor;
use Magento\Framework\Event\Observer as Event;
use Magento\Framework\Event\ObserverInterface;

class ConfigCustomizableProductsInCartQuery implements ObserverInterface
{
    /**
     * @var GraphqlQueryEditor
     */
    private $graphqlQueryEditor;

    public function __construct(GraphqlQueryEditor $graphqlQueryEditor)
    {
        $this->graphqlQueryEditor = $graphqlQueryEditor;
    }

    public function execute(Event $event)
    {
        $queryString = $event->getData('gql_container')->getData('query');

        $queryString = $this->graphqlQueryEditor->addFieldIn(
            $queryString,
            ['cart', 'items', '... on ConfigurableCartItem', 'customizable_options'],
            'label'
        );

        $queryString = $this->graphqlQueryEditor->addFieldIn(
            $queryString,
            ['cart', 'items', '... on ConfigurableCartItem', 'customizable_options', 'values'],
            'label value'
        );

        $queryString = $this->graphqlQueryEditor->addFieldIn(
            $queryString,
            ['cart', 'items', '... on ConfigurableCartItem', 'customizable_options', 'values', 'price'],
            'value type'
        );

        $event->getData('gql_container')->setData('query', $queryString);
    }
}
