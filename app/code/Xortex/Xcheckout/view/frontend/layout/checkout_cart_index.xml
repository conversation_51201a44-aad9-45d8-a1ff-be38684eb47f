<?xml version="1.0"?>
<!--
/**
 * Copyright © 2013-2017 Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="checkout_cart_item_renderers"/>
    <body>

        <referenceContainer name="checkout.cart.items">
            <container name="checkout.cart.container.wrapper" htmlTag="div" htmlClass="cart-container-wrapper"
                       before="-"/>
        </referenceContainer>

        <move element="checkout.cart.container" destination="checkout.cart.container.wrapper"/>

        <referenceContainer name="checkout.cart.container">
            <container name="checkout.cart.shipping.additional" label="Delivery Date and Comment" htmlTag="div"
                       htmlClass="checkout-shipping-additional" after="checkout.cart.form">
                    <block class="Xortex\Xcheckout\Block\Cart\Delivery" name="xcheckout.cart.delivery"
                           template="Xortex_Xcheckout::cart/delivery.phtml" cacheable="false" after="-">
                        <arguments>
                            <argument name="jsLayout" xsi:type="array">
                                <item name="components" xsi:type="array">
                                    <item name="cartAdditional" xsi:type="array">
                                        <item name="component" xsi:type="string">uiComponent</item>
                                        <item name="displayArea" xsi:type="string">cartAdditional</item>
                                        <item name="children" xsi:type="array">
                                            <item name="delivery_date" xsi:type="array">
                                                <item name="component" xsi:type="string">
                                                    Xortex_Xcheckout/js/view/delivery-date-block
                                                </item>
                                            </item>
                                            <item name="delivery_comment" xsi:type="array">
                                                <item name="component" xsi:type="string">
                                                    Xortex_Xcheckout/js/view/delivery-comment-block
                                                </item>
                                            </item>
                                        </item>
                                    </item>
                                </item>
                            </argument>
                        </arguments>
                    </block>
            </container>
        </referenceContainer>
    </body>
</page>