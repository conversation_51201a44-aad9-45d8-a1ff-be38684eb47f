<?php
namespace Xortex\Xcheckout\Model;

use Magento\Checkout\Model\ConfigProviderInterface;
use Magento\Cms\Block\Block;

class BillingInfoConfigProvider implements ConfigProviderInterface
{

    /**
     * Billing Info CMS Block Identifier
     */
    const BILLING_INFO_CMS_BLOCK_ID = "xcheckout_billing_info";

    /**
     * @var Block
     */
    private $cmsBlock;

    /**
     * @param Block $cmsBlock
     */
    public function __construct(
        Block $cmsBlock
    ) {
        $this->cmsBlock = $cmsBlock;
    }

    /**
     * {@inheritdoc}
     */
    public function getConfig()
    {

        $cmsBlockId = self::BILLING_INFO_CMS_BLOCK_ID;
        $cmsBlockHtml = "";
        if($cmsBlockId) {
          $block = $this->cmsBlock->setBlockId($cmsBlockId);
          $cmsBlockHtml = $block->toHtml();
        }

        $config = [
            'billing' => [
                'infoBlockHtml' => $cmsBlockHtml,
            ]
        ];
        return $config;
    }
}