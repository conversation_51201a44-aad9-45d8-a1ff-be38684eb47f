<?php
namespace Xortex\Xcheckout\Model;

use Magento\Framework\Model\AbstractExtensibleModel;
use Xortex\Xcheckout\Api\Data\CartInformationInterface;

/**
 * @codeCoverageIgnoreStart
 */
class CartInformation extends AbstractExtensibleModel implements CartInformationInterface
{
    /**
     * {@inheritdoc}
     */
    public function getDeliveryDate(): string
    {
        return $this->getData(self::DELIVERY_DATE);
    }

    /**
     * {@inheritdoc}
     */
    public function setDeliveryDate(string $date)
    {
        return $this->setData(self::DELIVERY_DATE, $date);
    }

    /**
     * {@inheritdoc}
     */
    public function getDeliveryComment(): string
    {
        return $this->getData(self::DELIVERY_COMMENT);
    }

    /**
     * {@inheritdoc}
     */
    public function setDeliveryComment(string $comment)
    {
        return $this->setData(self::DELIVERY_COMMENT, $comment);
    }

    /**
     * {@inheritdoc}
     */
    public function getExtensionAttributes()
    {
        return $this->_getExtensionAttributes();
    }

    /**
     * {@inheritdoc}
     */
    public function setExtensionAttributes(
        \Magento\Checkout\Api\Data\ShippingInformationExtensionInterface $extensionAttributes
    ) {
        return $this->_setExtensionAttributes($extensionAttributes);
    }
}
