<?php
/**
 * <AUTHOR>
 * @copyright Copyright (c) 2017 Xortex (https://www.xortex.com)
 * @package Xortex_Xproducttabs
 */

namespace Xortex\Xproducttabs\Ui\DataProvider\Product\Form\Modifier;

use Magento\Catalog\Model\Locator\LocatorInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Store\Api\WebsiteRepositoryInterface;
use Magento\Store\Api\GroupRepositoryInterface;
use Magento\Store\Api\StoreRepositoryInterface;
use Magento\Framework\Phrase;
use Magento\Framework\UrlInterface;
use Magento\Ui\Component\DynamicRows;
use Magento\Ui\Component\Form\Element\DataType\Number;
use Magento\Ui\Component\Form\Element\DataType\Text;
use Magento\Ui\Component\Form\Element\Input;
use Magento\Ui\Component\Form\Field;
use Magento\Ui\Component\Form\Fieldset;
use Magento\Ui\Component\Modal;
use Magento\Catalog\Model\Product\Attribute\Source\Status;
use Magento\Catalog\Ui\DataProvider\Product\Form\Modifier\AbstractModifier;

class Block extends AbstractModifier
{

    const DATA_SCOPE = '';
    const DATA_SCOPE_BLOCK = 'product_block';
    const GROUP_BLOCK = 'product_block';

    /**
     * @var string
     */
    private static $previousGroup = 'search-engine-optimization';

    /**
     * @var int
     */
    private static $sortOrder = 400;

    /**
     * @var LocatorInterface
     */
    public $locator;

    /**
     * @var WebsiteRepositoryInterface
     */
    public $websiteRepository;

    /**
     * @var GroupRepositoryInterface
     */
    public $groupRepository;

    /**
     * @var StoreRepositoryInterface
     */
    public $storeRepository;

    /**
     * @var StoreManagerInterface
     */
    public $storeManager;

    /**
     * @var string
     */
    public $scopeName;
    /**
     * @var string
     */
    public $scopePrefix;

    /**
     * @var UrlInterface
     */
    public $urlBuilder;

    /**
     * @var \Xortex\Xproducttabs\Model\ResourceModel\Product\CollectionFactory
     */
    public $productBlockCollection;

    /**
     * @var \Magento\Cms\Model\ResourceModel\Block\CollectionFactory
     */
    public $cmsBlockCollection;

    /**
     * @var string
     */
    private $dataScopeName;

    /**
     * @var Status
     */
    protected $status;

    /**
     * Block constructor.
     * @param LocatorInterface $locator
     * @param UrlInterface $urlBuilder
     * @param StoreManagerInterface $storeManager
     * @param WebsiteRepositoryInterface $websiteRepository
     * @param GroupRepositoryInterface $groupRepository
     * @param StoreRepositoryInterface $storeRepository
     * @param \Xortex\Xproducttabs\Model\ResourceModel\Product\CollectionFactory $productBlockCollection
     * @param \Magento\Cms\Model\ResourceModel\Block\CollectionFactory $cmsBlockCollection
     * @param Status $status
     * @param $dataScopeName
     * @param string $scopeName
     * @param string $scopePrefix
     */
    public function __construct(
        LocatorInterface $locator,
        UrlInterface $urlBuilder,
        StoreManagerInterface $storeManager,
        WebsiteRepositoryInterface $websiteRepository,
        GroupRepositoryInterface $groupRepository,
        StoreRepositoryInterface $storeRepository,
        \Xortex\Xproducttabs\Model\ResourceModel\Product\CollectionFactory $productBlockCollection,
        \Magento\Cms\Model\ResourceModel\Block\CollectionFactory $cmsBlockCollection,
        Status $status,
        $dataScopeName,
        $scopeName = '',
        $scopePrefix = ''
    )
    {
        $this->locator = $locator;
        $this->storeManager = $storeManager;
        $this->urlBuilder = $urlBuilder;
        $this->websiteRepository = $websiteRepository;
        $this->groupRepository = $groupRepository;
        $this->storeRepository = $storeRepository;
        $this->dataScopeName = $dataScopeName;
        $this->scopeName = $scopeName;
        $this->productBlockCollection = $productBlockCollection;
        $this->cmsBlockCollection = $cmsBlockCollection;
        $this->scopePrefix = $scopePrefix;
        $this->status = $status;
    }

    /**
     * @param array $data
     * @return array
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function modifyData(array $data)
    {
        /** @var \Magento\Catalog\Model\Product $product */
        $product = $this->locator->getProduct();
        $productId = $product->getId();

        if (!$productId) {
            return $data;
        }

        foreach ($this->getDataScopes() as $dataScope) {
            $data[$productId]['links'][$dataScope] = [];
            $productBlockCollection = $this->productBlockCollection->create()
                ->addFieldToFilter('product_id', ['eq' => $productId]);
            foreach ($productBlockCollection as $linkedBlock) {
                if ($this->fillData($linkedBlock) != '') {
                    $data[$productId]['links'][$dataScope][] = $this->fillData($linkedBlock);
                }
            }
        }
        return $data;
    }

    /**
     * @param \Magento\Framework\DataObject[] $linkedBlock
     * @return array|string
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function fillData($linkedBlock)
    {
        $cmsBlockCollection = $this->cmsBlockCollection->create()
            ->addFieldToFilter('block_id', ['eq' => $linkedBlock['block_id']])
            // @codingStandardsIgnoreStart
            ->getFirstItem();
        // @codingStandardsIgnoreEnd
        if (isset($cmsBlockCollection) && !empty($cmsBlockCollection->getData())) {
            $cmsStoreId = $cmsBlockCollection->getStoreId();
            return [
                'id' => $cmsBlockCollection->getId(),
                'title' => $cmsBlockCollection->getTitle(),
                'identifier' => $cmsBlockCollection->getIdentifier(),
                'is_active' => $this->status->getOptionText($cmsBlockCollection->getIsActive()),
                'store_id' => $this->getStoreLabels($cmsStoreId),
                'position' => $linkedBlock['position'],
            ];
        } else {
            return "";
        }
    }

    /**
     * Retrieve all data scopes
     *
     * @return array
     */
    // @codingStandardsIgnoreStart
    protected function getDataScopes()
    {
        return [
            static::DATA_SCOPE_BLOCK
        ];
    }
    // @codingStandardsIgnoreEnd

    /**
     * {@inheritdoc}
     */
    public function modifyMeta(array $meta)
    {
        $meta = array_replace_recursive(
            $meta,
            [
                static::GROUP_BLOCK => [
                    'children' => [
                        $this->scopePrefix . static::DATA_SCOPE_BLOCK => $this->getBlockFieldset(),
                    ],
                    'arguments' => [
                        'data' => [
                            'config' => [
                                'label' => __('Product Tabs'),
                                'collapsible' => true,
                                'componentType' => Fieldset::NAME,
                                'dataScope' => static::DATA_SCOPE,
                                'sortOrder' =>
                                    $this->getNextGroupSortOrder(
                                        $meta,
                                        self::$previousGroup,
                                        self::$sortOrder
                                    ),
                            ],
                        ],

                    ],
                ],
            ]
        );

        return $meta;
    }

    /**
     * Prepares config for the Related Block fieldset
     *
     * @return array
     */
    public function getBlockFieldset()
    {
        $content = __(
            'Related Block are shown as Tab in Product view.'
        );

        return [
            'children' => [
                'button_set' => $this->getButtonSet(
                    $content,
                    __('Add Tab'),
                    $this->scopePrefix . static::DATA_SCOPE_BLOCK
                ),
                static::DATA_SCOPE_BLOCK => $this->getGrid($this->scopePrefix . static::DATA_SCOPE_BLOCK),
                'modal' => $this->getGenericModal(
                    __('Add Tab'),
                    $this->scopePrefix . static::DATA_SCOPE_BLOCK
                )
            ],
            'arguments' => [
                'data' => [
                    'config' => [
                        'additionalClasses' => 'admin__fieldset-section',
                        'label' => __('Product Tabs'),
                        'collapsible' => false,
                        'componentType' => Fieldset::NAME,
                        'dataScope' => self::DATA_SCOPE,
                        'sortOrder' => 20,
                    ],
                ],
            ]
        ];
    }

    /**
     * Retrieve grid
     *
     * @param string $scope
     * @return array
     * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
     */
    public function getGrid($scope)
    {
        $dataProvider = $scope . '_listing';

        return [
            'arguments' => [
                'data' => [
                    'config' => [
                        'additionalClasses' => 'admin__field-wide',
                        'componentType' => DynamicRows::NAME,
                        'label' => null,
                        'columnsHeader' => false,
                        'columnsHeaderAfterRender' => true,
                        'renderDefaultRecord' => false,
                        'template' => 'ui/dynamic-rows/templates/grid',
                        'component' => 'Magento_Ui/js/dynamic-rows/dynamic-rows-grid',
                        'addButton' => false,
                        'recordTemplate' => 'record',
                        'dataScope' => 'data.links',
                        'deleteButtonLabel' => __('Remove'),
                        'dataProvider' => $dataProvider,
                        'map' => [
                            'id' => 'block_id',
                            'title' => 'title',
                            'identifier' => 'identifier',
                            'is_active' => 'status_text'
                        ],
                        'links' => [
                            'insertData' => '${ $.provider }:${ $.dataProvider }',
                            '__disableTmpl' => ['insertData' => false],
                        ],
                        'sortOrder' => 2,
                    ],
                ],
            ],
            'children' => [
                'record' => [
                    'arguments' => [
                        'data' => [
                            'config' => [
                                'componentType' => 'container',
                                'isTemplate' => true,
                                'is_collection' => true,
                                'component' => 'Magento_Ui/js/dynamic-rows/record',
                                'dataScope' => '',
                            ],
                        ],
                    ],
                    'children' => $this->fillMeta(),
                ],
            ],
        ];
    }

    /**
     * Retrieve button set
     *
     * @param Phrase $content
     * @param Phrase $buttonTitle
     * @param string $scope
     * @return array
     */
    public function getButtonSet(Phrase $content, Phrase $buttonTitle, $scope)
    {
        $modalTarget = 'product_form.product_form.' . static::GROUP_BLOCK . '.' . $scope . '.modal';

        return [
            'arguments' => [
                'data' => [
                    'config' => [
                        'formElement' => 'container',
                        'componentType' => 'container',
                        'label' => false,
                        'content' => $content,
                        'template' => 'ui/form/components/complex',
                    ],
                ],
            ],
            'children' => [
                'button_' . $scope => [
                    'arguments' => [
                        'data' => [
                            'config' => [
                                'formElement' => 'container',
                                'componentType' => 'container',
                                'component' => 'Magento_Ui/js/form/components/button',
                                'actions' => [
                                    [
                                        'targetName' => $modalTarget,
                                        'actionName' => 'toggleModal',
                                    ],
                                    [
                                        'targetName' => $modalTarget . '.' . $scope . '_listing',
                                        'actionName' => 'render',
                                    ]
                                ],
                                'title' => $buttonTitle,
                                'provider' => null,
                            ],
                        ],
                    ],

                ],
            ],
        ];
    }

    public function getPanelChildren()
    {
        return [
            'tabname_products_button_set' => $this->getButtonSet()

        ];
    }

    /**
     * Prepares config for modal slide-out panel
     *
     * @param Phrase $title
     * @param string $scope
     * @return array
     */
    public function getGenericModal(Phrase $title, $scope)
    {
        $listingTarget = $scope . '_listing';

        $modal = [
            'arguments' => [
                'data' => [
                    'config' => [
                        'componentType' => Modal::NAME,
                        'dataScope' => '',
                        'options' => [
                            'title' => $title,
                            'buttons' => [
                                [
                                    'text' => __('Cancel'),
                                    'actions' => [
                                        'closeModal'
                                    ]
                                ],
                                [
                                    'text' => __('Add Selected Blocks'),
                                    'class' => 'action-primary',
                                    'actions' => [
                                        [
                                            'targetName' => 'index = ' . $listingTarget,
                                            'actionName' => 'save'
                                        ],
                                        'closeModal'
                                    ]
                                ],
                            ],
                        ],
                    ],
                ],
            ],
            'children' => [
                $listingTarget => [
                    'arguments' => [
                        'data' => [
                            'config' => [
                                'autoRender' => false,
                                'componentType' => 'insertListing',
                                'dataScope' => $listingTarget,
                                'externalProvider' => $listingTarget . '.' . $listingTarget . '_data_source',
                                'selectionsProvider' => $listingTarget . '.'.$listingTarget . '.product_block_columns.ids',
                                'ns' => $listingTarget,
                                'render_url' => $this->urlBuilder->getUrl('mui/index/render'),
                                'realTimeLink' => true,
                                'dataLinks' => [
                                    'imports' => false,
                                    'exports' => true
                                ],
                                'behaviourType' => 'simple',
                                'externalFilterMode' => true
                            ],
                        ],
                    ],
                ],
            ],
        ];

        return $modal;
    }

    /**
     * Retrieve meta column
     *
     * @return array
     */
    public function fillMeta()
    {
        return [
            'id' => $this->getTextColumn('id', false, __('ID'), 0),
            'title' => $this->getTextColumn('title', false, __('Block'), 10),
            'identifier' => $this->getTextColumn('identifier', false, __('Identifier'), 20),
            'is_active' => $this->getTextColumn('is_active', true, __('Status'), 30),
            'store_id' => $this->getTextColumn('store_id', true, __('Store'), 40),
            'actionDelete' => [
                'arguments' => [
                    'data' => [
                        'config' => [
                            'additionalClasses' => 'data-grid-actions-cell',
                            'componentType' => 'actionDelete',
                            'dataType' => Text::NAME,
                            'label' => __('Actions'),
                            'sortOrder' => 50,
                            'fit' => true,
                        ],
                    ],
                ],
            ],
            'position' => [
                'arguments' => [
                    'data' => [
                        'config' => [
                            'dataType' => Number::NAME,
                            'formElement' => Input::NAME,
                            'componentType' => Field::NAME,
                            'dataScope' => 'position',
                            'sortOrder' => 60,
                            'visible' => false,
                        ],
                    ],
                ],
            ],
        ];
    }

    /**
     * Retrieve text column structure
     *
     * @param string $dataScope
     * @param bool $fit
     * @param Phrase $label
     * @param int $sortOrder
     * @return array
     */
    public function getTextColumn($dataScope, $fit, Phrase $label, $sortOrder)
    {
        $column = [
            'arguments' => [
                'data' => [
                    'config' => [
                        'componentType' => Field::NAME,
                        'formElement' => Input::NAME,
                        'elementTmpl' => 'ui/dynamic-rows/cells/text',
                        'component' => 'Magento_Ui/js/form/element/text',
                        'dataType' => Text::NAME,
                        'dataScope' => $dataScope,
                        'fit' => $fit,
                        'label' => $label,
                        'sortOrder' => $sortOrder,
                    ],
                ],
            ],
        ];

        return $column;
    }

    /**
     * @return \Magento\Store\Api\Data\StoreInterface[]
     */
    private function getStores()
    {
        static $stores = null;
        if (is_null($stores)) {
            $stores = $this->storeManager->getStores();
        }
        return $stores;
    }

    /**
     * @param array $storeIds
     * @return string
     */
    private function getStoreLabels($storeIds)
    {
        $content = "";
        $storeArray = $this->getStores();
        foreach ($storeIds ?? [] as $id) {
            if ($id == 0) {
                $name = __('All');
            } else {
                $name = $storeArray[$id]->getName();
            }
            $content ? $content .= "," : null;
            $content .= $name;
        }
        return $content;
    }
}
