<?php

namespace Xortex\App\Checkout\Model;


class GuestShippingInformationManagementPlugin
{

    /**
     * @var \Magento\Quote\Model\QuoteIdMaskFactory
     */
    protected $quoteIdMaskFactory;

    /**
     * @var \Xortex\App\Checkout\Model\ShippingInformationManagementPlugin
     */
    protected $shippingInformationManagementPlugin;

    /**
     * @param \Magento\Quote\Model\QuoteIdMaskFactory $quoteIdMaskFactory
     * @param \Xortex\App\Checkout\Model\ShippingInformationManagementPlugin $shippingInformationManagementPlugin
     * @codeCoverageIgnore
     */
    public function __construct(
        \Magento\Quote\Model\QuoteIdMaskFactory $quoteIdMaskFactory,
        \Xortex\App\Checkout\Model\ShippingInformationManagementPlugin $shippingInformationManagementPlugin
    )
    {
        $this->quoteIdMaskFactory = $quoteIdMaskFactory;
        $this->shippingInformationManagementPlugin = $shippingInformationManagementPlugin;
    }

    /**
     * @param \Magento\Checkout\Model\GuestShippingInformationManagement $subject
     * @param $cartId
     * @param \Magento\Checkout\Api\Data\ShippingInformationInterface $addressInformation
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function beforeSaveAddressInformation(
        \Magento\Checkout\Model\GuestShippingInformationManagement $subject,
        $cartId,
        \Magento\Checkout\Api\Data\ShippingInformationInterface $addressInformation
    )
    {
        $quoteIdMask = $this->quoteIdMaskFactory->create()->load($cartId, 'masked_id');
        $cartId = $quoteIdMask->getQuoteId();

        $this->shippingInformationManagementPlugin->beforeSaveAddressInformation($subject, $cartId, $addressInformation);
    }
}