<?php
/**
 * Created by PhpStorm.
 * User: eugen
 * Date: 27.11.2015
 * Time: 17:53
 */

namespace Xortex\App\Checkout\Observer;

use Magento\Framework\Event\Observer as EventObserver;
use Magento\Framework\Event\ObserverInterface;

class SaveExtensionAttributesToOrder implements ObserverInterface
{
    /**
     * @var \Magento\Quote\Model\QuoteRepository
     */
    protected $quoteRepository;

    /**
     * @param \Magento\Quote\Model\QuoteRepository $quoteRepository
     */
    public function __construct(
      \Magento\Quote\Model\QuoteRepository $quoteRepository)
    {
        $this->quoteRepository = $quoteRepository;
    }

    public function execute(EventObserver $observer)
    {
        /** @var  \Magento\Sales\Model\Order $order */
        $order = $observer->getOrder();
        /** @var \Magento\Quote\Model\Quote $quote */
        $quote = $this->quoteRepository->get($order->getQuoteId());

        // Shipping Attributes
        $billingExtAttributes = $quote->getBillingAddress()->getExtensionAttributes();
        if ($billingExtAttributes) {
            $telephoneMobile = $billingExtAttributes->getTelephoneMobile();
            $quote->getBillingAddress()->setTelephoneMobile($telephoneMobile);
        }

        /**
         * SET Optional Address Data
         */
        if($telephoneMobile = $quote->getBillingAddress()->getTelephoneMobile()) {
            $order->getBillingAddress()->setTelephoneMobile($telephoneMobile);
        }

        return $this;
    }

}