<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="perspective.partytown.module.init">
            <arguments>
                <argument name="csp_whitelist" xsi:type="boolean">true</argument>
            </arguments>
        </referenceBlock>
        <referenceBlock name="checkoutpaymenterror_discount_popup">
            <arguments>
                <argument name="csp_whitelist" xsi:type="boolean">true</argument>
            </arguments>
        </referenceBlock>
    </body>
</page>
