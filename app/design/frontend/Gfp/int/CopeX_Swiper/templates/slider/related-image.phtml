<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Magento\Catalog\Block\Product\Image;
use Magento\Framework\Escaper;

/** @var $block Image */
/** @var $escaper Escaper */
?>
<img class="hover:shadow-sm object-cover max-h-60	 <?= $escaper->escapeHtmlAttr($block->getClass()) ?>"
     x-data=""
     @update-gallery-<?= (int)$block->getProductId() ?>.window="$el.src = $event.detail"
<?php foreach ($block->getCustomAttributes() as $name => $value): ?>
<?= $escaper->escapeHtmlAttr($name) ?>="<?= $escaper->escapeHtmlAttr($value) ?>"
<?php endforeach; ?>
src="<?= $escaper->escapeUrl($block->getImageUrl()) ?>"
loading="lazy"
alt="<?= $escaper->escapeHtmlAttr($block->getLabel()) ?>"
title="<?= $escaper->escapeHtmlAttr($block->getLabel()) ?>"
/>
