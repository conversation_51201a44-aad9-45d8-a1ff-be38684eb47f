<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Magento\Theme\Block\Html\Breadcrumbs;
use Magento\Framework\Escaper;

/** @var Escaper $escaper */
/** @var Breadcrumbs $block */
/** @var array $crumbs */
?>
<?php if ($crumbs && is_array($crumbs)): ?>
<nav class="max-w-screen-2xl mx-auto px-4 font-medium hidden md:block breadcrumbs-block" aria-label="Breadcrumb">
    <div class="breadcrumbs">
        <ul class="items list-reset my-2 flex flex-wrap text-primary text-sm mx-auto">
            <?php foreach ($crumbs as $crumbName => $crumbInfo): ?>
                <li class="item flex <?= $escaper->escapeHtmlAttr($crumbName) ?>">
                <?php if (!$crumbInfo['first']): ?>
                    <span aria-hidden="true" class="separator text-primary-lighter2 px-2 pt-0.5 text-xs">/</span>
                <?php endif; ?>
                <?php if ($crumbInfo['link']): ?>
                    <a href="<?= $escaper->escapeUrl($crumbInfo['link']) ?>"
                       class="no-underline"
                       title="<?= $escaper->escapeHtmlAttr($crumbInfo['title']) ?>"
                    ><?= $escaper->escapeHtml($crumbInfo['label']) ?></a>
                <?php elseif ($crumbInfo['last']): ?>
                    <span class="text-primary-lighter2"
                          aria-current="page"
                    ><?= $escaper->escapeHtml($crumbInfo['label']) ?></span>
                <?php else: ?>
                    <?= $escaper->escapeHtml($crumbInfo['label']) ?>
                <?php endif; ?>
                </li>
            <?php endforeach; ?>
        </ul>
    </div>
</nav>
<?php endif; ?>
