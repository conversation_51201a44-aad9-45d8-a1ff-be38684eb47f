<?php
/** @var Template $block */
/** @var Escaper $escaper */

use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

?>

<script>
    function initDeliveryDateChooser(config) {
        return {
            formattedDate: config.formattedDate,
            productGroup: config.productGroup,
            itemId: config.itemId,
            label: config.label,
            showDeliveryDateChooser: false,
            status: false,

            toggleDeliveryDateChooser(){
                this.showDeliveryDateChooser = !this.showDeliveryDateChooser;
            },

            processChange(event){
                const deliveryDay = event.target.value;
                const minDeliveryDay = event.target.getAttribute('min');

                const d = new Date(deliveryDay);
                this.formattedDate = d.getDate() + '.' + (1 + d.getMonth()) + '.' + d.getUTCFullYear();

                const url = '<?= $escaper->escapeUrl($block->getBaseUrl()) ?>cwpicker/cart/change';
                fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest',
                    },
                    body: new URLSearchParams({
                        delivery_day: deliveryDay,
                        min_delivery_day: minDeliveryDay,
                        product_group: this.productGroup,
                        item_id: this.itemId
                    }),
                }).then(response => response.json()).then((data) => {
                    const deliveryDay = data.delivery_day;
                    if ("error" in data) {
                        this.status = "error";
                        const origLabel = this.label;
                        this.label = data.error;
                        setTimeout(()=> {
                            this.label = origLabel;
                            this.status = false;
                        }, 5000);
                    }
                    else {
                        this.status = "success";
                        setTimeout(()=> {
                            this.status = false;
                        }, 5000);
                    }
                    const d = new Date(deliveryDay);
                    this.formattedDate = d.getDate() + '.' + (1 + d.getMonth()) + '.' + d.getUTCFullYear();
                    event.target.value = deliveryDay;
                });
            }
        }
    }
</script>
