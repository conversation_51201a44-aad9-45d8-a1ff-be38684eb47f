<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Magento\Catalog\Block\Product\ProductList\Toolbar;
use Magento\Framework\Escaper;
use Hyva\Theme\Model\LocaleFormatter;

/** @var Toolbar $block */
/** @var Escaper $escaper */
/** @var LocaleFormatter $localeFormatter */

$availableLimitOptions = $block->getAvailableLimit();

?>

<?php if (count($availableLimitOptions) > 1): ?>
    <div class="text-center mt-4 lg:mt-0">
        <div class="control">
            <label class="text-sm label">
                <span><?= $escaper->escapeHtml((string)__('Show')) ?></span>

                <select data-role="limiter" class="form-select limiter-options"
                        @change="changeUrl(
                    'product_list_limit',
                    $event.currentTarget.options[$event.currentTarget.selectedIndex].value,
                    options.limitDefault
                )">
                    <?php foreach ($block->getAvailableLimit() as $key => $limit): ?>
                        <option value="<?= $escaper->escapeHtmlAttr($key) ?>"
                            <?php if ($block->isLimitCurrent($key)): ?>
                                selected="selected"
                            <?php endif ?>>
                            <?= $escaper->escapeHtml(is_numeric($limit) ? $localeFormatter->formatNumber((int) $limit) : $limit) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </label>
        </div>
        <span class="limiter-text text-sm ml-2 sr-only">
        <?= $escaper->escapeHtml(__('per page')) ?>
    </span>
    </div>
<?php else: ?>
    <div class="field limiter flex order-3 sm:order-2 md:order-3 lg:order-2 col-span-2 justify-end">

    </div>
<?php endif;
