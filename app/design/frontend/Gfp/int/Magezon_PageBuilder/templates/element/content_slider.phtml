<?php

use Cope<PERSON>\Swiper\ViewModel\Swiper;
use Hyva\Theme\Model\ViewModelRegistry;


/** @var \Magento\Framework\Escaper $escaper */
/** @var \Magento\Framework\View\Element\Template $block */

/** @var ViewModelRegistry $viewModels */

/** @var \CopeX\Swiper\ViewModel\Swiper $swiper */



$swiper = $viewModels->require(Swiper::class);
$sliderViewModel = $viewModels->require(\Hyva\Theme\ViewModel\Slider::class);
?>

<?php
$coreHelper      = $this->helper('\Magezon\Core\Helper\Data');
$element         = $this->getElement();
$title           = $coreHelper->filter($element->getData('title'));
$titleAlign      = $element->getData('title_align');
$titleTag        = $element->getData('title_tag') ? $element->getData('title_tag') : 'h2';
$description     = $coreHelper->filter($element->getData('description'));
$showLine        = $element->getData('show_line');
$linePosition    = $element->getData('line_position');
$items           = $this->toObjectArray($element->getItems());
$htmlId          = $element->getHtmlId();
$carouselOptions = $this->getOwlCarouselOptions();
$classes         = $this->getOwlCarouselClasses();
?>
<?php if (count($items)) { ?>
<div>
	<?php if ($title || $description) { ?>
	<div class="mgz-block-heading mgz-block-heading-align-<?= $titleAlign ?><?= $showLine ? ' mgz-block-heading-line' : '' ?> mgz-block-heading-line-position-<?= $linePosition ?>">
		<?php if ($title) { ?>
			<<?= $titleTag ?> class="title"><?= $title ?></<?= $titleTag ?>>
		<?php } ?>
		<?php if ($description) { ?>
			<div class="info"><?= $description ?></div>
		<?php } ?>
	</div>
	<?php } ?>
	<div class="mgz-block-content">
		<?php if ($items && count($items)) { ?>
            <?= $sliderViewModel->getSliderForItems('Magezon_PageBuilder::element/content_slider/item.phtml', $items, 'CopeX_Swiper::slider.phtml')
                ->addData([
                    'width' => 'w-full',
                    'lightbox' => false,
                    'pagination' => $carouselOptions['dots'],
                    'loop' => $carouselOptions['loop'],
                    'slides_per_view' => max($carouselOptions['item_xs'],1),
                    'extra_config' => '{
                                slidesPerGroup: '.$carouselOptions['slideBy'].',
                                breakpoints: {
                                    576: { 
                                        slidesPerView: '.$carouselOptions['item_sm'].'
                                    },
                                    768: { 
                                        slidesPerView: '.$carouselOptions['item_md'].'
                                    },
                                    992: { 
                                        slidesPerView: '.$carouselOptions['item_lg'].'
                                    },
                                    1200: { 
                                        slidesPerView: '.$carouselOptions['item_xl'].'
                                    }
                                },
                                speed: 100,
                                watchSlidesProgress: true,
                            }',
                    'swiper_wrapper_class' => 'swiper-wrapper',
                    'swiper_slide_class' => 'swiper-slide px-2 pb-2 flex flex-col w-full',
                ])
                ->toHtml(); ?>
		<?php } ?>
	</div>
</div>
<?php } ?>