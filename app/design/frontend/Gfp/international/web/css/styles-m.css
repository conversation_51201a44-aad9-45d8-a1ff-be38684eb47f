body {
    margin: 0;
    padding: 0;
}
article,
aside,
details,
figcaption,
figure,
main,
footer,
header,
nav,
section,
summary {
    display: block;
}
audio,
canvas,
video {
    display: inline-block;
}
audio:not([controls]) {
    display: none;
    height: 0;
}
nav ul,
nav ol {
    list-style: none none;
}
img {
    border: 0;
}
img,
object,
video,
embed {
    height: auto;
    max-width: 100%;
}
svg:not(:root) {
    overflow: hidden;
}
figure {
    margin: 0;
}
html {
    font-size: 62.5%;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    font-size-adjust: 100%;
}
body {
    color: #333333;
    font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-style: normal;
    font-weight: 400;
    line-height: 1.42857143;
    font-size: 1.4rem;
}
p {
    margin-top: 0rem;
    margin-bottom: 1rem;
}
abbr[title] {
    border-bottom: 1px dotted #cccccc;
    cursor: help;
}
b,
strong {
    font-weight: 500;
}
em,
i {
    font-style: italic;
}
mark {
    background: #f6f6f6;
    color: #000000;
}
small,
.small {
    font-size: 12px;
}
hr {
    border: 0;
    border-top: 1px solid #cccccc;
    margin-bottom: 20px;
    margin-top: 20px;
}
sub,
sup {
    font-size: 71.42857143000001%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
}
sup {
    top: -.5em;
}
sub {
    bottom: -.25em;
}
dfn {
    font-style: italic;
}
h1 {
    font-weight: 300;
    line-height: 1.1;
    font-size: 2.6rem;
    margin-top: 0rem;
    margin-bottom: 2rem;
}
h2 {
    font-weight: 300;
    line-height: 1.1;
    font-size: 2.6rem;
    margin-top: 2.5rem;
    margin-bottom: 2rem;
}
h3 {
    font-weight: 600;
    line-height: 1.1;
    font-size: 1.8rem;
    margin-top: 2rem;
    margin-bottom: 1rem;
}
h4 {
    font-weight: 700;
    line-height: 1.1;
    font-size: 1.4rem;
    margin-top: 2rem;
    margin-bottom: 2rem;
}
h5 {
    font-weight: 700;
    line-height: 1.1;
    font-size: 1.2rem;
    margin-top: 2rem;
    margin-bottom: 2rem;
}
h6 {
    font-weight: 700;
    line-height: 1.1;
    font-size: 1rem;
    margin-top: 2rem;
    margin-bottom: 2rem;
}
h1 small,
h2 small,
h3 small,
h4 small,
h5 small,
h6 small,
h1 .small,
h2 .small,
h3 .small,
h4 .small,
h5 .small,
h6 .small {
    color: #333333;
    font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-style: normal;
    font-weight: 400;
    line-height: 1;
}
a,
.alink {
    color: #1979c3;
    text-decoration: none;
}
a:visited,
.alink:visited {
    color: #1979c3;
    text-decoration: none;
}
a:hover,
.alink:hover {
    color: #006bb4;
    text-decoration: underline;
}
a:active,
.alink:active {
    color: #85b84b;
    text-decoration: underline;
}
ul,
ol {
    margin-top: 0rem;
    margin-bottom: 2.5rem;
}
ul > li,
ol > li {
    margin-top: 0rem;
    margin-bottom: 1rem;
}
ul ul,
ol ul,
ul ol,
ol ol {
    margin-bottom: 0;
}
dl {
    margin-bottom: 20px;
    margin-top: 0;
}
dt {
    font-weight: 700;
    margin-bottom: 5px;
    margin-top: 0;
}
dd {
    margin-bottom: 10px;
    margin-top: 0;
    margin-left: 0;
}
code,
kbd,
pre,
samp {
    font-family: Menlo, Monaco, Consolas, 'Courier New', monospace;
}
code {
    background: #f6f6f6;
    color: #111111;
    padding: 2px 4px;
    font-size: 1.2rem;
    white-space: nowrap;
}
kbd {
    background: #f6f6f6;
    color: #111111;
    padding: 2px 4px;
    font-size: 1.2rem;
}
pre {
    background: #f6f6f6;
    border: 1px solid #cccccc;
    color: #111111;
    line-height: 1.42857143;
    margin: 0 0 10px;
    padding: 10px;
    font-size: 1.2rem;
    display: block;
    word-wrap: break-word;
}
pre code {
    background-color: transparent;
    border-radius: 0;
    color: inherit;
    font-size: inherit;
    padding: 0;
    white-space: pre-wrap;
}
blockquote {
    border-left: 0 solid #cccccc;
    margin: 0 0 20px 40px;
    padding: 0;
    color: #333333;
    font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-style: italic;
    font-weight: 400;
    line-height: 1.42857143;
    font-size: 1.4rem;
}
blockquote p:last-child,
blockquote ul:last-child,
blockquote ol:last-child {
    margin-bottom: 0;
}
blockquote footer,
blockquote small,
blockquote .small {
    color: #333333;
    line-height: 1.42857143;
    font-size: 1rem;
    display: block;
}
blockquote footer:before,
blockquote small:before,
blockquote .small:before {
    content: '\2014 \00A0';
}
blockquote cite {
    font-style: normal;
}
blockquote:before,
blockquote:after {
    content: '';
}
q {
    quotes: none;
}
q:before,
q:after {
    content: '';
    content: none;
}
cite {
    font-style: normal;
}
table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    max-width: 100%;
}
table th {
    text-align: left;
}
table > tbody > tr > th,
table > tfoot > tr > th,
table > tbody > tr > td,
table > tfoot > tr > td {
    vertical-align: top;
}
table > thead > tr > th,
table > thead > tr > td {
    vertical-align: bottom;
}
table > thead > tr > th,
table > tbody > tr > th,
table > tfoot > tr > th,
table > thead > tr > td,
table > tbody > tr > td,
table > tfoot > tr > td {
    padding: 11px 10px;
}
button {
    background-image: none;
    background: #eeeeee;
    border: 1px solid #cccccc;
    color: #333333;
    cursor: pointer;
    display: inline-block;
    font-weight: 600;
    margin: 0;
    padding: 7px 15px;
    font-size: 1.4rem;
    line-height: 1.6rem;
    box-sizing: border-box;
    vertical-align: middle;
}

button.disabled,
button[disabled],
fieldset[disabled] button {
    opacity: 0.5;
    cursor: default;
    pointer-events: none;
}
button::-moz-focus-inner {
    border: 0;
    padding: 0;
}
input[type="text"],
input[type="password"],
input[type="url"],
input[type="tel"],
input[type="search"],
input[type="number"],
input[type="datetime"],
input[type="email"] {
    background: #ffffff;
    background-clip: padding-box;
    border: 2px solid #c2c2c2;
    border-radius: 1px;
    font-size: 14px;
    height: 32px;
    line-height: 1.42857143;
    padding: 0 9px;
    vertical-align: baseline;
    width: 100%;
    box-sizing: border-box;
    border-radius: 5px;
}
input[type="text"]:disabled,
input[type="password"]:disabled,
input[type="url"]:disabled,
input[type="tel"]:disabled,
input[type="search"]:disabled,
input[type="number"]:disabled,
input[type="datetime"]:disabled,
input[type="email"]:disabled {
    opacity: 0.5;
}
input[type="text"]::-moz-placeholder,
input[type="password"]::-moz-placeholder,
input[type="url"]::-moz-placeholder,
input[type="tel"]::-moz-placeholder,
input[type="search"]::-moz-placeholder,
input[type="number"]::-moz-placeholder,
input[type="datetime"]::-moz-placeholder,
input[type="email"]::-moz-placeholder {
    color: #c2c2c2;
}
input[type="text"]::-webkit-input-placeholder,
input[type="password"]::-webkit-input-placeholder,
input[type="url"]::-webkit-input-placeholder,
input[type="tel"]::-webkit-input-placeholder,
input[type="search"]::-webkit-input-placeholder,
input[type="number"]::-webkit-input-placeholder,
input[type="datetime"]::-webkit-input-placeholder,
input[type="email"]::-webkit-input-placeholder {
    color: #c2c2c2;
}
input[type="text"]:-ms-input-placeholder,
input[type="password"]:-ms-input-placeholder,
input[type="url"]:-ms-input-placeholder,
input[type="tel"]:-ms-input-placeholder,
input[type="search"]:-ms-input-placeholder,
input[type="number"]:-ms-input-placeholder,
input[type="datetime"]:-ms-input-placeholder,
input[type="email"]:-ms-input-placeholder {
    color: #c2c2c2;
}

input[type="search"] {
    -webkit-appearance: none;
}
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-results-button,
input[type="search"]::-webkit-search-results-decoration {
    -webkit-appearance: none;
}
select {
    background: #ffffff;
    background-clip: padding-box;
    border: 1px solid #c2c2c2;
    border-radius: 1px;
    font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-size: 14px;
    height: 32px;
    line-height: 1.42857143;
    padding: 5px 10px 4px;
    vertical-align: baseline;
    width: 100%;
    box-sizing: border-box;
}
select:disabled {
    opacity: 0.5;
}
select[multiple="multiple"] {
    height: auto;
    background-image: none;
}
textarea {
    background: #ffffff;
    background-clip: padding-box;
    border: 1px solid #c2c2c2;
    border-radius: 1px;
    font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-size: 14px;
    height: auto;
    line-height: 1.42857143;
    margin: 0;
    padding: 10px;
    vertical-align: baseline;
    width: 100%;
    box-sizing: border-box;
    resize: vertical;
}
textarea:disabled {
    opacity: 0.5;
}
textarea::-moz-placeholder {
    color: #c2c2c2;
}
textarea::-webkit-input-placeholder {
    color: #c2c2c2;
}
textarea:-ms-input-placeholder {
    color: #c2c2c2;
}
input[type="checkbox"] {
    margin: 2px 5px 0 0;
}
input[type="checkbox"]:disabled {
    opacity: 0.5;
}
input[type="radio"] {
    margin: 2px 5px 0 0;
}
input[type="radio"]:disabled {
    opacity: 0.5;
}
input.mage-error,
select.mage-error,
textarea.mage-error {
    border-color: #ed8380;
}
div.mage-error[generated] {
    color: #e02b27;
    font-size: 15px;
}
input[type="button"],
input[type="reset"],
input[type="submit"] {
    cursor: pointer;
    -webkit-appearance: button;
}
input::-moz-focus-inner {
    border: 0;
    padding: 0;
}
form {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
address {
    font-style: normal;
}
*:focus {
    box-shadow: none;
    outline: 0;
}
._keyfocus *:focus,
input:not([disabled]):focus,
textarea:not([disabled]):focus,
select:not([disabled]):focus {
    box-shadow: none;
}
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
.block-wishlist-management .wishlist-select-items,
.paypal-review .items-qty,
.bundle-options-container .block-bundle-summary .bundle.items,
.product-items,
.prices-tier,
.search-terms,
.cart-summary .checkout-methods-items,
.opc-wrapper .opc,
.opc-progress-bar,
.checkout-agreements-items,
.order-details-items .items-qty,
.footer.content ul,
.block-banners .banner-items,
.block-banners-inline .banner-items,
.block-event .slider-panel .slider {
    margin: 0;
    padding: 0;
    list-style: none none;
}
.block-wishlist-management .wishlist-select-items > li,
.paypal-review .items-qty > li,
.bundle-options-container .block-bundle-summary .bundle.items > li,
.product-items > li,
.prices-tier > li,
.search-terms > li,
.cart-summary .checkout-methods-items > li,
.opc-wrapper .opc > li,
.opc-progress-bar > li,
.checkout-agreements-items > li,
.order-details-items .items-qty > li,
.footer.content ul > li,
.block-banners .banner-items > li,
.block-banners-inline .banner-items > li,
.block-event .slider-panel .slider > li {
    margin: 0;
}

.action-primary.disabled,
.action-primary[disabled],
fieldset[disabled] .action-primary {
    opacity: 0.5;
    cursor: default;
    pointer-events: none;
}
.block-event .slider-panel .slider .item .action.event {
    background-image: none;
    background: #eeeeee;
    border: 1px solid #cccccc;
    color: #333333;
    cursor: pointer;
    font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-weight: 600;
    margin: 0;
    padding: 7px 15px;
    font-size: 1.4rem;
    line-height: 1.6rem;
    box-sizing: border-box;
    vertical-align: middle;
    display: inline-block;
    text-decoration: none;
    border-radius: 0;
}
.abs-action-link-button:focus,
.abs-action-link-button:active,
.block-event .slider-panel .slider .item .action.event:focus,
.block-event .slider-panel .slider .item .action.event:active {
    background: #bae588;
    border: 1px solid #cccccc;
    color: #333333;
}
.abs-action-link-button:hover,
.block-event .slider-panel .slider .item .action.event:hover {
    background: #e1e1e1;
    border: 1px solid #cccccc;
    color: #333333;
}
.abs-action-link-button.disabled,
.abs-action-link-button[disabled],
fieldset[disabled] .abs-action-link-button,
.block-event .slider-panel .slider .item .action.event.disabled,
.block-event .slider-panel .slider .item .action.event[disabled],
fieldset[disabled] .block-event .slider-panel .slider .item .action.event {
    opacity: 0.5;
    cursor: default;
    pointer-events: none;
}
.abs-action-link-button:hover,
.abs-action-link-button:active,
.abs-action-link-button:focus,
.block-event .slider-panel .slider .item .action.event:hover,
.block-event .slider-panel .slider .item .action.event:active,
.block-event .slider-panel .slider .item .action.event:focus {
    text-decoration: none;
}
.abs-action-remove,
.abs-discount-block .action.check,
.popup .actions-toolbar .action.cancel,
.cart-summary .block.giftcard .action.check,
.gift-options .actions-toolbar .action-cancel,
.table-comparison .wishlist.split.button > .action,
.product-items .wishlist.split.button > .action,
.paypal-button-widget .paypal-button,
.bundle-options-container .action.back,
.block.related .action.select,
.cart.table-wrapper .action.help.map,
.opc-wrapper .edit-address-link,
.action-auth-toggle,
.checkout-payment-method .checkout-billing-address .action-cancel,
.checkout-agreements-block .action-show,
.abs-add-fields .action.remove,
.paypal-review-discount .block .action.check,
.cart-discount .action.check,
.form-giftregistry-share .action.remove,
.form-giftregistry-edit .action.remove,
.form-add-invitations .action.remove,
.form-create-return .action.remove,
.form.send.friend .action.remove {
    line-height: 1.42857143;
    padding: 0;
    color: #1979c3;
    text-decoration: none;
    background: none;
    border: 0;
    display: inline;
    font-weight: 400;
    border-radius: 0;
}
.abs-action-button-as-link:visited,
.abs-action-remove:visited,
.abs-discount-block .action.check:visited,
.popup .actions-toolbar .action.cancel:visited,
.cart-summary .block.giftcard .action.check:visited,
.gift-options .actions-toolbar .action-cancel:visited,
.table-comparison .wishlist.split.button > .action:visited,
.product-items .wishlist.split.button > .action:visited,
.paypal-button-widget .paypal-button:visited,
.bundle-options-container .action.back:visited,
.block.related .action.select:visited,
.cart.table-wrapper .action.help.map:visited,
.opc-wrapper .edit-address-link:visited,
.action-auth-toggle:visited,
.checkout-payment-method .checkout-billing-address .action-cancel:visited,
.checkout-agreements-block .action-show:visited,
.abs-add-fields .action.remove:visited,
.paypal-review-discount .block .action.check:visited,
.cart-discount .action.check:visited,
.form-giftregistry-share .action.remove:visited,
.form-giftregistry-edit .action.remove:visited,
.form-add-invitations .action.remove:visited,
.form-create-return .action.remove:visited,
.form.send.friend .action.remove:visited {
    color: #1979c3;
    text-decoration: none;
}
.abs-action-button-as-link:hover,
.abs-action-remove:hover,
.abs-discount-block .action.check:hover,
.popup .actions-toolbar .action.cancel:hover,
.cart-summary .block.giftcard .action.check:hover,
.gift-options .actions-toolbar .action-cancel:hover,
.table-comparison .wishlist.split.button > .action:hover,
.product-items .wishlist.split.button > .action:hover,
.paypal-button-widget .paypal-button:hover,
.bundle-options-container .action.back:hover,
.block.related .action.select:hover,
.cart.table-wrapper .action.help.map:hover,
.opc-wrapper .edit-address-link:hover,
.action-auth-toggle:hover,
.checkout-payment-method .checkout-billing-address .action-cancel:hover,
.checkout-agreements-block .action-show:hover,
.abs-add-fields .action.remove:hover,
.paypal-review-discount .block .action.check:hover,
.cart-discount .action.check:hover,
.form-giftregistry-share .action.remove:hover,
.form-giftregistry-edit .action.remove:hover,
.form-add-invitations .action.remove:hover,
.form-create-return .action.remove:hover,
.form.send.friend .action.remove:hover {
    color: #006bb4;
    text-decoration: underline;
}
.abs-action-button-as-link:active,
.abs-action-remove:active,
.abs-discount-block .action.check:active,
.popup .actions-toolbar .action.cancel:active,
.cart-summary .block.giftcard .action.check:active,
.gift-options .actions-toolbar .action-cancel:active,
.table-comparison .wishlist.split.button > .action:active,
.product-items .wishlist.split.button > .action:active,
.paypal-button-widget .paypal-button:active,
.bundle-options-container .action.back:active,
.block.related .action.select:active,
.cart.table-wrapper .action.help.map:active,
.opc-wrapper .edit-address-link:active,
.action-auth-toggle:active,
.checkout-payment-method .checkout-billing-address .action-cancel:active,
.checkout-agreements-block .action-show:active,
.abs-add-fields .action.remove:active,
.paypal-review-discount .block .action.check:active,
.cart-discount .action.check:active,
.form-giftregistry-share .action.remove:active,
.form-giftregistry-edit .action.remove:active,
.form-add-invitations .action.remove:active,
.form-create-return .action.remove:active,
.form.send.friend .action.remove:active {
    color: #85b84b;
    text-decoration: underline;
}
.abs-action-button-as-link:hover,
.abs-action-remove:hover,
.abs-discount-block .action.check:hover,
.popup .actions-toolbar .action.cancel:hover,
.cart-summary .block.giftcard .action.check:hover,
.gift-options .actions-toolbar .action-cancel:hover,
.table-comparison .wishlist.split.button > .action:hover,
.product-items .wishlist.split.button > .action:hover,
.paypal-button-widget .paypal-button:hover,
.bundle-options-container .action.back:hover,
.block.related .action.select:hover,
.cart.table-wrapper .action.help.map:hover,
.opc-wrapper .edit-address-link:hover,
.action-auth-toggle:hover,
.checkout-payment-method .checkout-billing-address .action-cancel:hover,
.checkout-agreements-block .action-show:hover,
.abs-add-fields .action.remove:hover,
.paypal-review-discount .block .action.check:hover,
.cart-discount .action.check:hover,
.form-giftregistry-share .action.remove:hover,
.form-giftregistry-edit .action.remove:hover,
.form-add-invitations .action.remove:hover,
.form-create-return .action.remove:hover,
.form.send.friend .action.remove:hover {
    color: #006bb4;
}
.abs-action-button-as-link:hover,
.abs-action-button-as-link:active,
.abs-action-button-as-link:focus,
.abs-action-remove:hover,
.abs-action-remove:active,
.abs-action-remove:focus,
.abs-discount-block .action.check:hover,
.abs-discount-block .action.check:active,
.abs-discount-block .action.check:focus,
.popup .actions-toolbar .action.cancel:hover,
.popup .actions-toolbar .action.cancel:active,
.popup .actions-toolbar .action.cancel:focus,
.cart-summary .block.giftcard .action.check:hover,
.cart-summary .block.giftcard .action.check:active,
.cart-summary .block.giftcard .action.check:focus,
.gift-options .actions-toolbar .action-cancel:hover,
.gift-options .actions-toolbar .action-cancel:active,
.gift-options .actions-toolbar .action-cancel:focus,
.table-comparison .wishlist.split.button > .action:hover,
.table-comparison .wishlist.split.button > .action:active,
.table-comparison .wishlist.split.button > .action:focus,
.product-items .wishlist.split.button > .action:hover,
.product-items .wishlist.split.button > .action:active,
.product-items .wishlist.split.button > .action:focus,
.paypal-button-widget .paypal-button:hover,
.paypal-button-widget .paypal-button:active,
.paypal-button-widget .paypal-button:focus,
.bundle-options-container .action.back:hover,
.bundle-options-container .action.back:active,
.bundle-options-container .action.back:focus,
.block.related .action.select:hover,
.block.related .action.select:active,
.block.related .action.select:focus,
.cart.table-wrapper .action.help.map:hover,
.cart.table-wrapper .action.help.map:active,
.cart.table-wrapper .action.help.map:focus,
.opc-wrapper .edit-address-link:hover,
.opc-wrapper .edit-address-link:active,
.opc-wrapper .edit-address-link:focus,
.action-auth-toggle:hover,
.action-auth-toggle:active,
.action-auth-toggle:focus,
.checkout-payment-method .checkout-billing-address .action-cancel:hover,
.checkout-payment-method .checkout-billing-address .action-cancel:active,
.checkout-payment-method .checkout-billing-address .action-cancel:focus,
.checkout-agreements-block .action-show:hover,
.checkout-agreements-block .action-show:active,
.checkout-agreements-block .action-show:focus,
.abs-add-fields .action.remove:hover,
.abs-add-fields .action.remove:active,
.abs-add-fields .action.remove:focus,
.paypal-review-discount .block .action.check:hover,
.paypal-review-discount .block .action.check:active,
.paypal-review-discount .block .action.check:focus,
.cart-discount .action.check:hover,
.cart-discount .action.check:active,
.cart-discount .action.check:focus,
.form-giftregistry-share .action.remove:hover,
.form-giftregistry-share .action.remove:active,
.form-giftregistry-share .action.remove:focus,
.form-giftregistry-edit .action.remove:hover,
.form-giftregistry-edit .action.remove:active,
.form-giftregistry-edit .action.remove:focus,
.form-add-invitations .action.remove:hover,
.form-add-invitations .action.remove:active,
.form-add-invitations .action.remove:focus,
.form-create-return .action.remove:hover,
.form-create-return .action.remove:active,
.form-create-return .action.remove:focus,
.form.send.friend .action.remove:hover,
.form.send.friend .action.remove:active,
.form.send.friend .action.remove:focus {
    background: none;
    border: 0;
}
.abs-action-button-as-link.disabled,
.abs-action-button-as-link[disabled],
fieldset[disabled] .abs-action-button-as-link,
.abs-action-remove.disabled,
.abs-action-remove[disabled],
fieldset[disabled] .abs-action-remove,
.abs-discount-block .action.check.disabled,
.abs-discount-block .action.check[disabled],
fieldset[disabled] .abs-discount-block .action.check,
.popup .actions-toolbar .action.cancel.disabled,
.popup .actions-toolbar .action.cancel[disabled],
fieldset[disabled] .popup .actions-toolbar .action.cancel,
.cart-summary .block.giftcard .action.check.disabled,
.cart-summary .block.giftcard .action.check[disabled],
fieldset[disabled] .cart-summary .block.giftcard .action.check,
.gift-options .actions-toolbar .action-cancel.disabled,
.gift-options .actions-toolbar .action-cancel[disabled],
fieldset[disabled] .gift-options .actions-toolbar .action-cancel,
.table-comparison .wishlist.split.button > .action.disabled,
.table-comparison .wishlist.split.button > .action[disabled],
fieldset[disabled] .table-comparison .wishlist.split.button > .action,
.product-items .wishlist.split.button > .action.disabled,
.product-items .wishlist.split.button > .action[disabled],
fieldset[disabled] .product-items .wishlist.split.button > .action,
.paypal-button-widget .paypal-button.disabled,
.paypal-button-widget .paypal-button[disabled],
fieldset[disabled] .paypal-button-widget .paypal-button,
.bundle-options-container .action.back.disabled,
.bundle-options-container .action.back[disabled],
fieldset[disabled] .bundle-options-container .action.back,
.block.related .action.select.disabled,
.block.related .action.select[disabled],
fieldset[disabled] .block.related .action.select,
.cart.table-wrapper .action.help.map.disabled,
.cart.table-wrapper .action.help.map[disabled],
fieldset[disabled] .cart.table-wrapper .action.help.map,
.opc-wrapper .edit-address-link.disabled,
.opc-wrapper .edit-address-link[disabled],
fieldset[disabled] .opc-wrapper .edit-address-link,
.action-auth-toggle.disabled,
.action-auth-toggle[disabled],
fieldset[disabled] .action-auth-toggle,
.checkout-payment-method .checkout-billing-address .action-cancel.disabled,
.checkout-payment-method .checkout-billing-address .action-cancel[disabled],
fieldset[disabled] .checkout-payment-method .checkout-billing-address .action-cancel,
.checkout-agreements-block .action-show.disabled,
.checkout-agreements-block .action-show[disabled],
fieldset[disabled] .checkout-agreements-block .action-show,
.abs-add-fields .action.remove.disabled,
.abs-add-fields .action.remove[disabled],
fieldset[disabled] .abs-add-fields .action.remove,
.paypal-review-discount .block .action.check.disabled,
.paypal-review-discount .block .action.check[disabled],
fieldset[disabled] .paypal-review-discount .block .action.check,
.cart-discount .action.check.disabled,
.cart-discount .action.check[disabled],
fieldset[disabled] .cart-discount .action.check,
.form-giftregistry-share .action.remove.disabled,
.form-giftregistry-share .action.remove[disabled],
fieldset[disabled] .form-giftregistry-share .action.remove,
.form-giftregistry-edit .action.remove.disabled,
.form-giftregistry-edit .action.remove[disabled],
fieldset[disabled] .form-giftregistry-edit .action.remove,
.form-add-invitations .action.remove.disabled,
.form-add-invitations .action.remove[disabled],
fieldset[disabled] .form-add-invitations .action.remove,
.form-create-return .action.remove.disabled,
.form-create-return .action.remove[disabled],
fieldset[disabled] .form-create-return .action.remove,
.form.send.friend .action.remove.disabled,
.form.send.friend .action.remove[disabled],
fieldset[disabled] .form.send.friend .action.remove {
    color: #1979c3;
    opacity: 0.5;
    cursor: default;
    pointer-events: none;
    text-decoration: underline;
}
.abs-action-remove:active,
.abs-action-remove:not(:focus),
.abs-discount-block .action.check:active,
.abs-discount-block .action.check:not(:focus),
.popup .actions-toolbar .action.cancel:active,
.popup .actions-toolbar .action.cancel:not(:focus),
.cart-summary .block.giftcard .action.check:active,
.cart-summary .block.giftcard .action.check:not(:focus),
.gift-options .actions-toolbar .action-cancel:active,
.gift-options .actions-toolbar .action-cancel:not(:focus),
.table-comparison .wishlist.split.button > .action:active,
.table-comparison .wishlist.split.button > .action:not(:focus),
.product-items .wishlist.split.button > .action:active,
.product-items .wishlist.split.button > .action:not(:focus),
.paypal-button-widget .paypal-button:active,
.paypal-button-widget .paypal-button:not(:focus),
.bundle-options-container .action.back:active,
.bundle-options-container .action.back:not(:focus),
.block.related .action.select:active,
.block.related .action.select:not(:focus),
.cart.table-wrapper .action.help.map:active,
.cart.table-wrapper .action.help.map:not(:focus),
.opc-wrapper .edit-address-link:active,
.opc-wrapper .edit-address-link:not(:focus),
.action-auth-toggle:active,
.action-auth-toggle:not(:focus),
.checkout-payment-method .checkout-billing-address .action-cancel:active,
.checkout-payment-method .checkout-billing-address .action-cancel:not(:focus),
.checkout-agreements-block .action-show:active,
.checkout-agreements-block .action-show:not(:focus),
.abs-add-fields .action.remove:active,
.abs-add-fields .action.remove:not(:focus),
.paypal-review-discount .block .action.check:active,
.paypal-review-discount .block .action.check:not(:focus),
.cart-discount .action.check:active,
.cart-discount .action.check:not(:focus),
.form-giftregistry-share .action.remove:active,
.form-giftregistry-share .action.remove:not(:focus),
.form-giftregistry-edit .action.remove:active,
.form-giftregistry-edit .action.remove:not(:focus),
.form-add-invitations .action.remove:active,
.form-add-invitations .action.remove:not(:focus),
.form-create-return .action.remove:active,
.form-create-return .action.remove:not(:focus),
.form.send.friend .action.remove:active,
.form.send.friend .action.remove:not(:focus) {
    box-shadow: none;
}
.abs-revert-to-action-secondary,
.cart-summary .form.giftregistry .fieldset .action.primary,
.paypal-review .block .actions-toolbar .action.primary,
.abs-discount-block .actions-toolbar .action.primary,
.abs-discount-block .actions-toolbar .action.cancel,
.block-compare .action.primary,
.cart-summary .block .actions-toolbar > .primary .action.primary,
.paypal-review-discount .block .actions-toolbar .action.primary,
.paypal-review-discount .block .actions-toolbar .action.cancel,
.cart-discount .actions-toolbar .action.primary,
.cart-discount .actions-toolbar .action.cancel {

}
.abs-revert-secondary-color:focus,
.abs-revert-secondary-color:active,
.abs-revert-to-action-secondary:focus,
.abs-revert-to-action-secondary:active,
.cart-summary .form.giftregistry .fieldset .action.primary:focus,
.cart-summary .form.giftregistry .fieldset .action.primary:active,
.paypal-review .block .actions-toolbar .action.primary:focus,
.paypal-review .block .actions-toolbar .action.primary:active,
.abs-discount-block .actions-toolbar .action.primary:focus,
.abs-discount-block .actions-toolbar .action.primary:active,
.abs-discount-block .actions-toolbar .action.cancel:focus,
.abs-discount-block .actions-toolbar .action.cancel:active,
.block-compare .action.primary:focus,
.block-compare .action.primary:active,
.cart-summary .block .actions-toolbar > .primary .action.primary:focus,
.cart-summary .block .actions-toolbar > .primary .action.primary:active,
.paypal-review-discount .block .actions-toolbar .action.primary:focus,
.paypal-review-discount .block .actions-toolbar .action.primary:active,
.paypal-review-discount .block .actions-toolbar .action.cancel:focus,
.paypal-review-discount .block .actions-toolbar .action.cancel:active,
.cart-discount .actions-toolbar .action.primary:focus,
.cart-discount .actions-toolbar .action.primary:active,
.cart-discount .actions-toolbar .action.cancel:focus,
.cart-discount .actions-toolbar .action.cancel:active {

}
.abs-revert-secondary-color:hover,
.abs-revert-to-action-secondary:hover,
.cart-summary .form.giftregistry .fieldset .action.primary:hover,
.paypal-review .block .actions-toolbar .action.primary:hover,
.abs-discount-block .actions-toolbar .action.primary:hover,
.abs-discount-block .actions-toolbar .action.cancel:hover,
.block-compare .action.primary:hover,
.cart-summary .block .actions-toolbar > .primary .action.primary:hover,
.paypal-review-discount .block .actions-toolbar .action.primary:hover,
.paypal-review-discount .block .actions-toolbar .action.cancel:hover,
.cart-discount .actions-toolbar .action.primary:hover,
.cart-discount .actions-toolbar .action.cancel:hover {

}
.cart-summary .form.giftregistry .fieldset .action.primary {
    font-size: 1.4rem;
    line-height: 1.6rem;
    padding: 7px 15px;
}
.multicheckout .action.primary,
.bundle-actions .action.primary.customize,
.bundle-options-container .block-bundle-summary .box-tocart .action.primary,
.cart-summary .checkout-methods-items .action.primary.checkout,
.block-minicart .block-content > .actions > .primary .action.primary,
.methods-shipping .actions-toolbar .action.primary,
.block-authentication .action.action-register,
.block-authentication .action.action-login,
.checkout-payment-method .payment-method-content > .actions-toolbar > .primary .action.primary,
.form-address-edit .actions-toolbar .action.primary {
    line-height: 2.2rem;
    padding: 14px 17px;
    font-size: 1.8rem;
}
.block-giftregistry-shared-items .product .item-options dt,
.multicheckout .item-options dt,
.cart.table-wrapper .item-options dt {
    clear: left;
    float: left;
    margin: 0 10px 5px 0;
}
.block-giftregistry-shared-items .product .item-options dt:after,
.multicheckout .item-options dt:after,
.cart.table-wrapper .item-options dt:after {
    content: ': ';
}
.block-giftregistry-shared-items .product .item-options dd,
.multicheckout .item-options dd,
.cart.table-wrapper .item-options dd {
    display: block;
    float: none;
    margin: 0 0 5px;
}
.actions-toolbar > .primary .action,
.actions-toolbar > .secondary .action,
.bundle-actions .action.primary.customize,
.bundle-options-container .block-bundle-summary .box-tocart .action.primary {
    width: 100%;
}
.block-giftregistry-shared-items .product-image-wrapper,
.products-list .product-image-wrapper,
.cart.table-wrapper .product-image-wrapper,
.minicart-items .product-image-wrapper {
    height: auto;
    padding: 0 !important;
}
.block-giftregistry-shared-items .product-image-wrapper .product-image-photo,
.products-list .product-image-wrapper .product-image-photo,
.cart.table-wrapper .product-image-wrapper .product-image-photo,
.minicart-items .product-image-wrapper .product-image-photo {
    position: static;
}
.product.media .product.photo .photo.image {
    display: block;
    height: auto;
    margin: 0 auto;
    max-width: 100%;
}
.amazon-validate-container .block .block-title,
.login-container .block .block-title {
    border-bottom: 1px solid #e8e8e8;
    font-size: 1.8rem;
    margin-bottom: 15px;
    padding-bottom: 12px;
}
.amazon-validate-container .block .block-title strong,
.login-container .block .block-title strong {
    font-weight: 500;
}
.giftregisty-dropdown,
.wishlist-dropdown {
    display: inline-block;
    position: relative;
}
.abs-dropdown-simple:before,
.abs-dropdown-simple:after,
.giftregisty-dropdown:before,
.giftregisty-dropdown:after,
.wishlist-dropdown:before,
.wishlist-dropdown:after {
    content: '';
    display: table;
}
.abs-dropdown-simple:after,
.giftregisty-dropdown:after,
.wishlist-dropdown:after {
    clear: both;
}
.abs-dropdown-simple .action.toggle,
.giftregisty-dropdown .action.toggle,
.wishlist-dropdown .action.toggle {
    cursor: pointer;
    display: inline-block;
    text-decoration: none;
}
.abs-dropdown-simple .action.toggle:after,
.giftregisty-dropdown .action.toggle:after,
.wishlist-dropdown .action.toggle:after {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 12px;
    line-height: inherit;
    color: inherit;
    content: '\e622';
    font-family: 'luma-icons';
    margin: 0 0 0 5px;
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.abs-dropdown-simple .action.toggle:hover:after,
.giftregisty-dropdown .action.toggle:hover:after,
.wishlist-dropdown .action.toggle:hover:after {
    color: inherit;
}
.abs-dropdown-simple .action.toggle:active:after,
.giftregisty-dropdown .action.toggle:active:after,
.wishlist-dropdown .action.toggle:active:after {
    color: inherit;
}
.abs-dropdown-simple .action.toggle.active,
.giftregisty-dropdown .action.toggle.active,
.wishlist-dropdown .action.toggle.active {
    display: inline-block;
    text-decoration: none;
}
.abs-dropdown-simple .action.toggle.active:after,
.giftregisty-dropdown .action.toggle.active:after,
.wishlist-dropdown .action.toggle.active:after {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 12px;
    line-height: inherit;
    color: inherit;
    content: '\e621';
    font-family: 'luma-icons';
    margin: 0 0 0 5px;
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.abs-dropdown-simple .action.toggle.active:hover:after,
.giftregisty-dropdown .action.toggle.active:hover:after,
.wishlist-dropdown .action.toggle.active:hover:after {
    color: inherit;
}
.abs-dropdown-simple .action.toggle.active:active:after,
.giftregisty-dropdown .action.toggle.active:active:after,
.wishlist-dropdown .action.toggle.active:active:after {
    color: inherit;
}
.abs-dropdown-simple ul.dropdown,
.giftregisty-dropdown ul.dropdown,
.wishlist-dropdown ul.dropdown {
    margin: 0;
    padding: 0;
    list-style: none none;
    background: #ffffff;
    border: 1px solid #bbbbbb;
    margin-top: 4px;
    min-width: 200px;
    z-index: 100;
    box-sizing: border-box;
    display: none;
    position: absolute;
    top: 100%;
    box-shadow: 0 3px 3px rgba(0, 0, 0, 0.15);
}
.abs-dropdown-simple ul.dropdown li,
.giftregisty-dropdown ul.dropdown li,
.wishlist-dropdown ul.dropdown li {
    margin: 0;
    padding: 5px 5px 5px 23px;
}
.abs-dropdown-simple ul.dropdown li:hover,
.giftregisty-dropdown ul.dropdown li:hover,
.wishlist-dropdown ul.dropdown li:hover {
    background: #e8e8e8;
    cursor: pointer;
}
.abs-dropdown-simple ul.dropdown:before,
.abs-dropdown-simple ul.dropdown:after,
.giftregisty-dropdown ul.dropdown:before,
.giftregisty-dropdown ul.dropdown:after,
.wishlist-dropdown ul.dropdown:before,
.wishlist-dropdown ul.dropdown:after {
    border-bottom-style: solid;
    content: '';
    display: block;
    height: 0;
    position: absolute;
    width: 0;
}
.abs-dropdown-simple ul.dropdown:before,
.giftregisty-dropdown ul.dropdown:before,
.wishlist-dropdown ul.dropdown:before {
    border: 6px solid;
    border-color: transparent transparent #ffffff transparent;
    z-index: 99;
}
.abs-dropdown-simple ul.dropdown:after,
.giftregisty-dropdown ul.dropdown:after,
.wishlist-dropdown ul.dropdown:after {
    border: 7px solid;
    border-color: transparent transparent #bbbbbb transparent;
    z-index: 98;
}
.abs-dropdown-simple ul.dropdown:before,
.giftregisty-dropdown ul.dropdown:before,
.wishlist-dropdown ul.dropdown:before {
    left: 10px;
    top: -12px;
}
.abs-dropdown-simple ul.dropdown:after,
.giftregisty-dropdown ul.dropdown:after,
.wishlist-dropdown ul.dropdown:after {
    left: 9px;
    top: -14px;
}
.abs-dropdown-simple.active,
.giftregisty-dropdown.active,
.wishlist-dropdown.active {
    overflow: visible;
}
.abs-dropdown-simple.active ul.dropdown,
.giftregisty-dropdown.active ul.dropdown,
.wishlist-dropdown.active ul.dropdown {
    display: block;
}
.giftregisty-dropdown ul.items,
.wishlist-dropdown ul.items {
    padding: 6px 0;
}
.column .block-addbysku .qty .qty,
.table-giftregistry-items .field.qty .qty,
.block-giftregistry-shared-items .qty .qty,
.block-wishlist-info-items .input-text.qty,
.multicheckout .table-wrapper .col .qty .input-text,
.account .table-wrapper .data.table.wishlist .box-tocart .qty,
.products-grid.wishlist .product-item .box-tocart input.qty,
.bundle-options-container .input-text.qty,
.box-tocart .input-text.qty,
.account .data.table-return-items .qty .input-text,
.table.grouped .col.qty .control.qty .qty,
.sidebar .block-addbysku .fieldset .fields .field.qty .qty {
    text-align: center;
}
.block,
.block-banners,
.block-banners-inline {
    margin-bottom: 40px;
}
.sidebar .product-items .action.delete,
.table-comparison .cell.remove .action.delete,
.block-compare .action.delete,
.filter-current .action.remove {
    display: inline-block;
    text-decoration: none;
}
.abs-remove-button-for-blocks > span,
.sidebar .product-items .action.delete > span,
.table-comparison .cell.remove .action.delete > span,
.block-compare .action.delete > span,
.filter-current .action.remove > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.abs-remove-button-for-blocks:before,
.sidebar .product-items .action.delete:before,
.table-comparison .cell.remove .action.delete:before,
.block-compare .action.delete:before,
.filter-current .action.remove:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 12px;
    line-height: 15px;
    color: #757575;
    content: '\e616';
    font-family: 'luma-icons';
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.abs-remove-button-for-blocks:hover:before,
.sidebar .product-items .action.delete:hover:before,
.table-comparison .cell.remove .action.delete:hover:before,
.block-compare .action.delete:hover:before,
.filter-current .action.remove:hover:before {
    color: #494949;
}
.abs-remove-button-for-blocks:active:before,
.sidebar .product-items .action.delete:active:before,
.table-comparison .cell.remove .action.delete:active:before,
.block-compare .action.delete:active:before,
.filter-current .action.remove:active:before {
    color: #999999;
}
.product-item-name,
.product.name a {
    font-weight: 400;
}
.product-item-name > a,
.product.name a > a {
    color: #333333;
    text-decoration: none;
}
.abs-product-link > a:visited,
.product-item-name > a:visited,
.product.name a > a:visited {
    color: #333333;
    text-decoration: underline;
}
.abs-product-link > a:hover,
.product-item-name > a:hover,
.product.name a > a:hover {
    color: #333333;
    text-decoration: underline;
}
.abs-product-link > a:active,
.product-item-name > a:active,
.product.name a > a:active {
    color: #333333;
    text-decoration: underline;
}
.abs-like-link:visited {
    color: #1979c3;
    text-decoration: none;
}
.abs-like-link:hover {
    color: #006bb4;
    text-decoration: underline;
}
.abs-like-link:active {
    color: #85b84b;
    text-decoration: underline;
}
.form.giftregistry.items .actions-toolbar,
.form-new-agreement .actions-toolbar,
.fieldset .review-legend.legend,
.form.wishlist.items .actions-toolbar {
    margin-left: 0;
}
.abs-add-fields .action.remove,
.form-giftregistry-share .action.remove,
.form-giftregistry-edit .action.remove,
.form-add-invitations .action.remove,
.form-create-return .action.remove,
.form.send.friend .action.remove {
    line-height: normal;
    margin-left: 73%;
    position: absolute;
    top: 34px;
    width: auto;
}
.form-giftregistry-share .fieldset .field .control,
.form-giftregistry-edit .fieldset .field .control,
.form-add-invitations .fieldset .field .control,
.form-create-return .fieldset .field .control,
.form.send.friend .fieldset .field .control {
    width: 70%;
}
.form-giftregistry-share .fieldset .actions-toolbar > .secondary,
.form-giftregistry-edit .fieldset .actions-toolbar > .secondary,
.form-add-invitations .fieldset .actions-toolbar > .secondary,
.form-create-return .fieldset .actions-toolbar > .secondary,
.form.send.friend .fieldset .actions-toolbar > .secondary {
    float: left;
}
.form-giftregistry-share .fieldset .actions-toolbar > .secondary .action.add,
.form-giftregistry-edit .fieldset .actions-toolbar > .secondary .action.add,
.form-add-invitations .fieldset .actions-toolbar > .secondary .action.add,
.form-create-return .fieldset .actions-toolbar > .secondary .action.add,
.form.send.friend .fieldset .actions-toolbar > .secondary .action.add {
    margin-top: 30px;
}
.form-giftregistry-share .fieldset .fields .actions-toolbar,
.form-giftregistry-edit .fieldset .fields .actions-toolbar,
.form-add-invitations .fieldset .fields .actions-toolbar,
.form-create-return .fieldset .fields .actions-toolbar,
.form.send.friend .fieldset .fields .actions-toolbar {
    margin: 0;
}
.form-giftregistry-share .additional,
.form-giftregistry-edit .additional,
.form-add-invitations .additional,
.form-create-return .additional,
.form.send.friend .additional {
    margin-top: 55px;
    position: relative;
}
.form-giftregistry-share .message.notice,
.form-giftregistry-edit .message.notice,
.form-add-invitations .message.notice,
.form-create-return .message.notice,
.form.send.friend .message.notice {
    margin: 30px 0 0;
}
.order-details-items .order-items .order-gift-message:not(.expanded-content) {
    height: 0;
    visibility: hidden;
}
.page-product-giftcard .fieldset.giftcard > .legend,
.opc-wrapper .form-giftcard-account .field .label,
.block-wishlist-search-form .form-wishlist-search .fieldset > .legend,
.page-multiple-wishlist .page-title-wrapper .page-title,
.multicheckout .table-wrapper .col .label,
.multicheckout .block .methods-shipping .item-content .fieldset > .legend,
.block.newsletter .label,
.opc-wrapper .form-discount .field .label,
.products-grid.wishlist .product-item .comment-box .label,
.bundle-options-container .block-bundle-summary .product-image-container,
.bundle-options-container .block-bundle-summary .product.name,
.bundle-options-container .block-bundle-summary .stock,
.product-item-actions .actions-secondary > .action span,
.special-price .price-label,
.table-comparison .cell.label.remove span,
.table-comparison .cell.label.product span,
.block.related .field.choice .label,
.cart.table-wrapper .col.qty .label,
.minicart-wrapper .action.showcart .text,
.minicart-wrapper .action.showcart .counter-label,
.checkout-payment-method .field-select-billing > .label,
.checkout-payment-method .payments .legend,
.checkout-payment-method .ccard .legend,
.field.street .field.additional .label,
.page-product-downloadable .product-options-wrapper .fieldset .legend.links-title,
.filter-options-content .filter-count-label,
.block-reorder .product-item .label,
.action.skip:not(:focus),
.page-header .switcher .label,
.page-footer .switcher .label {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.minicart-items .product.options .toggle > span,
.shipping-policy-block.field-tooltip .field-tooltip-action span {
    clip: auto;
    height: auto;
    margin: 0;
    overflow: visible;
    position: static;
    width: auto;
}
.abs-add-clearfix:before,
.abs-add-clearfix:after,
.abs-add-fields .fieldset .actions-toolbar:before,
.abs-add-fields .fieldset .actions-toolbar:after,
.page-messages-container:before,
.page-messages-container:after,
.payment-method-braintree .hosted-date-wrap:before,
.payment-method-braintree .hosted-date-wrap:after,
.gift-options:before,
.gift-options:after,
.gift-item-block .content:before,
.gift-item-block .content:after,
.order-details-items .order-gift-message:before,
.order-details-items .order-gift-message:after,
.order-details-items .block-order-details-gift-message:before,
.order-details-items .block-order-details-gift-message:after,
.block-giftregistry-shared-items .product .item-options:before,
.block-giftregistry-shared-items .product .item-options:after,
.gift-wrapping-list:before,
.gift-wrapping-list:after,
.opc-wrapper .data.table .gift-wrapping:before,
.opc-wrapper .data.table .gift-wrapping:after,
.table-order-items .gift-wrapping:before,
.table-order-items .gift-wrapping:after,
.table-order-review .gift-wrapping:before,
.table-order-review .gift-wrapping:after,
.order-options .product:before,
.order-options .product:after,
.map-popup .map-info-price:before,
.map-popup .map-info-price:after,
.multicheckout .item-options:before,
.multicheckout .item-options:after,
.multicheckout .block:before,
.multicheckout .block:after,
.multicheckout .block-content:before,
.multicheckout .block-content:after,
.customer-review .product-details:before,
.customer-review .product-details:after,
.bundle-options-container .product-options-wrapper .field.choice .label:before,
.bundle-options-container .product-options-wrapper .field.choice .label:after,
.toolbar:before,
.toolbar:after,
.sidebar .product-items-images:before,
.sidebar .product-items-images:after,
.cart.table-wrapper .actions-toolbar:before,
.cart.table-wrapper .actions-toolbar:after,
.cart.table-wrapper .item-options:before,
.cart.table-wrapper .item-options:after,
.checkout-container:before,
.checkout-container:after,
.opc-estimated-wrapper:before,
.opc-estimated-wrapper:after,
.opc-wrapper .field.addresses:before,
.opc-wrapper .field.addresses:after,
.page-product-downloadable .product-add-form:before,
.page-product-downloadable .product-add-form:after,
.header.content:before,
.header.content:after,
.row:before,
.row:after,
.product-info-wrapper:before,
.product-info-wrapper:after,
.product-reviews-container .product-reviews-slider-container:before,
.product-reviews-container .product-reviews-slider-container:after,
.product.info.detailed .product-info-details-tab-details-description:before,
.product.info.detailed .product-info-details-tab-details-description:after,
.catalog-related-product-container:before,
.catalog-related-product-container:after,
.form-giftregistry-share .fieldset .actions-toolbar:before,
.form-giftregistry-share .fieldset .actions-toolbar:after,
.form-giftregistry-edit .fieldset .actions-toolbar:before,
.form-giftregistry-edit .fieldset .actions-toolbar:after,
.form-add-invitations .fieldset .actions-toolbar:before,
.form-add-invitations .fieldset .actions-toolbar:after,
.form-create-return .fieldset .actions-toolbar:before,
.form-create-return .fieldset .actions-toolbar:after,
.form.send.friend .fieldset .actions-toolbar:before,
.form.send.friend .fieldset .actions-toolbar:after {
    content: '';
    display: table;
}
.abs-add-clearfix:after,
.abs-add-fields .fieldset .actions-toolbar:after,
.page-messages-container:after,
.payment-method-braintree .hosted-date-wrap:after,
.gift-options:after,
.gift-item-block .content:after,
.order-details-items .order-gift-message:after,
.order-details-items .block-order-details-gift-message:after,
.block-giftregistry-shared-items .product .item-options:after,
.gift-wrapping-list:after,
.opc-wrapper .data.table .gift-wrapping:after,
.table-order-items .gift-wrapping:after,
.table-order-review .gift-wrapping:after,
.order-options .product:after,
.map-popup .map-info-price:after,
.multicheckout .item-options:after,
.multicheckout .block:after,
.multicheckout .block-content:after,
.customer-review .product-details:after,
.bundle-options-container .product-options-wrapper .field.choice .label:after,
.toolbar:after,
.sidebar .product-items-images:after,
.cart.table-wrapper .actions-toolbar:after,
.cart.table-wrapper .item-options:after,
.minicart-items .product-item > .product:after,
.checkout-container:after,
.opc-estimated-wrapper:after,
.opc-wrapper .field.addresses:after,
.page-product-downloadable .product-add-form:after,
.header.content:after,
.row:after,
.product-info-wrapper:after,
.product-reviews-container .product-reviews-slider-container:after,
.product.info.detailed .product-info-details-tab-details-description:after,
.catalog-related-product-container:after,
.form-giftregistry-share .fieldset .actions-toolbar:after,
.form-giftregistry-edit .fieldset .actions-toolbar:after,
.form-add-invitations .fieldset .actions-toolbar:after,
.form-create-return .fieldset .actions-toolbar:after,
.form.send.friend .fieldset .actions-toolbar:after {
    clear: both;
}
.abs-field-date .control,
.abs-field-tooltip,
.columns .column.main,
.columns .sidebar-main,
.columns .sidebar-additional,
.column .block-addbysku .sku,
.product-item,
.sidebar .product-items-images .product-item,
.search-autocomplete,
.cart-summary,
.opc-wrapper .shipping-address-item,
.shipping-policy-block.field-tooltip .field-tooltip-content,
.opc-block-summary,
.sidebar .block-addbysku .fieldset .fields .field.sku,
.block-event .slider-panel .slider .item .action.show,
.form.search.advanced .fields.range .field.date .control,
.field .control._with-tooltip {
    box-sizing: border-box;
}
.abs-navigation-icon:after {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 34px;
    line-height: 1.2;
    color: inherit;
    content: '\e622';
    font-family: 'luma-icons';
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.abs-split-button:before,
.abs-split-button:after {
    content: '';
    display: table;
}
.abs-split-button:after {
    clear: both;
}
.abs-split-button .action.split {
    float: left;
    margin: 0;
}
.abs-split-button .action.toggle {
    float: right;
    margin: 0;
}
.abs-split-button button.action.split {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0;
}
.abs-split-button button + .action.toggle {
    border-bottom-left-radius: 0;
    border-left: 0;
    border-top-left-radius: 0;
}
.abs-split-button .action.toggle {
    display: inline-block;
    text-decoration: none;
}
.abs-split-button .action.toggle > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.abs-split-button .action.toggle:after {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 22px;
    line-height: inherit;
    color: inherit;
    content: '\e622';
    font-family: 'luma-icons';
    margin: 0;
    vertical-align: top;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.abs-split-button .action.toggle:hover:after {
    color: inherit;
}
.abs-split-button .action.toggle:active:after {
    color: inherit;
}
.abs-split-button .action.toggle.active {
    display: inline-block;
    text-decoration: none;
}
.abs-split-button .action.toggle.active > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.abs-split-button .action.toggle.active:after {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 22px;
    line-height: inherit;
    color: inherit;
    content: '\e621';
    font-family: 'luma-icons';
    margin: 0;
    vertical-align: top;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.abs-split-button .action.toggle.active:hover:after {
    color: inherit;
}
.abs-split-button .action.toggle.active:active:after {
    color: inherit;
}
.abs-split-button .items {
    margin: 0;
    padding: 0;
    list-style: none none;
    background: #ffffff;
    border: 1px solid #bbbbbb;
    margin-top: 4px;
    min-width: 100%;
    z-index: 100;
    box-sizing: border-box;
    display: none;
    position: absolute;
    top: 100%;
    box-shadow: 0 3px 3px rgba(0, 0, 0, 0.15);
}
.abs-split-button .items li {
    margin: 0;
    padding: 3px 5px;
}
.abs-split-button .items li:hover {
    background: #e8e8e8;
    cursor: pointer;
}
.abs-split-button .items:before,
.abs-split-button .items:after {
    border-bottom-style: solid;
    content: '';
    display: block;
    height: 0;
    position: absolute;
    width: 0;
}
.abs-split-button .items:before {
    border: 6px solid;
    border-color: transparent transparent #ffffff transparent;
    z-index: 99;
}
.abs-split-button .items:after {
    border: 7px solid;
    border-color: transparent transparent #bbbbbb transparent;
    z-index: 98;
}
.abs-split-button .items:before {
    right: 10px;
    top: -12px;
}
.abs-split-button .items:after {
    right: 9px;
    top: -14px;
}
.abs-split-button.active {
    overflow: visible;
}
.abs-split-button.active .items {
    display: block;
}
.bundle-options-container .block-bundle-summary .product-addto-links > .action,
.product-item-actions .actions-secondary > .action,
.product-social-links .action.tocompare {
    color: #999999;
    font-weight: 600;
    letter-spacing: .05em;
    text-transform: uppercase;
    display: inline-block;
    text-decoration: none;
}
.abs-actions-addto:before,
.bundle-options-container .block-bundle-summary .product-addto-links > .action:before,
.product-item-actions .actions-secondary > .action:before,
.product-social-links .action.tocompare:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 16px;
    line-height: 16px;
    color: inherit;
    font-family: 'luma-icons';
    margin: -2px 5px 0 0;
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.bundle-options-container .block-bundle-summary .product-addto-links > .action:hover,
.product-item-actions .actions-secondary > .action:hover,
.product-social-links .action.tocompare:hover {
    color: #333333;
    text-decoration: none;
}
.bundle-options-container .block-bundle-summary .product-addto-links > .action:before,
.product-item-actions .actions-secondary > .action:before,
.product-social-links .action.tocompare:before {
    width: 18px;
}
.bundle-options-container .block-bundle-summary .box-tocart,
.box-tocart {
    margin: 0 0 30px;
}
.abs-cart-block > .title,
.minicart-items .product .toggle,
.abs-discount-block .block > .title,
.cart-summary .block > .title,
.paypal-review-discount .block .block > .title,
.cart-discount .block > .title,
.checkout-cart-index .delivery-comment .newmessagetitle{
    border-top: 1px solid #cccccc;
    cursor: pointer;
    margin-bottom: 0;
    position: relative;
    padding: 5px 40px 5px 0;
    display: block;
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
}
.abs-cart-block > .title:after,
.minicart-items .product .toggle:after,
.abs-discount-block .block > .title:after,
.cart-summary .block > .title:after,
.paypal-review-discount .block .block > .title:after,
.cart-discount .block > .title:after {
    position: absolute;
    right: 20px;
    top: 10px;
}
.abs-toggling-title > span,
.abs-cart-block > .title > span,
.minicart-items .product .toggle > span,
.abs-discount-block .block > .title > span,
.cart-summary .block > .title > span,
.paypal-review-discount .block .block > .title > span,
.cart-discount .block > .title > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.abs-toggling-title:after,
.abs-cart-block > .title:after,
.minicart-items .product .toggle:after,
.abs-discount-block .block > .title:after,
.cart-summary .block > .title:after,
.paypal-review-discount .block .block > .title:after,
.cart-discount .block > .title:after {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 12px;
    line-height: 12px;
    color: inherit;
    content: '\e622';
    font-family: 'luma-icons';
    margin: 3px 0 0;
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.abs-discount-block .block,
.cart-summary .block,
.paypal-review-discount .block .block,
.cart-discount .block {
    margin: 0;
}
.abs-discount-block .block > .title:after,
.cart-summary .block > .title:after,
.paypal-review-discount .block .block > .title:after,
.cart-discount .block > .title:after {
    color: #858585;
}
.abs-discount-block .block .content,
.cart-summary .block .content,
.paypal-review-discount .block .block .content,
.cart-discount .block .content {
    display: none;
    padding: 5px 15px 23px;
    position: relative;
}
.abs-cart-block.active > .title:after,
.abs-discount-block .block.active > .title:after,
.cart-summary .block.active > .title:after,
.paypal-review-discount .block .block.active > .title:after,
.cart-discount .block.active > .title:after {
    content: '\e621';
}
.abs-discount-block .block.active .content,
.cart-summary .block.active .content,
.paypal-review-discount .block .block.active .content,
.cart-discount .block.active .content {
    display: block;
}
.multicheckout .table-wrapper .cart-price {
    color: #7d7d7d;
    font-size: 1.6rem;
    font-weight: 700;
}
.multicheckout .table-wrapper .product-item-name {
    font-size: 1.8rem;
    font-weight: 300;
    margin: 0;
}
.abs-account-blocks .block-title,
.form-giftregistry-search .legend,
.block-wishlist-search-form .block-title,
.multicheckout .block-title,
.multicheckout .block-content .title,
.account .legend,
.multicheckout .block-title,
.paypal-review .block .block-title,
.magento-rma-guest-returns .column.main .block:not(.widget) .block-title,
.account .column.main .block:not(.widget) .block-title,
[class^='sales-guest-'] .column.main .block:not(.widget) .block-title,
.sales-guest-view .column.main .block:not(.widget) .block-title {
    border-bottom: 1px solid #c6c6c6;
    margin-bottom: 25px;
    padding-bottom: 10px;
}
.abs-account-blocks .block-title > strong,
.abs-account-blocks .block-title > span,
.form-giftregistry-search .legend > strong,
.form-giftregistry-search .legend > span,
.block-wishlist-search-form .block-title > strong,
.block-wishlist-search-form .block-title > span,
.multicheckout .block-title > strong,
.multicheckout .block-title > span,
.multicheckout .block-content .title > strong,
.multicheckout .block-content .title > span,
.account .legend > strong,
.account .legend > span,
.multicheckout .block-title > strong,
.multicheckout .block-title > span,
.paypal-review .block .block-title > strong,
.paypal-review .block .block-title > span,
.magento-rma-guest-returns .column.main .block:not(.widget) .block-title > strong,
.magento-rma-guest-returns .column.main .block:not(.widget) .block-title > span,
.account .column.main .block:not(.widget) .block-title > strong,
.account .column.main .block:not(.widget) .block-title > span,
[class^='sales-guest-'] .column.main .block:not(.widget) .block-title > strong,
[class^='sales-guest-'] .column.main .block:not(.widget) .block-title > span,
.sales-guest-view .column.main .block:not(.widget) .block-title > strong,
.sales-guest-view .column.main .block:not(.widget) .block-title > span {
    font-size: 2.2rem;
    font-weight: 300;
}
.block-addresses-list address,
.box-billing-address .box-content,
.box-shipping-address .box-content,
.box-information .box-content,
.box-newsletter .box-content {
    line-height: 2.4rem;
}
.order-details-items tfoot td {
    background: #f5f5f5;
}
.cart.table-wrapper .col.price .price-including-tax,
.cart.table-wrapper .col.price .price-excluding-tax,
.cart.table-wrapper .col.subtotal .price-including-tax,
.cart.table-wrapper .col.subtotal .price-excluding-tax,
.cart.table-wrapper .col.msrp .price-including-tax,
.cart.table-wrapper .col.msrp .price-excluding-tax,
.order-details-items .col.price .price-including-tax,
.order-details-items .col.price .price-excluding-tax,
.order-details-items .col.subtotal .price-including-tax,
.order-details-items .col.subtotal .price-excluding-tax {
    font-size: 1.8rem;
    display: block;
    line-height: 1;
    white-space: nowrap;
}
.cart.table-wrapper .col.price .price-including-tax .price,
.cart.table-wrapper .col.price .price-excluding-tax .price,
.cart.table-wrapper .col.subtotal .price-including-tax .price,
.cart.table-wrapper .col.subtotal .price-excluding-tax .price,
.cart.table-wrapper .col.msrp .price-including-tax .price,
.cart.table-wrapper .col.msrp .price-excluding-tax .price,
.order-details-items .col.price .price-including-tax .price,
.order-details-items .col.price .price-excluding-tax .price,
.order-details-items .col.subtotal .price-including-tax .price,
.order-details-items .col.subtotal .price-excluding-tax .price {
    font-weight: 500;
}
.gift-summary .price-including-tax,
.gift-summary .price-excluding-tax,
.gift-options .price-including-tax,
.gift-options .price-excluding-tax,
.bundle-options-container .product-options-wrapper .fieldset-bundle-options .field.choice .price-notice .price-including-tax,
.bundle-options-container .product-options-wrapper .fieldset-bundle-options .field.choice .price-notice .price-excluding-tax,
.bundle-options-container .product-options-wrapper .field.choice .price-notice .price-including-tax,
.bundle-options-container .product-options-wrapper .field.choice .price-notice .price-excluding-tax,
.cart-summary .price-including-tax,
.cart-summary .price-excluding-tax {
    font-size: 1.4rem;
    display: inline-block;
}
.gift-summary .price-including-tax + .price-excluding-tax,
.gift-options .price-including-tax + .price-excluding-tax,
.bundle-options-container .product-options-wrapper .fieldset-bundle-options .field.choice .price-notice .price-including-tax + .price-excluding-tax,
.bundle-options-container .product-options-wrapper .field.choice .price-notice .price-including-tax + .price-excluding-tax,
.cart-summary .price-including-tax + .price-excluding-tax {
    font-size: 1.1rem;
    display: inline-block;
}
.gift-summary .price-including-tax + .price-excluding-tax:before,
.gift-options .price-including-tax + .price-excluding-tax:before,
.bundle-options-container .product-options-wrapper .fieldset-bundle-options .field.choice .price-notice .price-including-tax + .price-excluding-tax:before,
.bundle-options-container .product-options-wrapper .field.choice .price-notice .price-including-tax + .price-excluding-tax:before,
.cart-summary .price-including-tax + .price-excluding-tax:before {
    content: '(' attr(data-label) ': ';
}
.gift-summary .price-including-tax + .price-excluding-tax:after,
.gift-options .price-including-tax + .price-excluding-tax:after,
.bundle-options-container .product-options-wrapper .fieldset-bundle-options .field.choice .price-notice .price-including-tax + .price-excluding-tax:after,
.bundle-options-container .product-options-wrapper .field.choice .price-notice .price-including-tax + .price-excluding-tax:after,
.cart-summary .price-including-tax + .price-excluding-tax:after {
    content: ')';
}
.price-including-tax .cart-tax-total,
.price-excluding-tax .cart-tax-total,
.cart.table-wrapper .cart-tax-total,
.order-details-items .cart-tax-total {
    cursor: pointer;
    padding-right: 10px;
    position: relative;
    display: inline-block;
    text-decoration: none;
}
.abs-tax-total:after,
.price-including-tax .cart-tax-total:after,
.price-excluding-tax .cart-tax-total:after,
.cart.table-wrapper .cart-tax-total:after,
.order-details-items .cart-tax-total:after {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 8px;
    line-height: 8px;
    color: inherit;
    content: '\e622';
    font-family: 'luma-icons';
    margin: 3px 0 0 0;
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.price-including-tax .cart-tax-total:after,
.price-excluding-tax .cart-tax-total:after,
.cart.table-wrapper .cart-tax-total:after,
.order-details-items .cart-tax-total:after {
    position: absolute;
    right: -2px;
    top: 4px;
}
.abs-tax-total-expanded:after,
.price-including-tax .cart-tax-total-expanded:after,
.price-excluding-tax .cart-tax-total-expanded:after,
.cart.table-wrapper .cart-tax-total-expanded:after,
.order-details-items .cart-tax-total-expanded:after {
    content: '\e621';
}
.review-ratings .rating-summary {
    display: table-row;
}
.review-ratings .rating-label {
    display: table-cell;
    padding-bottom: 5px;
    padding-right: 25px;
    padding-top: 1px;
    vertical-align: top;
}
.review-ratings .rating-result {
    display: table-cell;
    vertical-align: top;
}
.block-addresses-list .items.addresses .item.actions .action:after,
.account .data.table .col.actions .action:after,
[class^='sales-guest-'] .data.table .col.actions .action:after,
.sales-guest-view .data.table .col.actions .action:after,
.block .box-actions .action:after {
    border-left: 1px solid #a6a6a6;
    content: '';
    display: inline-block;
    height: 12px;
    margin: 0 10px;
    vertical-align: -1px;
}
.block-addresses-list .items.addresses .item.actions .action:last-child:after,
.account .data.table .col.actions .action:last-child:after,
[class^='sales-guest-'] .data.table .col.actions .action:last-child:after,
.sales-guest-view .data.table .col.actions .action:last-child:after,
.block .box-actions .action:last-child:after {
    display: none;
}
.multicheckout .block-title > .action,
.paypal-review .block .block-title > .action,
.magento-rma-guest-returns .column.main .block:not(.widget) .block-title > .action,
.account .column.main .block:not(.widget) .block-title > .action,
[class^='sales-guest-'] .column.main .block:not(.widget) .block-title > .action,
.sales-guest-view .column.main .block:not(.widget) .block-title > .action {
    margin-left: 15px;
}
.multicheckout .box-title,
.paypal-review .block .box-title,
.magento-rma-guest-returns .column.main .block:not(.widget) .box-title,
.account .column.main .block:not(.widget) .box-title,
[class^='sales-guest-'] .column.main .block:not(.widget) .box-title,
.sales-guest-view .column.main .block:not(.widget) .box-title {
    display: inline-block;
    margin: 0 0 10px;
}
.multicheckout .box-title > span,
.paypal-review .block .box-title > span,
.magento-rma-guest-returns .column.main .block:not(.widget) .box-title > span,
.account .column.main .block:not(.widget) .box-title > span,
[class^='sales-guest-'] .column.main .block:not(.widget) .box-title > span,
.sales-guest-view .column.main .block:not(.widget) .box-title > span {
    font-size: 1.6rem;
}
.multicheckout .box-title > .action,
.paypal-review .block .box-title > .action,
.magento-rma-guest-returns .column.main .block:not(.widget) .box-title > .action,
.account .column.main .block:not(.widget) .box-title > .action,
[class^='sales-guest-'] .column.main .block:not(.widget) .box-title > .action,
.sales-guest-view .column.main .block:not(.widget) .box-title > .action {
    font-weight: 400;
    margin-left: 10px;
}
.order-details-items .order-gift-message .label:after,
.order-details-items .block-order-details-gift-message .label:after,
.block-wishlist-management .wishlist-select .wishlist-name-label:after,
.block-minicart .subtotal .label:after,
.minicart-items .details-qty .label:after,
.minicart-items .price-minicart .label:after {
    content: ': ';
}
.abs-dropdown-items-new .action.new,
.wishlist.split.button .action.new,
.wishlist-dropdown .action.new {
    display: inline-block;
    text-decoration: none;
}
.abs-icon-add:before,
.abs-dropdown-items-new .action.new:before,
.wishlist.split.button .action.new:before,
.wishlist-dropdown .action.new:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 10px;
    line-height: 10px;
    color: inherit;
    content: '\e61c';
    font-family: 'luma-icons';
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.wishlist.split.button .items .item:last-child:hover,
.wishlist-dropdown .items .item:last-child:hover {
    background: #e8e8e8;
}
.wishlist.split.button .action.new:before,
.wishlist-dropdown .action.new:before {
    margin-left: -17px;
    margin-right: 5px;
}
.abs-sidebar-totals .table-caption,
.page-product-giftcard .fieldset.giftcard > .legend + br,
.order-details-items .order-items .order-gift-message .action.close,
.multicheckout .block .methods-shipping .item-content .fieldset > .legend + br,
.multicheckout .hidden,
[class^='multishipping-'] .nav-sections,
[class^='multishipping-'] .nav-toggle,
.sidebar .subtitle,
.cart-summary .block .fieldset.estimate > .legend,
.cart-summary .block .fieldset.estimate > .legend + br,
.minicart-items .product-item-details .weee[data-label] .label,
.no-display,
.cart-totals .table-caption,
.opc-block-summary .table-totals .table-caption {
    display: none;
}
.return-status,
.order-status {
    border: 2px solid #cccccc;
    border-radius: 3px;
    display: inline-block;
    margin: 3px 0 0;
    padding: 2px 10px;
    text-transform: uppercase;
    vertical-align: top;
}
.account .page-main .page-title-wrapper .page-title,
[class^='sales-guest-'] .page-main .page-title-wrapper .page-title,
.sales-guest-view .page-main .page-title-wrapper .page-title,
.magento-rma-guest-returns .page-main .page-title-wrapper .page-title {
    margin-right: 25px;
}
.account .page-main .page-title-wrapper .order-date,
[class^='sales-guest-'] .page-main .page-title-wrapper .order-date,
.sales-guest-view .page-main .page-title-wrapper .order-date,
.magento-rma-guest-returns .page-main .page-title-wrapper .order-date {
    font-size: 1.6rem;
    margin-bottom: 10px;
}
.account .page-main .page-title-wrapper .order-date .label,
[class^='sales-guest-'] .page-main .page-title-wrapper .order-date .label,
.sales-guest-view .page-main .page-title-wrapper .order-date .label,
.magento-rma-guest-returns .page-main .page-title-wrapper .order-date .label {
    display: none;
}
.account .column.main .order-details-items .table-wrapper .data.table,
[class^='sales-guest-'] .column.main .order-details-items .table-wrapper .data.table,
.sales-guest-view .column.main .order-details-items .table-wrapper .data.table,
.magento-rma-guest-returns .column.main .order-details-items .table-wrapper .data.table {
    border: none;
}
.abs-table-striped > tbody > tr:nth-child(even) > td,
.abs-table-striped > tbody > tr:nth-child(even) > th,
.account .column.main .order-details-items .table-wrapper .data.table > tbody > tr:nth-child(even) > td,
.account .column.main .order-details-items .table-wrapper .data.table > tbody > tr:nth-child(even) > th,
[class^='sales-guest-'] .column.main .order-details-items .table-wrapper .data.table > tbody > tr:nth-child(even) > td,
[class^='sales-guest-'] .column.main .order-details-items .table-wrapper .data.table > tbody > tr:nth-child(even) > th,
.sales-guest-view .column.main .order-details-items .table-wrapper .data.table > tbody > tr:nth-child(even) > td,
.sales-guest-view .column.main .order-details-items .table-wrapper .data.table > tbody > tr:nth-child(even) > th,
.magento-rma-guest-returns .column.main .order-details-items .table-wrapper .data.table > tbody > tr:nth-child(even) > td,
.magento-rma-guest-returns .column.main .order-details-items .table-wrapper .data.table > tbody > tr:nth-child(even) > th {
    background: #f6f6f6;
}
.abs-table-striped > thead > tr > th,
.abs-table-striped > tbody > tr > th,
.abs-table-striped > tfoot > tr > th,
.abs-table-striped > thead > tr > td,
.abs-table-striped > tbody > tr > td,
.abs-table-striped > tfoot > tr > td,
.account .column.main .order-details-items .table-wrapper .data.table > thead > tr > th,
.account .column.main .order-details-items .table-wrapper .data.table > tbody > tr > th,
.account .column.main .order-details-items .table-wrapper .data.table > tfoot > tr > th,
.account .column.main .order-details-items .table-wrapper .data.table > thead > tr > td,
.account .column.main .order-details-items .table-wrapper .data.table > tbody > tr > td,
.account .column.main .order-details-items .table-wrapper .data.table > tfoot > tr > td,
[class^='sales-guest-'] .column.main .order-details-items .table-wrapper .data.table > thead > tr > th,
[class^='sales-guest-'] .column.main .order-details-items .table-wrapper .data.table > tbody > tr > th,
[class^='sales-guest-'] .column.main .order-details-items .table-wrapper .data.table > tfoot > tr > th,
[class^='sales-guest-'] .column.main .order-details-items .table-wrapper .data.table > thead > tr > td,
[class^='sales-guest-'] .column.main .order-details-items .table-wrapper .data.table > tbody > tr > td,
[class^='sales-guest-'] .column.main .order-details-items .table-wrapper .data.table > tfoot > tr > td,
.sales-guest-view .column.main .order-details-items .table-wrapper .data.table > thead > tr > th,
.sales-guest-view .column.main .order-details-items .table-wrapper .data.table > tbody > tr > th,
.sales-guest-view .column.main .order-details-items .table-wrapper .data.table > tfoot > tr > th,
.sales-guest-view .column.main .order-details-items .table-wrapper .data.table > thead > tr > td,
.sales-guest-view .column.main .order-details-items .table-wrapper .data.table > tbody > tr > td,
.sales-guest-view .column.main .order-details-items .table-wrapper .data.table > tfoot > tr > td,
.magento-rma-guest-returns .column.main .order-details-items .table-wrapper .data.table > thead > tr > th,
.magento-rma-guest-returns .column.main .order-details-items .table-wrapper .data.table > tbody > tr > th,
.magento-rma-guest-returns .column.main .order-details-items .table-wrapper .data.table > tfoot > tr > th,
.magento-rma-guest-returns .column.main .order-details-items .table-wrapper .data.table > thead > tr > td,
.magento-rma-guest-returns .column.main .order-details-items .table-wrapper .data.table > tbody > tr > td,
.magento-rma-guest-returns .column.main .order-details-items .table-wrapper .data.table > tfoot > tr > td {
    border: none;
}
.abs-table-striped > thead > tr > th,
.abs-table-striped > thead > tr > td,
.account .column.main .order-details-items .table-wrapper .data.table > thead > tr > th,
.account .column.main .order-details-items .table-wrapper .data.table > thead > tr > td,
[class^='sales-guest-'] .column.main .order-details-items .table-wrapper .data.table > thead > tr > th,
[class^='sales-guest-'] .column.main .order-details-items .table-wrapper .data.table > thead > tr > td,
.sales-guest-view .column.main .order-details-items .table-wrapper .data.table > thead > tr > th,
.sales-guest-view .column.main .order-details-items .table-wrapper .data.table > thead > tr > td,
.magento-rma-guest-returns .column.main .order-details-items .table-wrapper .data.table > thead > tr > th,
.magento-rma-guest-returns .column.main .order-details-items .table-wrapper .data.table > thead > tr > td {
    border-bottom: 1px solid #cccccc;
}
.block-wishlist .counter,
.block-compare .counter {
    color: #6d6d6d;
    font-size: 1.2rem;
    white-space: nowrap;
}
.block-wishlist .counter:before,
.block-compare .counter:before {
    content: '(';
}
.block-wishlist .counter:after,
.block-compare .counter:after {
    content: ')';
}
.block-compare .block-title,
.block-reorder .block-title,
.widget .block-title {
    margin: 0 0 20px;
}
.block-compare .block-title strong,
.block-reorder .block-title strong,
.widget .block-title strong {
    font-size: 18px;
    font-weight: 300;
}
.block-cart-failed,
.cart-container .form-cart {
    margin-bottom: 20px;
}
.block-cart-failed .actions.main .continue,
.block-cart-failed .actions.main .clear,
.cart-container .form-cart .actions.main .continue,
.cart-container .form-cart .actions.main .clear {
    display: none;
}
.form.search.advanced .fields.range .field.date .control {
    position: relative;
}
.abs-field-date input,
.field.date ._has-datepicker,
.field-dob ._has-datepicker,
.form-giftregistry-search .fields-specific-options .datetime-picker,
.form.search.advanced .fields.range .field.date input {
    margin-right: 10px;
    width: calc(100% - 33px);
}
.field .control._with-tooltip {
    position: relative;
}
.field .control._with-tooltip input {
    margin-right: 10px;
    width: calc(100% - 36px);
}
.checkout-index-index .modal-popup .field-tooltip .field-tooltip-content,
.shipping-policy-block.field-tooltip .field-tooltip-content {
    right: -10px;
    top: 40px;
    left: auto;
}
.checkout-index-index .modal-popup .field-tooltip .field-tooltip-content:before,
.checkout-index-index .modal-popup .field-tooltip .field-tooltip-content:after,
.shipping-policy-block.field-tooltip .field-tooltip-content:before,
.shipping-policy-block.field-tooltip .field-tooltip-content:after {
    border: 10px solid transparent;
    height: 0;
    width: 0;
    margin-top: -21px;
    right: 10px;
    left: auto;
    top: 0;
}
.checkout-index-index .modal-popup .field-tooltip .field-tooltip-content:before,
.shipping-policy-block.field-tooltip .field-tooltip-content:before {
    border-bottom-color: #666666;
}
.checkout-index-index .modal-popup .field-tooltip .field-tooltip-content:after,
.shipping-policy-block.field-tooltip .field-tooltip-content:after {
    border-bottom-color: #f5f5f5;
    top: 1px;
}
.opc-wrapper .step-title,
.opc-block-summary > .title,
.checkout-agreements-items .checkout-agreements-item-title {
    font-weight: 500;
    font-size: 20px;
}
.cart-totals {
    border-top: 1px solid #cccccc;
    padding-top: 10px;
}
.cart-totals tbody .mark,
.cart-totals tfoot .mark,
.opc-block-summary .table-totals tbody .mark,
.opc-block-summary .table-totals tfoot .mark {
    border: 0;
    font-weight: 400;
    padding: 6px 0;
}
.cart-totals tbody .amount,
.cart-totals tfoot .amount,
.opc-block-summary .table-totals tbody .amount,
.opc-block-summary .table-totals tfoot .amount {
    border: 0;
    font-weight: 400;
    padding: 6px 0 6px 14px;
    text-align: right;
    white-space: nowrap;
}
.cart-totals .grand th,
.cart-totals .grand td,
.opc-block-summary .table-totals .grand th,
.opc-block-summary .table-totals .grand td {
    padding: 0;
}
.cart-totals .grand strong,
.opc-block-summary .table-totals .grand strong {
    display: inline-block;
    font-weight: 600;
    padding: 3px 0 0;
}
.cart-totals .grand .mark,
.opc-block-summary .table-totals .grand .mark {
    font-size: 1.8rem;

    height: max-content;
}
.cart-totals .grand .amount,
.opc-block-summary .table-totals .grand .amount {
    font-size: 1.8rem;
}

.opc-block-summary .table-totals .grand .mark, .opc-block-summary .table-totals .grand .amount {
    padding: 10px 10px 14px 10px;
    border-top: 1px solid #ccc;
}
.cart-totals .msrp,
.opc-block-summary .table-totals .msrp {
    margin-bottom: 10px;
}
.cart-totals .totals-tax-summary .mark,
.cart-totals .totals-tax-summary .amount,
.opc-block-summary .table-totals .totals-tax-summary .mark,
.opc-block-summary .table-totals .totals-tax-summary .amount {
    border-bottom: 1px solid #cccccc;
    border-top: 1px solid #cccccc;
    cursor: pointer;
}
.cart-totals .totals-tax-summary .amount .price,
.opc-block-summary .table-totals .totals-tax-summary .amount .price {
    padding-right: 20px;
    position: relative;
    display: inline-block;
    text-decoration: none;
}
.abs-sidebar-totals .totals-tax-summary .amount .price > span,
.cart-totals .totals-tax-summary .amount .price > span,
.opc-block-summary .table-totals .totals-tax-summary .amount .price > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.abs-sidebar-totals .totals-tax-summary .amount .price:after,
.cart-totals .totals-tax-summary .amount .price:after,
.opc-block-summary .table-totals .totals-tax-summary .amount .price:after {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 12px;
    line-height: 12px;
    color: inherit;
    content: '\e622';
    font-family: 'luma-icons';
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.cart-totals .totals-tax-summary .amount .price:after,
.opc-block-summary .table-totals .totals-tax-summary .amount .price:after {
    position: absolute;
    right: 3px;
    top: 3px;
}
.cart-totals .totals-tax-summary.expanded .mark,
.cart-totals .totals-tax-summary.expanded .amount,
.opc-block-summary .table-totals .totals-tax-summary.expanded .mark,
.opc-block-summary .table-totals .totals-tax-summary.expanded .amount {
    border-bottom: 0;
}
.abs-sidebar-totals .totals-tax-summary.expanded .amount .price:after,
.cart-totals .totals-tax-summary.expanded .amount .price:after,
.opc-block-summary .table-totals .totals-tax-summary.expanded .amount .price:after {
    content: '\e621';
}
.cart-totals .totals-tax-details,
.opc-block-summary .table-totals .totals-tax-details {
    border-bottom: 1px solid #cccccc;
    display: none;
}
.cart-totals .totals-tax-details.shown,
.opc-block-summary .table-totals .totals-tax-details.shown {
    display: table-row;
}
.cart-totals .table-wrapper,
.opc-block-summary .table-totals .table-wrapper {
    margin-bottom: 0;
}
.paypal-review-discount .block .block > .title strong,
.cart-discount .block > .title strong {
    color: #1979c3;
    font-weight: 400;
}
.paypal-review-discount .block .actions-toolbar,
.cart-discount .actions-toolbar {
    display: table-cell;
    vertical-align: top;
    width: 1%;
}
.paypal-review-discount .block .actions-toolbar .action.primary,
.paypal-review-discount .block .actions-toolbar .action.cancel,
.cart-discount .actions-toolbar .action.primary,
.cart-discount .actions-toolbar .action.cancel {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
    margin: 0 0 0 -1px;
    white-space: nowrap;
    width: auto;
}
.paypal-review-discount .block .actions-toolbar .secondary,
.cart-discount .actions-toolbar .secondary {
    bottom: 5px;
    left: 15px;
    position: absolute;
}
.paypal-review-discount .block .action.check,
.cart-discount .action.check {
    font-weight: 400;
}
.paypal-review-discount .block .fieldset,
.cart-discount .fieldset {
    display: table;
    width: 100%;
}
.paypal-review-discount .block .fieldset > .field > .label,
.cart-discount .fieldset > .field > .label {
    display: none;
}
.paypal-review-discount .block .field,
.cart-discount .field {
    display: table-cell;
}
html {
    font-size: 10px;
}
html {
    background: white;
    color: initial;
}
html.blank {
    background: #ffffff;
}
body {
    color: #636d70;
    font: 400 17px / 26px 'futura-pt';
}
/**********************************************************************************************************************
   * TEXT
   **********************************************************************************************************************/
p {
    margin: 0;
}

strong,
b {
    font-weight: 500;
}

.page-main ul.list-unstyled,
.modal-popup ul.list-unstyled,
.page-main ul.items,
.modal-popup ul.items {
    padding: 0;
    list-style: none;
}
.page-main ul.list-unstyled ul,
.modal-popup ul.list-unstyled ul,
.page-main ul.items ul,
.modal-popup ul.items ul {
    list-style: none;
}
.page-main ul.list-unstyled ul ul,
.modal-popup ul.list-unstyled ul ul,
.page-main ul.items ul ul,
.modal-popup ul.items ul ul {
    list-style: none;
}
.block .title {
    margin: 0 0 10px 0;
}
.block .title strong {
    font-size: 3rem;
    line-height: 3rem;
}
/**********************************************************************************************************************
   * HEADLINES
   **********************************************************************************************************************/
h1,
h2,
h3 {
    color: #2f3943;
}
h4,
h5,
h6 {
    color: #2f3943;
}
h1 {
    font-size: 4rem;
    line-height: 4rem;
    margin: 27px 0 17px 0;
}
h2 {
    font-size: 3.4rem;
    line-height: 3.4rem;
    margin: 48px 0 16px 0;
}
h3 {
    font-size: 3rem;
    line-height: 3rem;
    margin: 50px 0 16px 0;
}
h4 {
    font-size: 2.4rem;
    line-height: 2.6rem;
    margin: 50px 0 16px 0;
}
h5 {
    font-size: 2rem;
    line-height: 2.2rem;
    margin: 50px 0 16px 0;
}
h6 {
    font-size: 1.8rem;
    line-height: 2rem;
    margin: 50px 0 16px 0;
}
/**********************************************************************************************************************
   * LINK
   **********************************************************************************************************************/
a {
    color: #2f3943;
    transition: color 0.4s;
    position: relative;
    text-decoration: none;
}
a:visited {
    color: #2f3943;
}
a:hover,
a:focus {
    color: #85b84b;
    text-decoration: none;
}
a[href]:after {
    content: normal;
}
/**********************************************************************************************************************
   * HR
   **********************************************************************************************************************/
hr {
    height: 1px;
    border: none;
    margin: 20px 0;
    background: #e5e5e5;
}
.modal-custom button.action-close,
.modal-popup button.action-close {
    height: auto;
}
.modal-custom button.action-close::after,
.modal-popup button.action-close::after {
    display: none;
}
a.xinfo-modal-button span {
    margin-left: 22px;
}
a.xinfo-modal-button::before {
    content: "\e623";
    font-family: 'luma-icons';
    border: none;
    display: inline-block;
    margin-right: 10px;
    cursor: pointer;
    text-align: center;
    line-height: 1.9rem;
    color: #2f3943;
    font-weight: 400;
    font-size: 1.8rem;
    transform: rotate(0deg);
    width: 20px;
    height: 20px;
    transition: all 0.4s;
}
a.xinfo-modal-button:hover::before {
    border-bottom: none;
    color: #85b84b;
}
.control.quantity {
    display: inline-block;
    position: relative;
    margin-left: 35px;
}
.control.quantity a.increaseQty,
.control.quantity a.decreaseQty {
    width: 33px;
    height: 33px;
    position: absolute;
    background: #f5f5f5;
    border-radius: 15px;
    display: block;
    top: 8px;
    box-shadow: 0 0 3px gray;
    cursor: pointer;
}
.control.quantity a.increaseQty::before,
.control.quantity a.decreaseQty::before {
    border-bottom: 0;
    font-size: 1.4rem;
    font-family: "luma-icons";
    left: 9px;
    top: 4px;
}
.control.quantity a.increaseQty:hover::before,
.control.quantity a.decreaseQty:hover::before {
    border-bottom: 0;
}
.control.quantity .input-text.qty {
    width: 70px;
}
.control.quantity .increaseQty {
    right: -23px;
}
.control.quantity .increaseQty::before {
    content: "\e61c";
}
.control.quantity .decreaseQty {
    left: -23px;
}
.control.quantity .decreaseQty::before {
    content: "\e60f";
}
/** Grauen Balken entfernen */
.page-wrapper {
    background: white;
    transition: 0.4s ease all;
}
/**********************************************************************************************************************
   * Stock-Status
   **********************************************************************************************************************/
.stock.available,
.stock.unavailable {
    font-weight: 400;
    text-transform: none;
}
.stock.available > span,
.stock.unavailable > span {
    color: #85b84b;
}
.stock.unavailable > span {
    color: #ff5131;
}
.info-error {
    color: #ff5131;
}
/* Box sizing */
div {

}
/* FONTS
    @toDo: Replace with correct licensed fonts
  */

.tk-futura-pt {
    font-family: "futura-pt", sans-serif;
}
div[role="tablist"] div[data-role="title"]:after {
    font-family: 'luma-icons';
    content: "\e622";
    margin-left: 10px;
    vertical-align: middle;
    color: #2f3943;
}

div[role="tablist"].active div[data-role="title"]:after {
    content: "\e621";
}
.items {
    margin: 0;
    padding: 0;
    list-style: none none;
}
@media (max-width: 767px) {
    h1 {
        font-size: 3.2rem;
        line-height: 3.2rem;
    }
    h2 {
        font-size: 2.6rem;
        line-height: 2.8rem;
        margin: 27px 0 16px 0;
    }
    h3 {
        font-size: 2.4rem;
        line-height: 2.6rem;
        margin: 20px 0 16px 0;
    }
    h4 {
        font-size: 2rem;
        line-height: 2.3rem;
        margin: 20px 0 16px 0;
    }
    h5 {
        font-size: 1.9rem;
        line-height: 2.1rem;
        margin: 16px 0 16px 0;
    }
    h6 {
        font-size: 1.8rem;
        line-height: 2rem;
        margin: 16px 0 16px 0;
    }
}
.columns {
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
    box-sizing: border-box;
}
.columns:after {
    clear: both;
    content: ' ';
    display: block;
    height: 0;
    overflow: hidden;
    visibility: hidden;
}
.columns .column.main {
    padding-bottom: 40px;
    -webkit-flex-basis: 100%;
    flex-basis: 100%;
    -webkit-flex-grow: 1;
    flex-grow: 1;
    -ms-flex-order: 1;
    -webkit-order: 1;
    order: 1;
    width: 100%;
}
.columns .sidebar-main {
    -webkit-flex-grow: 1;
    flex-grow: 1;
    -webkit-flex-basis: 100%;
    flex-basis: 100%;
    -ms-flex-order: 1;
    -webkit-order: 1;
    order: 1;
}
.columns .sidebar-additional {
    -webkit-flex-grow: 1;
    flex-grow: 1;
    -webkit-flex-basis: 100%;
    flex-basis: 100%;
    -ms-flex-order: 2;
    -webkit-order: 2;
    order: 2;
}
.table-wrapper {
    margin-bottom: 20px;
}
table > caption {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.table tfoot {
    background: #f5f5f5;
}
.table tfoot > tr:first-child th,
.table tfoot > tr:first-child td {
    border-top: 1px solid #cccccc;
    padding-top: 20px;
}
.table tfoot th,
.table tfoot td {
    border: 0;
}
.table tfoot .mark {
    font-weight: 400;
    text-align: right;
}
/**********************************************************************************************************************
     * Tables
     **********************************************************************************************************************/
.gfptable {
    border-radius: 5px;
    border: 1px solid #E2E2E2;
}
.gfptable .odd {
    background: #95989A;
}
.no-padding {
    padding: 0;
}
.no-padding td,
.no-padding th,
.no-padding tr {
    padding: inherit;
}
.message.info {
    margin: 0 0 10px;
    padding: 12px 20px 12px 25px;
    display: block;
    font-size: 15px;
    background: #fdf0d5;
    color: #6f4400;
    padding-left: 45px;
    position: relative;
}

.message.info > *:first-child:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 24px;
    line-height: 24px;
    color: #c07600;
    content: '\e602';
    font-family: 'luma-icons';
    margin: -12px 0 0;
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    left: 0;
    top: 22px;
    width: 45px;
    position: absolute;
    text-align: center;
}
.message.error {
    margin: 0 0 10px;
    padding: 12px 20px 12px 25px;
    display: block;
    font-size: 15px;
    background: #fae5e5;
    color: #e02b27;
    padding-left: 45px;
    position: relative;
}

.message.error > *:first-child:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 24px;
    line-height: 24px;
    color: #b30000;
    content: '\e61f';
    font-family: 'luma-icons';
    margin: -12px 0 0;
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    left: 0;
    top: 22px;
    width: 45px;
    position: absolute;
    text-align: center;
}
.message.warning {
    margin: 0 0 10px;
    padding: 12px 20px 12px 25px;
    display: block;
    font-size: 15px;
    background: #fdf0d5;
    color: #6f4400;
    padding-left: 45px;
    position: relative;
}

.message.warning > *:first-child:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 24px;
    line-height: 24px;
    color: #c07600;
    content: '\e602';
    font-family: 'luma-icons';
    margin: -12px 0 0;
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    left: 0;
    top: 22px;
    width: 45px;
    position: absolute;
    text-align: center;
}
.message.notice {
    margin: 0 0 10px;
    padding: 12px 20px 12px 25px;
    display: block;
    font-size: 15px;
    background: #fdf0d5;
    color: #6f4400;
    padding-left: 45px;
    position: relative;
}
.message.notice a {
}
.message.notice a:hover {
}
.message.notice a:active {
}
.message.notice > *:first-child:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 24px;
    line-height: 24px;
    color: #c07600;
    content: '\e602';
    font-family: 'luma-icons';
    margin: -12px 0 0;
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    left: 0;
    top: 22px;
    width: 45px;
    position: absolute;
    text-align: center;
}
.message.success {
    margin: 0 0 10px;
    padding: 12px 20px 12px 25px;
    display: block;
    font-size: 15px;
    background: #e5efe5;
    color: #006400;
    padding-left: 45px;
    position: relative;
}
.message.success a {
}
.message.success a:hover {
}
.message.success a:active {
}
.message.success > *:first-child:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 24px;
    line-height: 24px;
    color: #006400;
    content: '\e60e';
    font-family: 'luma-icons';
    margin: -12px 0 0;
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    left: 0;
    top: 22px;
    width: 45px;
    position: absolute;
    text-align: center;
}
/**********************************************************************************************************************
      * Error messages
      **********************************************************************************************************************/
.page-messages-container {
    background: #f5f5f5;
}
.message.success,
.message.error {
    border-radius: 0;
    color: #2f3943;
    border: 1px solid #2f3943;
}
.message.success > :first-child::before,
.message.error > :first-child::before {
    line-height: 3rem;
}
.message.success {
    color: #85b84b;
    border-color: #85b84b;
}
.message.success > :first-child::before {
    color: #85b84b;
}
.message.error {
    color: #ff5131;
    border-color: #ff5131;
}
.message.error > :first-child::before {
    color: #ff5131;
}
.panel.header .links,
.panel.header .switcher {
    display: none;
}
.nav-sections {
    background: #f0f0f0;
}
.nav-toggle {
    display: inline-block;
    text-decoration: none;
    cursor: pointer;
    display: block;
    left: 15px;
    position: absolute;
    top: 15px;
    z-index: 14;
}
.nav-toggle > span {
    display: block; line-height: 1; font-size: 12px;
}
.tooltip.wrapper {
    position: relative;
}
.tooltip.wrapper .tooltip.content {
    background: #ffffff;
    max-width: 360px;
    min-width: 210px;
    padding: 12px 16px;
    z-index: 100;
    display: none;
    position: absolute;
    text-align: left;
    color: #333333;
    line-height: 1.4;
    border: 1px solid #bbbbbb;
    margin-top: 5px;
    left: 0;
    top: 100%;
}
.tooltip.wrapper .tooltip.content:after,
.tooltip.wrapper .tooltip.content:before {
    border: solid transparent;
    content: '';
    height: 0;
    position: absolute;
    width: 0;
}
.tooltip.wrapper .tooltip.content:after {
    border-width: 5px;
    border-color: transparent;
}
.tooltip.wrapper .tooltip.content:before {
    border-width: 6px;
    border-color: transparent;
}
.tooltip.wrapper .tooltip.content:after,
.tooltip.wrapper .tooltip.content:before {
    bottom: 100%;
}
.tooltip.wrapper .tooltip.content:after {
    border-bottom-color: #ffffff;
    left: 15px;
    margin-left: -5px;
}
.tooltip.wrapper .tooltip.content:before {
    border-bottom-color: #bbbbbb;
    left: 15px;
    margin-left: -6px;
}
.tooltip.wrapper tooltip.toggle {
    cursor: help;
}
.tooltip.wrapper tooltip.toggle:hover + .tooltip.content,
.tooltip.wrapper tooltip.toggle:focus + .tooltip.content,
.tooltip.wrapper:hover .tooltip.content {
    display: block;
}
.tooltip.wrapper .tooltip.content dl {
    margin-bottom: 0;
}
.tooltip.wrapper .tooltip.content dd {
    white-space: normal;
}
.tooltip.wrapper .tooltip.content .subtitle {
    display: inline-block;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 15px;
}
.tooltip.wrapper .tooltip.content .label {
    margin-top: 10px;
}
.tooltip.wrapper .tooltip.content .label:first-child {
    margin-top: 0;
}
.tooltip.wrapper .tooltip.content .values {
    margin: 0;
}
.ui-tooltip {
    position: absolute;
    z-index: 9999;
}
.load.indicator {
    background-color: rgba(255, 255, 255, 0.7);
    z-index: 9999;
    bottom: 0;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
    position: absolute;
}
.load.indicator:before {
    background: transparent url('../images/loader-2.gif') no-repeat 50% 50%;
    border-radius: 5px;
    height: 160px;
    width: 160px;
    bottom: 0;
    box-sizing: border-box;
    content: '';
    left: 0;
    margin: auto;
    position: absolute;
    right: 0;
    top: 0;
}
.load.indicator > span {
    display: none;
}
.loader {
    width: 100%;
    height: 100%;
    position: absolute;
    margin: auto;
    z-index: 100;
    background: rgba(255, 255, 255, 0.5);
}
.loader img {
    bottom: 0;
    left: 0;
    margin: auto;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 100;
}
.loading-mask {
    bottom: 0;
    left: 0;
    margin: auto;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 100;
    background: rgba(255, 255, 255, 0.5);
}
.loading-mask .loader > img {
    bottom: 0;
    left: 0;
    margin: auto;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 100;
}
.loading-mask .loader > p {
    display: none;
}
body > .loading-mask {
    z-index: 9999;
}
._block-content-loading {
    position: relative;
}
.fieldset {
    border: 0;
    margin: 0 0 40px;
    padding: 0;
    letter-spacing: -.31em;
}
.fieldset > * {
    letter-spacing: normal;
}
.fieldset > .legend {
    margin: 0 0 20px;
    padding: 0 0 10px;
    width: 100%;
    box-sizing: border-box;
    float: left;
    font-weight: 300;
    line-height: 1.2;
    font-size: 1.8rem;
}
.fieldset > .legend + br {
    clear: both;
    display: block;
    height: 0;
    overflow: hidden;
    visibility: hidden;
}
.fieldset:last-child {
    margin-bottom: 0;
}

.fieldset > .field > .label,
.fieldset > .fields > .field > .label {
    margin: 0 0 8px;
    display: inline-block;
}
.fieldset > .field:last-child,
.fieldset > .fields > .field:last-child {
    margin-bottom: 0;
}
.fieldset > .field > .label,
.fieldset > .fields > .field > .label {
    font-weight: 400;
}
.fieldset > .field > .label + br,
.fieldset > .fields > .field > .label + br {
    display: none;
}
.fieldset > .field .choice input,
.fieldset > .fields > .field .choice input {
    vertical-align: top;
}
.fieldset > .field .fields.group:before,
.fieldset > .fields > .field .fields.group:before,
.fieldset > .field .fields.group:after,
.fieldset > .fields > .field .fields.group:after {
    content: '';
    display: table;
}
.fieldset > .field .fields.group:after,
.fieldset > .fields > .field .fields.group:after {
    clear: both;
}
.fieldset > .field .fields.group .field,
.fieldset > .fields > .field .fields.group .field {
    box-sizing: border-box;
    float: left;
}
.fieldset > .field .fields.group.group-2 .field,
.fieldset > .fields > .field .fields.group.group-2 .field {
    width: 50% !important;
}
.fieldset > .field .fields.group.group-3 .field,
.fieldset > .fields > .field .fields.group.group-3 .field {
    width: 33.3% !important;
}
.fieldset > .field .fields.group.group-4 .field,
.fieldset > .fields > .field .fields.group.group-4 .field {
    width: 25% !important;
}
.fieldset > .field .fields.group.group-5 .field,
.fieldset > .fields > .field .fields.group.group-5 .field {
    width: 20% !important;
}
.fieldset > .field .addon,
.fieldset > .fields > .field .addon {
    display: -webkit-inline-flex;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-flex-wrap: nowrap;
    flex-wrap: nowrap;
    padding: 0;
    width: 100%;
}
.fieldset > .field .addon textarea,
.fieldset > .fields > .field .addon textarea,
.fieldset > .field .addon select,
.fieldset > .fields > .field .addon select,
.fieldset > .field .addon input,
.fieldset > .fields > .field .addon input {
    -ms-flex-order: 2;
    -webkit-order: 2;
    order: 2;
    -webkit-flex-basis: 100%;
    flex-basis: 100%;
    display: inline-block;
    margin: 0;
    width: auto;
}
.fieldset > .field .addon .addbefore,
.fieldset > .fields > .field .addon .addbefore,
.fieldset > .field .addon .addafter,
.fieldset > .fields > .field .addon .addafter {
    background: #ffffff;
    background-clip: padding-box;
    border: 1px solid #c2c2c2;
    border-radius: 1px;
    font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-size: 14px;
    height: 32px;
    line-height: 1.42857143;
    padding: 0 9px;
    vertical-align: baseline;
    width: 100%;
    box-sizing: border-box;
    -ms-flex-order: 3;
    -webkit-order: 3;
    order: 3;
    display: inline-block;
    vertical-align: middle;
    white-space: nowrap;
    width: auto;
}
.fieldset > .field .addon .addbefore:disabled,
.fieldset > .fields > .field .addon .addbefore:disabled,
.fieldset > .field .addon .addafter:disabled,
.fieldset > .fields > .field .addon .addafter:disabled {
    opacity: 0.5;
}
.fieldset > .field .addon .addbefore::-moz-placeholder,
.fieldset > .fields > .field .addon .addbefore::-moz-placeholder,
.fieldset > .field .addon .addafter::-moz-placeholder,
.fieldset > .fields > .field .addon .addafter::-moz-placeholder {
    color: #c2c2c2;
}
.fieldset > .field .addon .addbefore::-webkit-input-placeholder,
.fieldset > .fields > .field .addon .addbefore::-webkit-input-placeholder,
.fieldset > .field .addon .addafter::-webkit-input-placeholder,
.fieldset > .fields > .field .addon .addafter::-webkit-input-placeholder {
    color: #c2c2c2;
}
.fieldset > .field .addon .addbefore:-ms-input-placeholder,
.fieldset > .fields > .field .addon .addbefore:-ms-input-placeholder,
.fieldset > .field .addon .addafter:-ms-input-placeholder,
.fieldset > .fields > .field .addon .addafter:-ms-input-placeholder {
    color: #c2c2c2;
}
.fieldset > .field .addon .addbefore,
.fieldset > .fields > .field .addon .addbefore {
    float: left;
    -ms-flex-order: 1;
    -webkit-order: 1;
    order: 1;
}
.fieldset > .field .additional,
.fieldset > .fields > .field .additional {
    margin-top: 10px;
}
.fieldset > .field.required :not(.checkout-agreement) > .label:after,
.fieldset > .fields > .field.required > .label:after,
.fieldset > .field._required > .label:after,
.fieldset > .field.required > .label:after,
.fieldset > .fields > .field._required > .label:after {
    content: '*';
    color: #e02b27;
    font-size: 1.2rem;
    margin: 0 0 0 5px;
}
.fieldset > .field .note,
.fieldset > .fields > .field .note {
    font-size: 1.2rem;
    margin: 3px 0 0;
    padding: 0;
    display: inline-block;
    text-decoration: none;
}
.fieldset > .field .note:before,
.fieldset > .fields > .field .note:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 24px;
    line-height: 12px;
    font-family: 'luma-icons';
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.fieldset > .field.no-label > .label,
.fieldset > .fields > .field.no-label > .label {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.fieldset > .field.choice .label,
.fieldset > .fields > .field.choice .label {
    display: inline;
    font-weight: normal;
    margin: 0;
}
.fieldset > .field .label,
.fieldset > .fields > .field .label {
    font-weight: normal;
}
.column:not(.main) .fieldset > .field .label,
.column:not(.main) .fieldset > .fields > .field .label {
    font-weight: normal;
}
.fieldset > .field .field.choice,
.fieldset > .fields > .field .field.choice {
    margin-bottom: 10px;
}
.fieldset > .field .field.choice:last-child,
.fieldset > .fields > .field .field.choice:last-child {
    margin-bottom: 0;
}
.legend + .fieldset,
.legend + div {
    clear: both;
}
.legend > span {
    margin-right: 5px;
}
fieldset.field {
    border: 0;
    padding: 0;
}
.field.date .time-picker {
    display: inline-block;
    margin-top: 10px;
    white-space: nowrap;
}
.field .message.warning {
    margin-top: 10px;
}
select {
    border: 1px solid #cccccc;
    height: 32px;
    padding-right: 25px;
    text-indent: .01em;
    text-overflow: '';
    border-radius: 5px;
    -webkit-appearance: none;
    -moz-appearance: none;
    -ms-appearance: none;
    appearance: none;
}
select::-ms-expand {
    display: none;
}
.lt-ie10 select {
    background-image: none;
    padding-right: 4px;
}
div.mage-error[generated] {
    margin-top: 7px;
}
.field .tooltip {
    position: relative;
}
.field .tooltip .tooltip-content {
    background: #ffffff;
    max-width: 360px;
    min-width: 210px;
    padding: 12px 16px;
    z-index: 100;
    display: none;
    position: absolute;
    text-align: left;
    color: #333333;
    line-height: 1.4;
    border: 1px solid #bbbbbb;
    margin-left: 5px;
    left: 100%;
    top: 0;
}
.field .tooltip .tooltip-content:after,
.field .tooltip .tooltip-content:before {
    border: solid transparent;
    content: '';
    height: 0;
    position: absolute;
    width: 0;
}
.field .tooltip .tooltip-content:after {
    border-width: 5px;
    border-color: transparent;
}
.field .tooltip .tooltip-content:before {
    border-width: 6px;
    border-color: transparent;
}
.field .tooltip .tooltip-content:after,
.field .tooltip .tooltip-content:before {
    right: 100%;
}
.field .tooltip .tooltip-content:after {
    border-right-color: #ffffff;
    margin-top: -5px;
    top: 15px;
}
.field .tooltip .tooltip-content:before {
    border-right-color: #bbbbbb;
    margin-top: -6px;
    top: 15px;
}
.field .tooltip .tooltip-toggle {
    cursor: help;
}
.field .tooltip .tooltip-toggle:hover + .tooltip-content,
.field .tooltip .tooltip-toggle:focus + .tooltip-content,
.field .tooltip:hover .tooltip-content {
    display: block;
}
.field .tooltip .tooltip-content {
    min-width: 200px;
    white-space: normal;
}
input:focus ~ .tooltip .tooltip-content,
select:focus ~ .tooltip .tooltip-content {
    display: block;
}
._has-datepicker ~ .ui-datepicker-trigger {
    background-image: none;
    background: none;
    -moz-box-sizing: content-box;
    border: 0;
    box-shadow: none;
    line-height: inherit;
    margin: 0;
    padding: 0;
    text-shadow: none;
    font-weight: 400;
    display: block;
    text-decoration: none;
    display: inline-block;
    margin-top: -4px;
    vertical-align: middle;
}
._has-datepicker ~ .ui-datepicker-trigger:focus,
._has-datepicker ~ .ui-datepicker-trigger:active {
    background: none;
    border: none;
}
._has-datepicker ~ .ui-datepicker-trigger:hover {
    background: none;
    border: none;
}
._has-datepicker ~ .ui-datepicker-trigger.disabled,
._has-datepicker ~ .ui-datepicker-trigger[disabled],
fieldset[disabled] ._has-datepicker ~ .ui-datepicker-trigger {
    pointer-events: none;
    opacity: 0.5;
}
._has-datepicker ~ .ui-datepicker-trigger > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
._has-datepicker ~ .ui-datepicker-trigger:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 23px;
    line-height: 23px;
    color: #7d7d7d;
    content: '\e612';
    font-family: 'luma-icons';
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
._has-datepicker ~ .ui-datepicker-trigger:focus {
    box-shadow: none;
    outline: 0;
}
.sidebar .fieldset {
    margin: 0;
}
.sidebar .fieldset > .field:not(.choice):not(:last-child),
.sidebar .fieldset .fields > .field:not(:last-child) {
    margin: 0 0 20px;
}
.sidebar .fieldset > .field:not(.choice) .label,
.sidebar .fieldset .fields > .field .label {
    margin: 0 0 4px;
    padding: 0 0 5px;
    text-align: left;
    width: 100%;
}
.sidebar .fieldset > .field:not(.choice) .control,
.sidebar .fieldset .fields > .field .control {
    width: 100%;
}
/**********************************************************************************************************************
     * FORMS
     **********************************************************************************************************************/
input[type="text"],
input[type="email"],
input[type="password"],
input.input-text,
select {
    font-size: 1.8rem;
    height: 35px;
    transition: all 0.4s;
}
input[type="text"].mage-error,
input[type="email"].mage-error,
input[type="password"].mage-error,
input.input-text.mage-error,
select.mage-error {
    border-color: #ff5131;
}

.select-wrapper {
    position: relative;
    display: inline-block;
    z-index: 1;
}
.select-wrapper .select-icon::before {
    /*position: absolute;
    right: 14px;
    top: 14px;
    font-size: 1rem;
    line-height: 2.2rem;
    color: inherit;
    content: '\e622';
    font-family: 'luma-icons';
    margin: 0;
    vertical-align: top;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
    pointer-events: none;*/
}
div.mage-error {
    color: #ff5131;
}
.field .control._with-tooltip input {
    width: 100%;
    margin-right: 0;
}
.field-tooltip {
    right: -12px;
    top: 9px;
}
.field-tooltip .field-tooltip-action::before {
    color: #333333;
}
/* Custom radio / checkbox */
[type="checkbox"]:not(:checked),
[type="checkbox"]:checked {
    position: absolute;
    left: -9999px;
}
[type="checkbox"]:not(:checked) + label,
[type="checkbox"]:checked + label {
    position: relative;
    padding-left: 1.95em;
    cursor: pointer;
    display: block;
}
/* checkbox */
[type="checkbox"]:not(:checked) + label:before,
[type="checkbox"]:checked + label:before {
    content: '\e901';
    font-family: 'luma-icons';
    font-size: 2.8rem;
    position: absolute;
    left: 0;
    top: 0;
    width: 1.25em;
    height: 1.25em;
}
/* checked mark */
[type="checkbox"]:not(:checked) + label:after,
[type="checkbox"]:checked + label:after {
    content: '\f00c';
    font-family: 'Font Awesome 5 Free';
    position: absolute;
    left: 8px;
    font-weight: 600;
    top: 0;
    font-size: 1.2rem;
    color: #000;
    transition: all .2s;
}
/* checked mark aspect changes */
[type="checkbox"]:not(:checked) + label:after {
    opacity: 0;
}
[type="checkbox"]:checked + label:after {
    opacity: 1;
}
/* radio */
input[type=radio] {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
input[type=radio] + label:before, .checkout-index-index .checkout-container .payment-method .payment-method-title .label:before {
    content: '\e903';
    font-family: 'luma-icons';
    display: none;
    letter-spacing: 10px;
    color: #535353;
    font-size: 2.3rem;
    vertical-align: text-top;
}
/*input[type=radio]:checked + label:before {*/
/*    content: '\e904';*/
/*    font-family: 'luma-icons';*/
/*}*/
.ui-datepicker {
    background: white;
}
.ui-datepicker a::before {
    border-bottom: none;
}
.ui-datepicker .ui-datepicker-next span {
    border: solid #2D3843;
    border-width: 0 2px 2px 0;
    display: inline-block;
    padding: 3px;
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
    transition: border 0.4s;
}
.ui-datepicker .ui-datepicker-prev span {
    border: solid #2D3843;
    border-width: 0 2px 2px 0;
    display: inline-block;
    padding: 3px;
    transform: rotate(135deg);
    -webkit-transform: rotate(135deg);
    transition: border 0.4s;
}
.ui-datepicker .ui-datepicker-calendar th {
    background: white;
    border: none;
}
.ui-datepicker .ui-datepicker-calendar td {
    border: none;
    background: white;
}
.ui-datepicker .ui-datepicker-calendar td .ui-state-hover {
    background: #f5f5f5;
    border-radius: 20px;
}
.ui-datepicker .ui-datepicker-calendar .ui-state-active {
    background: #f5f5f5;
    border-radius: 20px;
}
.ui-datepicker .ui-datepicker-calendar .ui-state-disabled {
    background: white;
}
.ui-datepicker .ui-datepicker-calendar .ui-datepicker-today {
    border-radius: 20px;
}
.ui-datepicker .ui-datepicker-calendar {
    border: none;
}
.abs-field-date input,
.field.date ._has-datepicker,
.field-dob ._has-datepicker,
.form-giftregistry-search .fields-specific-options .datetime-picker,
.form.search.advanced .fields.range .field.date input,
.form-giftregistry-edit .field.date input,
.delivery-info-block ._has-datepicker,
.form.search.advanced .fields.range .field.date input {
    padding-left: 45px;
    background: url('../images/datepicker_icon.svg') no-repeat 15px 6px;
    background-size: 20px 20px;
    width: 195px;
    display: inline-block;
    cursor: pointer;
}
@font-face {
    font-family: 'luma-icons';
    src: url('../fonts/Luma-Icons.woff2') format('woff2'), url('../fonts/Luma-Icons.woff') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}
body:not(._keyfocus) button:focus {
    box-shadow: none;
}
/**********************************************************************************************************************
     * BUTTON
     **********************************************************************************************************************/
button,
.action.primary,
button.action,
button.action-auth-toggle,
.button,
a.button,
div.std_button a,
input[type=button],
input[type=submit] {
    font-family: 'futura-pt';
    min-height: 45px;
    width: auto;
    display: inline-block;
    cursor: pointer;
    text-align: center;
    border: none;
    padding: 8px 20px 8px 20px;
    margin: 5px 10px 5px 0;
    text-decoration: none;
    color: #fff;
    background-color: #677752;
    font-weight: 400;
    font-size: 1.8rem;
    transition: all 0.4s;
    box-shadow: none;
    border-radius: 5px;
    vertical-align: middle;
    box-sizing: border-box;
}
button a,
.action.primary a,
button.action a,
button.action-auth-toggle a,
.button a,
a.button a,
div.std_button a a,
input[type=button] a,
input[type=submit] a {
    padding: 0;
    margin: 0;
}
button a::after,
.action.primary a::after,
button.action a::after,
button.action-auth-toggle a::after,
.button a::after,
a.button a::after,
div.std_button a a::after,
input[type=button] a::after,
input[type=submit] a::after {
    display: none;
}

button::before,
.action.primary::before,
button.action::before,
button.action-auth-toggle::before,
.button::before,
a.button::before,
div.std_button a::before,
input[type=button]::before,
input[type=submit]::before {
    font-family: 'luma-icons';
    display: inline-block;
    cursor: pointer;
    text-align: center;
    color: inherit;
    border: none;
    transition: all 0.4s;
    width: auto;
    position: inherit;
    bottom: auto;
    left: auto;
}
button::before:hover,
.action.primary::before:hover,
button.action::before:hover,
button.action-auth-toggle::before:hover,
.button::before:hover,
a.button::before:hover,
div.std_button a::before:hover,
input[type=button]::before:hover,
input[type=submit]::before:hover {
    border: none;
}
button.green,
.action.primary.green,
button.action.green,
button.action-auth-toggle.green,
.button.green,
a.button.green,
div.std_button a.green,
input[type=button].green,
input[type=submit].green,
button.green a,
.action.primary.green a,
button.action.green a,
button.action-auth-toggle.green a,
.button.green a,
a.button.green a,
div.std_button a.green a,
input[type=button].green a,
input[type=submit].green a,
button.checkout,
.action.primary.checkout,
button.action.checkout,
button.action-auth-toggle.checkout,
.button.checkout,
a.button.checkout,
div.std_button a.checkout,
input[type=button].checkout,
input[type=submit].checkout {
    color: white;
    background-color: #677752;
    border-color: #677752;
}
button.green::before,
.action.primary.green::before,
button.action.green::before,
button.action-auth-toggle.green::before,
.button.green::before,
a.button.green::before,
div.std_button a.green::before,
input[type=button].green::before,
input[type=submit].green::before,
button.green a::before,
.action.primary.green a::before,
button.action.green a::before,
button.action-auth-toggle.green a::before,
.button.green a::before,
a.button.green a::before,
div.std_button a.green a::before,
input[type=button].green a::before,
input[type=submit].green a::before,
button.checkout::before,
.action.primary.checkout::before,
button.action.checkout::before,
button.action-auth-toggle.checkout::before,
.button.checkout::before,
a.button.checkout::before,
div.std_button a.checkout::before,
input[type=button].checkout::before,
input[type=submit].checkout::before {
    border-bottom: none;
}
button.green:hover,
.action.primary.green:hover,
button.action.green:hover,
button.action-auth-toggle.green:hover,
.button.green:hover,
a.button.green:hover,
div.std_button a.green:hover,
input[type=button].green:hover,
input[type=submit].green:hover,
button.green a:hover,
.action.primary.green a:hover,
button.action.green a:hover,
button.action-auth-toggle.green a:hover,
.button.green a:hover,
a.button.green a:hover,
div.std_button a.green a:hover,
input[type=button].green a:hover,
input[type=submit].green a:hover,
button.checkout:hover,
.action.primary.checkout:hover,
button.action.checkout:hover,
button.action-auth-toggle.checkout:hover,
.button.checkout:hover,
a.button.checkout:hover,
div.std_button a.checkout:hover,
input[type=button].checkout:hover,
input[type=submit].checkout:hover {
    opacity: 0.6;
    filter: alpha(opacity=60);
    /* For IE8 and earlier */
}
button.blue,
.action.primary.blue,
button.action.blue,
button.action-auth-toggle.blue,
.button.blue,
a.button.blue,
div.std_button a.blue,
input[type=button].blue,
input[type=submit].blue,
button.blue a,
.action.primary.blue a,
button.action.blue a,
button.action-auth-toggle.blue a,
.button.blue a,
a.button.blue a,
div.std_button a.blue a,
input[type=button].blue a,
input[type=submit].blue a {
    color: white;
    background-color: #318cc5;
    border-color: #318cc5;
}
button.blue::before,
.action.primary.blue::before,
button.action.blue::before,
button.action-auth-toggle.blue::before,
.button.blue::before,
a.button.blue::before,
div.std_button a.blue::before,
input[type=button].blue::before,
input[type=submit].blue::before,
button.blue a::before,
.action.primary.blue a::before,
button.action.blue a::before,
button.action-auth-toggle.blue a::before,
.button.blue a::before,
a.button.blue a::before,
div.std_button a.blue a::before,
input[type=button].blue a::before,
input[type=submit].blue a::before {
    border-bottom: none;
}
button.blue:hover,
.action.primary.blue:hover,
button.action.blue:hover,
button.action-auth-toggle.blue:hover,
.button.blue:hover,
a.button.blue:hover,
div.std_button a.blue:hover,
input[type=button].blue:hover,
input[type=submit].blue:hover,
button.blue a:hover,
.action.primary.blue a:hover,
button.action.blue a:hover,
button.action-auth-toggle.blue a:hover,
.button.blue a:hover,
a.button.blue a:hover,
div.std_button a.blue a:hover,
input[type=button].blue a:hover,
input[type=submit].blue a:hover {
    opacity: 0.6;
    filter: alpha(opacity=60);
    /* For IE8 and earlier */
}
button.light,
.action.primary.light,
button.action.light,
button.action-auth-toggle.light,
.button.light,
a.button.light,
div.std_button a.light,
input[type=button].light,
input[type=submit].light,
button.light a,
.action.primary.light a,
button.action.light a,
button.action-auth-toggle.light a,
.button.light a,
a.button.light a,
div.std_button a.light a,
input[type=button].light a,
input[type=submit].light a {
    color: white;
    background-color: transparent;
    border-color: white;
}
button.light:hover,
.action.primary.light:hover,
button.action.light:hover,
button.action-auth-toggle.light:hover,
.button.light:hover,
a.button.light:hover,
div.std_button a.light:hover,
input[type=button].light:hover,
input[type=submit].light:hover,
button.light a:hover,
.action.primary.light a:hover,
button.action.light a:hover,
button.action-auth-toggle.light a:hover,
.button.light a:hover,
a.button.light a:hover,
div.std_button a.light a:hover,
input[type=button].light a:hover,
input[type=submit].light a:hover {
    background: #677752;
    border-color: #677752;
}
button.tocart,
.action.primary.tocart,
button.action.tocart,
button.action-auth-toggle.tocart,
.button.tocart,
a.button.tocart,
div.std_button a.tocart,
input[type=button].tocart,
input[type=submit].tocart {
    color: white;
    background-color: #677752;
    border-color: #677752;
}
button.tocart::after,
.action.primary.tocart::after,
button.action.tocart::after,
button.action-auth-toggle.tocart::after,
.button.tocart::after,
a.button.tocart::after,
div.std_button a.tocart::after,
input[type=button].tocart::after,
input[type=submit].tocart::after {
    content: "";
    margin-left: 0;
}
button.tocart::before,
.action.primary.tocart::before,
button.action.tocart::before,
button.action-auth-toggle.tocart::before,
.button.tocart::before,
a.button.tocart::before,
div.std_button a.tocart::before,
input[type=button].tocart::before,
input[type=submit].tocart::before {
    content: "\e90d";
    margin-right: 10px;
}
button.tocart:hover,
.action.primary.tocart:hover,
button.action.tocart:hover,
button.action-auth-toggle.tocart:hover,
.button.tocart:hover,
a.button.tocart:hover,
div.std_button a.tocart:hover,
input[type=button].tocart:hover,
input[type=submit].tocart:hover {
    background: #9ec670;
    border-color: #9ec670;
}
button.toorder,
.action.primary.toorder,
button.action.toorder,
button.action-auth-toggle.toorder,
.button.toorder,
a.button.toorder,
div.std_button a.toorder,
input[type=button].toorder,
input[type=submit].toorder {
    color: white;
    background-color: #677752;
    border-color: #677752;
}
button.toorder::after,
.action.primary.toorder::after,
button.action.toorder::after,
button.action-auth-toggle.toorder::after,
.button.toorder::after,
a.button.toorder::after,
div.std_button a.toorder::after,
input[type=button].toorder::after,
input[type=submit].toorder::after {
    content: "";
    margin-left: 0;
}
button.toorder::before,
.action.primary.toorder::before,
button.action.toorder::before,
button.action-auth-toggle.toorder::before,
.button.toorder::before,
a.button.toorder::before,
div.std_button a.toorder::before,
input[type=button].toorder::before,
input[type=submit].toorder::before {
    content: "\e908";
    margin-right: 10px;
}
button.toorder:hover,
.action.primary.toorder:hover,
button.action.toorder:hover,
button.action-auth-toggle.toorder:hover,
.button.toorder:hover,
a.button.toorder:hover,
div.std_button a.toorder:hover,
input[type=button].toorder:hover,
input[type=submit].toorder:hover {
    background: #9ec670;
    border-color: #9ec670;
}
button.todescription::after,
.action.primary.todescription::after,
button.action.todescription::after,
button.action-auth-toggle.todescription::after,
.button.todescription::after,
a.button.todescription::after,
div.std_button a.todescription::after,
input[type=button].todescription::after,
input[type=submit].todescription::after {
    content: "";
    margin-left: 0;
}
button.todescription::before,
.action.primary.todescription::before,
button.action.todescription::before,
button.action-auth-toggle.todescription::before,
.button.todescription::before,
a.button.todescription::before,
div.std_button a.todescription::before,
input[type=button].todescription::before,
input[type=submit].todescription::before {
    content: "\e907";
    margin-right: 10px;
}
button.todescription:hover,
.action.primary.todescription:hover,
button.action.todescription:hover,
button.action-auth-toggle.todescription:hover,
.button.todescription:hover,
a.button.todescription:hover,
div.std_button a.todescription:hover,
input[type=button].todescription:hover,
input[type=submit].todescription:hover {
    background: #9ec670;
    border-color: #9ec670;
}
button.tooverview::after,
.action.primary.tooverview::after,
button.action.tooverview::after,
button.action-auth-toggle.tooverview::after,
.button.tooverview::after,
a.button.tooverview::after,
div.std_button a.tooverview::after,
input[type=button].tooverview::after,
input[type=submit].tooverview::after {
    content: "";
    margin-left: 0;
}
button.tooverview::before,
.action.primary.tooverview::before,
button.action.tooverview::before,
button.action-auth-toggle.tooverview::before,
.button.tooverview::before,
a.button.tooverview::before,
div.std_button a.tooverview::before,
input[type=button].tooverview::before,
input[type=submit].tooverview::before {
    content: "\e90f";
    margin-right: 10px;
}
button.tooverview:hover,
.action.primary.tooverview:hover,
button.action.tooverview:hover,
button.action-auth-toggle.tooverview:hover,
.button.tooverview:hover,
a.button.tooverview:hover,
div.std_button a.tooverview:hover,
input[type=button].tooverview:hover,
input[type=submit].tooverview:hover {
    background: #9ec670;
    border-color: #9ec670;
}
button.update.action::after,
.action.primary.update.action::after,
button.action.update.action::after,
button.action-auth-toggle.update.action::after,
.button.update.action::after,
a.button.update.action::after,
div.std_button a.update.action::after,
input[type=button].update.action::after,
input[type=submit].update.action::after {
    content: "";
    margin-left: 0;
}
button.update.action::before,
.action.primary.update.action::before,
button.action.update.action::before,
button.action-auth-toggle.update.action::before,
.button.update.action::before,
a.button.update.action::before,
div.std_button a.update.action::before,
input[type=button].update.action::before,
input[type=submit].update.action::before {
    content: "\e603";
    margin-right: 10px;
}
button.update.action:hover::before,
.action.primary.update.action:hover::before,
button.action.update.action:hover::before,
button.action-auth-toggle.update.action:hover::before,
.button.update.action:hover::before,
a.button.update.action:hover::before,
div.std_button a.update.action:hover::before,
input[type=button].update.action:hover::before,
input[type=submit].update.action:hover::before {
    color: white;
}
button.toggle,
.action.primary.toggle,
button.action.toggle,
button.action-auth-toggle.toggle,
.button.toggle,
a.button.toggle,
div.std_button a.toggle,
input[type=button].toggle,
input[type=submit].toggle {
    padding: 5px 15px;
    min-height: 35px;
}
button.toggle::after,
.action.primary.toggle::after,
button.action.toggle::after,
button.action-auth-toggle.toggle::after,
.button.toggle::after,
a.button.toggle::after,
div.std_button a.toggle::after,
input[type=button].toggle::after,
input[type=submit].toggle::after {
    content: "\e622";
    margin-left: 10px;
    vertical-align: middle;
}
button.toggle.active::after,
.action.primary.toggle.active::after,
button.action.toggle.active::after,
button.action-auth-toggle.toggle.active::after,
.button.toggle.active::after,
a.button.toggle.active::after,
div.std_button a.toggle.active::after,
input[type=button].toggle.active::after,
input[type=submit].toggle.active::after {
    content: "\e621";
}
.product.data.items {
    margin: 0;
    padding: 0;
    border-bottom: 1px solid #cccccc;
    margin-bottom: 20px;
    margin-left: -15px;
    margin-right: -15px;
}
.product.data.items .item.title > .switch {
    display: block;
}
.product.data.items .item.content {
    box-sizing: border-box;
    display: block;
    float: none;
    margin: 0;
}
.product.data.items .item.content:before,
.product.data.items .item.content:after {
    content: '';
    display: table;
}
.product.data.items .item.content:after {
    clear: both;
}
.product.data.items .item.content.active {
    display: block;
}
.product.data.items .item.title {
    margin: 0;
    flex: 1;
}
.product.data.items .item.title > .switch {
    border-top: 1px solid #cccccc;
    height: 40px;
    padding: 1px 10px 1px 10px;
    font-weight: 400;
    line-height: 40px;
    font-size: 1.4rem;
    color: #333333;
    text-decoration: none;
}
.product.data.items .item.title > .switch:visited {
    color: #333333;
    text-decoration: none;
}
.product.data.items .item.title > .switch:hover {
    color: #333333;
    text-decoration: none;
}
.product.data.items .item.title > .switch:active {
    color: #333333;
    text-decoration: none;
}
.product.data.items .item.title:not(.disabled) > .switch:active,
.product.data.items .item.title.active > .switch,
.product.data.items .item.title.active > .switch:focus,
.product.data.items .item.title.active > .switch:hover {
    padding-bottom: 1px;
}
.product.data.items .item.content {
    background: #ffffff;
    margin: 0;
    padding: 10px 10px 30px;
}
.product.data.items .item.title a {
    position: relative;
    text-decoration: none;
}

.product.data.items .value p:last-child {
    margin-bottom: 0;
}
.product.data.items .table-wrapper {
    margin: 0;
}
.pages > .label {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.pages .items {
    font-size: 0;
    letter-spacing: -1px;
    line-height: 0;
    white-space: nowrap;
    margin: 0;
    padding: 0;
    list-style: none none;
    display: inline-block;
    font-weight: 400;
}
.pages .item {
    font-size: 1.4rem;
    line-height: 3rem;
    letter-spacing: normal;
    margin: 0 8px 0 0;
    display: inline-block;
}
.pages .item .label {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.pages a.page {
    color: #a3a3a3;
    display: inline-block;
    padding: 0 5px;
    text-decoration: none;
}
.pages a.page:hover {
    color: #a3a3a3;
    text-decoration: none;
}
.pages strong.page {
    font-size: 1.4rem;
    line-height: 3rem;
    letter-spacing: normal;
    color: #333333;
    display: inline-block;
    padding: 0 5px;
}
.pages .action {
    background: #f0f0f0;
    border: 1px solid #cccccc;
    color: #7d7d7d;
    display: inline-block;
    padding: 0;
    text-decoration: none;
}
.pages .action:visited {
    color: #7d7d7d;
}
.pages .action:hover {
    background: #dedede;
    color: #7d7d7d;
    text-decoration: none;
}
.pages .action:active {
    color: #7d7d7d;
}
.pages .action.next {
    display: inline-block;
    text-decoration: none;
}
.pages .action.next:visited:before {
    color: #7d7d7d;
}
.pages .action.next:active:before {
    color: #7d7d7d;
}
.pages .action.next > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.pages .action.next:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 16px;
    line-height: inherit;
    color: #7d7d7d;
    content: '\e608';
    font-family: 'luma-icons';
    margin: 0;
    vertical-align: top;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.pages .action.next:hover:before {
    color: #7d7d7d;
}
.pages .action.next:active:before {
    color: #7d7d7d;
}
.pages .action.previous {
    display: inline-block;
    text-decoration: none;
}
.pages .action.previous:visited:before {
    color: #7d7d7d;
}
.pages .action.previous:active:before {
    color: #7d7d7d;
}
.pages .action.previous > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.pages .action.previous:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 16px;
    line-height: inherit;
    color: #7d7d7d;
    content: '\e617';
    font-family: 'luma-icons';
    margin: 0;
    vertical-align: top;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.pages .action.previous:hover:before {
    color: #7d7d7d;
}
.pages .action.previous:active:before {
    color: #7d7d7d;
}
.pages .action {
    padding: 0 8px;
}
.pages .action.previous {
    margin-right: 8px;
}
.pages .action.next {
    margin-left: 8px;
}
.actions-toolbar > .primary,
.actions-toolbar > .secondary {
    margin-bottom: 10px;
    text-align: center;
}
.actions-toolbar > .primary .action,
.actions-toolbar > .secondary .action {
    margin-bottom: 10px;
}
.actions-toolbar > .primary .action:last-child,
.actions-toolbar > .secondary .action:last-child {
    margin-bottom: 0;
}
.actions-toolbar > .primary:last-child,
.actions-toolbar > .secondary:last-child {
    margin-bottom: 0;
}
.actions-toolbar > .secondary .action.back {
    display: none;
}
.breadcrumbs {
    margin: 0 0 15px;
}
.breadcrumbs .items {
    font-size: 1.2rem;
    color: #a3a3a3;
    margin: 0;
    padding: 0;
    list-style: none none;
}
.breadcrumbs .items > li {
    display: inline-block;
    vertical-align: top;
    letter-spacing: 0.3px;
}
.breadcrumbs .item {
    margin: 0;
}
.breadcrumbs a {
    color: #333333;
    text-decoration: none;
}
.breadcrumbs a:visited {
    color: #333333;
    text-decoration: none;
}
.breadcrumbs a:hover {
    color: #333333;
    text-decoration: underline;
}
.breadcrumbs a:active {
    color: #333333;
    text-decoration: none;
}
.breadcrumbs .item:not(:last-child) {
    display: inline-block;
    text-decoration: none;
}
.breadcrumbs .item:not(:last-child):after {
    font-size: 8px;
    line-height: 8px;
    content: '/';
    margin: 0 10px 4px;
    vertical-align: middle;
    display: inline-block;
    font-weight: bold;
    text-align: center;
}
.ui-dialog.popup .action.close {
    position: absolute;
    height: 40px;
    right: 0;
    top: 0;
    width: 40px;
    z-index: 1001;
    display: inline-block;
    background-image: none;
    background: none;
    -moz-box-sizing: content-box;
    border: 0;
    box-shadow: none;
    line-height: inherit;
    margin: 0;
    padding: 0;
    text-decoration: none;
    text-shadow: none;
    font-weight: 400;
}
.ui-dialog.popup .action.close > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.ui-dialog.popup .action.close:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 16px;
    line-height: 16px;
    color: #8c8c8c;
    content: '\e616';
    font-family: 'luma-icons';
    margin: 0;
    vertical-align: top;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.ui-dialog.popup .action.close:hover:before {
    color: #737373;
}
.ui-dialog.popup .action.close:active:before {
    color: #737373;
}
.ui-dialog.popup .action.close:focus,
.ui-dialog.popup .action.close:active {
    background: none;
    border: none;
}
.ui-dialog.popup .action.close:hover {
    background: none;
    border: none;
}
.ui-dialog.popup .action.close.disabled,
.ui-dialog.popup .action.close[disabled],
fieldset[disabled] .ui-dialog.popup .action.close {
    pointer-events: none;
    opacity: 0.5;
}
.popup .actions-toolbar .action.cancel {
    font-weight: 400;
}
.popup-pointer {
    margin-left: -14px;
    position: absolute;
    top: -14px;
}
.popup-pointer:before,
.popup-pointer:after {
    content: '';
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    border-bottom-style: solid;
}
.popup-pointer:before {
    left: 2px;
    top: 2px;
    border: solid 6px;
    border-color: transparent transparent #ffffff transparent;
    z-index: 99;
}
.popup-pointer:after {
    left: 1px;
    top: 0px;
    border: solid 7px;
    border-color: transparent transparent #aeaeae transparent;
    z-index: 98;
}
.price-tier_price .price-excluding-tax,
.price-tier_price .price-including-tax {
    display: inline;
}
.price-including-tax,
.price-excluding-tax {
    display: block;
    font-size: 1.8rem;
    line-height: 1;
}
.price-including-tax .price,
.price-excluding-tax .price {
    font-weight: 700;
}
.price-including-tax + .price-excluding-tax,
.weee[data-label] {
    display: block;
    font-size: 1.8rem;
}
.price-including-tax + .price-excluding-tax:before,
.weee[data-label]:before {
    content: attr(data-label) ': ';
    font-size: 1.1rem;
}
.price-including-tax + .price-excluding-tax .price,
.weee[data-label] .price {
    font-size: 1.1rem;
}
body._has-modal {
    height: 100%;
    overflow: hidden;
    width: 100%;
}
.modals-overlay {
    z-index: 899;
}
.modal-slide,
.modal-popup {
    bottom: 0;
    left: 0;
    min-width: 0;
    position: fixed;
    right: 0;
    top: 0;
    visibility: hidden;
    opacity: 0;
    -webkit-transition: visibility 0s .3s, opacity .3s ease;
    transition: visibility 0s .3s, opacity .3s ease;
}
.modal-slide._show,
.modal-popup._show {
    visibility: visible;
    opacity: 1;
    -webkit-transition: opacity .3s ease;
    transition: opacity .3s ease;
}
.modal-slide._show .modal-inner-wrap,
.modal-popup._show .modal-inner-wrap {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
}
.modal-slide .modal-inner-wrap,
.modal-popup .modal-inner-wrap {
    background-color: #ffffff;
    opacity: 1;
    pointer-events: auto;
}
.modal-slide {
    left: 0;
    z-index: 900;
}
.modal-slide._show .modal-inner-wrap {
    -webkit-transform: translateX(0);
    transform: translateX(0);
}
.modal-slide .modal-inner-wrap {
    height: 100%;
    overflow-y: auto;
    position: static;
    -webkit-transform: translateX(100%);
    transform: translateX(100%);
    -webkit-transition: -webkit-transform 0.3s ease-in-out;
    transition: transform 0.3s ease-in-out;
    width: auto;
}
.modal-slide._inner-scroll .modal-inner-wrap {
    overflow-y: visible;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
}
.modal-slide._inner-scroll .modal-header,
.modal-slide._inner-scroll .modal-footer {
    -webkit-flex-grow: 0;
    flex-grow: 0;
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
    border: none;
}
.modal-slide._inner-scroll .modal-content {
    overflow-y: auto;
}
.modal-slide._inner-scroll .modal-footer {
    margin-top: auto;
}
.modal-slide .modal-header,
.modal-slide .modal-content,
.modal-slide .modal-footer {
    padding: 0 2.6rem 2.6rem;
}
.modal-slide .modal-header {
    padding-bottom: 2.1rem;
    padding-top: 2.1rem;
}
.modal-popup {
    z-index: 900;
    left: 0;
    overflow-y: auto;
}
.modal-popup.confirm .modal-inner-wrap {
    width: 50rem;
}
.modal-popup._show .modal-inner-wrap {
    -webkit-transform: translateY(0);
    transform: translateY(0);
}
.modal-popup .modal-inner-wrap {
    margin: 5rem auto;
    width: 75%;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    box-sizing: border-box;
    height: auto;
    left: 0;
    position: absolute;
    right: 0;
    -webkit-transform: translateY(-200%);
    transform: translateY(-200%);
    -webkit-transition: -webkit-transform 0.2s ease;
    transition: transform .2s ease;
}
.modal-popup._inner-scroll {
    overflow-y: visible;
}
.ie11 .modal-popup._inner-scroll,
.ie10 .modal-popup._inner-scroll,
.ie9 .modal-popup._inner-scroll {
    overflow-y: auto;
}
.modal-popup._inner-scroll .modal-inner-wrap {
    max-height: 90%;
    border-radius: 5px;
    max-width: 1570px;
}
.ie11 .modal-popup._inner-scroll .modal-inner-wrap,
.ie10 .modal-popup._inner-scroll .modal-inner-wrap,
.ie9 .modal-popup._inner-scroll .modal-inner-wrap {
    max-height: none;
}
.modal-popup._inner-scroll .modal-content {
    overflow-y: auto;
    border: none; box-shadow: none;
}
.modal-popup .modal-header,
.modal-popup .modal-content,
.modal-popup .modal-footer {
    padding-left: 3rem;
    padding-right: 3rem;
    box-shadow: none;
    border: none;
}
.modal-header .action-close span { font-size: 1.25rem; color: white; }
.modal-popup .modal-header,
.modal-popup .modal-footer {
    -webkit-flex-grow: 0;
    flex-grow: 0;
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
}
.modal-popup .modal-header {
    padding-bottom: 1.2rem;
    padding-top: 3rem;
}
.modal-popup .modal-footer {
    margin-top: auto;
    padding-bottom: 3rem;
    padding-top: 3rem;
}
.modal-popup .modal-footer-actions {
    text-align: right;
}
.modal-custom .action-close,
.modal-popup .action-close,
.modal-slide .action-close {
    background-image: none;
    background: none;
    -moz-box-sizing: content-box;
    border: 0;
    box-shadow: none;
    line-height: inherit;
    margin: 0;
    padding: 0;
    text-shadow: none;
    font-weight: 400;
    display: inline-block;
    text-decoration: none;
    padding: 0 30px 0 0;
    position: absolute;
    right: 0;
    top: 0;
}
.modal-custom .action-close:focus,
.modal-popup .action-close:focus,
.modal-slide .action-close:focus,
.modal-custom .action-close:active,
.modal-popup .action-close:active,
.modal-slide .action-close:active {
    background: none;
    border: none;
}
.modal-custom .action-close:hover,
.modal-popup .action-close:hover,
.modal-slide .action-close:hover {
    background: none;
    border: none;
}
.modal-custom .action-close.disabled,
.modal-popup .action-close.disabled,
.modal-slide .action-close.disabled,
.modal-custom .action-close[disabled],
.modal-popup .action-close[disabled],
.modal-slide .action-close[disabled],
fieldset[disabled] .modal-custom .action-close,
fieldset[disabled] .modal-popup .action-close,
fieldset[disabled] .modal-slide .action-close {
    pointer-events: none;
    opacity: 0.5;
}
.modal-custom .action-close > span,
.modal-popup .action-close > span,
.modal-slide .action-close > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.modal-custom .action-close:before,
.modal-popup .action-close:before,
.modal-slide .action-close:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 14px;
    line-height: 14px;
    color: inherit;
    content: '\e616';
    font-family: 'luma-icons';
    margin: 0;
    vertical-align: top;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.modal-custom .action-close:hover:before,
.modal-popup .action-close:hover:before,
.modal-slide .action-close:hover:before {
    color: inherit;
}
.modal-custom .action-close:active:before,
.modal-popup .action-close:active:before,
.modal-slide .action-close:active:before {
    color: inherit;
}
.modal-custom .action-close:hover:before,
.modal-popup .action-close:hover:before,
.modal-slide .action-close:hover:before {
    color: inherit;
}
.modal-popup .modal-title {
    border-bottom: 1px solid #f5f5f5;
    font-weight: 300;
    padding-bottom: 10px;
    font-size: 26px;
    margin-bottom: 0;
    min-height: 1em;
    word-wrap: break-word;
}
.modal-popup .action-close {
    padding: 20px;
}
.modal-slide .action-close {
    padding: 2.1rem 3rem;
}
.modal-slide .page-main-actions {
    margin-bottom: -12.9rem;
    margin-top: 2.1rem;
}
.modals-overlay {
    background-color: rgba(51, 51, 51, 0.55);
    bottom: 0;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
}
body._has-modal-custom .modal-custom-overlay {
    height: 100vh;
    left: 0;
    position: fixed;
    top: 0;
    width: 100vw;
    z-index: 899;
}
.modal-popup .action-close,
.modal-slide .action-close {
    padding: 0 30px 0 0;
}
.modal-popup .action-close::before,
.modal-slide .action-close::before {
    right: 30px;
    top: 50%;
    margin-top: -7px;
    color: inherit;
}
.modal-popup .action-close:hover,
.modal-slide .action-close:hover {
    color: #2f3943;
}
.modal-popup .action-close > span,
.modal-slide .action-close > span {
    clip: unset;
    height: auto;
    width: auto;
    line-height: 1.6rem;
    padding-right: 25px;
    position: relative;
    margin: 0;
    overflow: visible;
}
.main-faqs-page-index-view,
.main-faqs-page-index {
    width: 100%;
}
.main-faqs-page-index-view .faq_search,
.main-faqs-page-index .faq_search {
    padding: 8px 14px 14px 14px;
    background: #e4e4e4;
    margin: 0 0 20px 0;
    border-radius: 4px;
}
.main-faqs-page-index-view .faq_search .ui-helper-hidden-accessible,
.main-faqs-page-index .faq_search .ui-helper-hidden-accessible {
    padding: 0 0 6px;
    display: block;
    font-weight: bold;
}
.main-faqs-page-index-view ul,
.main-faqs-page-index ul {
    margin: 0;
    padding: 0;
    list-style: none;
}
.main-faqs-page-index-view ul li,
.main-faqs-page-index ul li {
    font-size: 14px;
    color: #000;
    text-decoration: none;
    margin: 0;
}
.main-faqs-page-index-view ul li a,
.main-faqs-page-index ul li a {
    color: #333;
    text-decoration: none;
    font-weight: bold;
}
.main-faqs-page-index-view .md_faq_main_left,
.main-faqs-page-index .md_faq_main_left {
    border: #ddd solid 1px;
    border-radius: 4px;
}
.main-faqs-page-index-view .md_faq_main_left ul li a,
.main-faqs-page-index .md_faq_main_left ul li a {
    padding: 14px 32px 14px 16px;
    border-bottom: #ddd solid 1px;
    display: block;
}
.main-faqs-page-index-view .md_faq_main_left ul li a:after,
.main-faqs-page-index .md_faq_main_left ul li a:after {
    content: "\f105";
    margin: 0px -16px 0 0;
    color: #333;
    font-size: 18px;
    font-family: 'FontAwesome';
    float: right;
    line-height: 18px;
}
.main-faqs-page-index-view .md_faq_main_left ul li a:hover,
.main-faqs-page-index .md_faq_main_left ul li a:hover {
    background: #1979c3;
    color: #fff;
}
.main-faqs-page-index-view .md_faq_main_left ul li a:hover:after,
.main-faqs-page-index .md_faq_main_left ul li a:hover:after {
    color: #fff;
}
.main-faqs-page-index-view .md_faq_main_left ul li.ui-state-active a,
.main-faqs-page-index .md_faq_main_left ul li.ui-state-active a {
    background: #ddd;
}
.main-faqs-page-index-view .md_faq_main_left ul li.ui-state-active a:after,
.main-faqs-page-index .md_faq_main_left ul li.ui-state-active a:after {
    color: #333;
}
.main-faqs-page-index-view .md_faq_main_left ul li.ui-state-active a:hover,
.main-faqs-page-index .md_faq_main_left ul li.ui-state-active a:hover {
    color: #333;
}
.main-faqs-page-index-view .md_faq_main_left ul li.active a,
.main-faqs-page-index .md_faq_main_left ul li.active a {
    background: #1979c3;
    color: #ffffff;
}
.main-faqs-page-index-view .md_faq_main_left ul li.active a:after,
.main-faqs-page-index .md_faq_main_left ul li.active a:after {
    color: #ffffff;
}
.main-faqs-page-index-view .md_faq_main_right ul li,
.main-faqs-page-index .md_faq_main_right ul li {
    margin: 0 0 18px 0;
}
.main-faqs-page-tags .md_faq_main_left {
    border: #ddd solid 1px;
    border-radius: 4px;
}
.main-faqs-page-tags .md_faq_main_left ul {
    margin: 0;
    padding: 0;
    list-style: none;
}
.main-faqs-page-tags .md_faq_main_left ul li a {
    padding: 14px 16px;
    border-bottom: #ddd solid 1px;
    display: block;
}
.main-faqs-page-tags .md_faq_main_left ul li a:hover {
    background: #1979c3;
    color: #fff;
}
.main-faqs-page-tags .md_faq_main_left ul li a:hover:after {
    color: #fff;
}
.main-faqs-page-tags .md_faq_main_left ul li.ui-state-active a {
    background: #ddd;
}
.main-faqs-page-tags .md_faq_main_left ul li.ui-state-active a:after {
    color: #333;
}
.main-faqs-page-tags .md_faq_main_left ul li.ui-state-active a:hover {
    color: #333;
}
.faqs-index-category .ui-autocomplete,
.faqs-index-search .ui-autocomplete,
.faqs-index-index .ui-autocomplete {
    background: #fff;
    border-radius: 0 0 4px 4px;
    box-shadow: 2px 2px 5px #cccccc;
    list-style: none;
    border: #ccc solid 1px;
    padding: 10px;
    width: 45% !important;
    margin: -1px 0 0 0 !important;
}
.faqs-index-category .ui-autocomplete .ui-autocomplete-category,
.faqs-index-search .ui-autocomplete .ui-autocomplete-category,
.faqs-index-index .ui-autocomplete .ui-autocomplete-category {
    font-weight: bold;
    font-size: 16px;
}
.faqs-index-category .ui-autocomplete .ui-menu-item a,
.faqs-index-search .ui-autocomplete .ui-menu-item a,
.faqs-index-index .ui-autocomplete .ui-menu-item a {
    font-size: 13px;
    text-decoration: none;
    font-weight: bold;
    cursor: pointer;
}
.faqs-index-category .ui-autocomplete .ui-menu-item a:hover,
.faqs-index-search .ui-autocomplete .ui-menu-item a:hover,
.faqs-index-index .ui-autocomplete .ui-menu-item a:hover {
    text-decoration: underline;
}
.faqs-index-category a.accordion-toggle.faq_questions_title,
.faqs-index-search a.accordion-toggle.faq_questions_title,
.faqs-index-index a.accordion-toggle.faq_questions_title {
    text-decoration: none;
}
.faqs-index-category .faq-index-list-view .faq-category,
.faqs-index-search .faq-index-list-view .faq-category,
.faqs-index-index .faq-index-list-view .faq-category {
    line-height: 100px;
}
.faqs-index-category .faqcat-block.col-md-6.col-xs-12,
.faqs-index-search .faqcat-block.col-md-6.col-xs-12,
.faqs-index-index .faqcat-block.col-md-6.col-xs-12 {
    height: auto;
    min-height: 309px;
}
.faqs-index-category .panel-group .panel-heading,
.faqs-index-search .panel-group .panel-heading,
.faqs-index-index .panel-group .panel-heading {
    border-bottom: 1px solid #cccccc;
}
.faqs-index-category .panel-default > .panel-heading + .panel-collapse > .panel-body,
.faqs-index-search .panel-default > .panel-heading + .panel-collapse > .panel-body,
.faqs-index-index .panel-default > .panel-heading + .panel-collapse > .panel-body {
    border: none;
    margin: 15px 0 20px 0px;
}
.faqs-index-category .faq-index-list-view .faqcat-desc,
.faqs-index-search .faq-index-list-view .faqcat-desc,
.faqs-index-index .faq-index-list-view .faqcat-desc {
    margin-bottom: 15px;
    text-align: justify;
}
.faqs-question-tags h3.faqcat-title {
    margin-top: 0px;
    margin-bottom: 2rem;
}
.faqs-question-view .mage-faq-social {
    float: right;
    margin-top: 20px;
}
.main-faqs-page-index-view .md_faq_main_heading h3 {
    clear: both;
    display: block;
    float: left;
    font-size: 18px;
    font-weight: bold;
    margin: 20px 0 16px;
    padding: 0;
    width: 100%;
}
.main-faqs-page-index-view .md_faq_main_heading h3 a {
    color: #000;
    text-decoration: none;
    background: #ddd;
    display: block;
    padding: 12px 32px 12px 16px;
    border-radius: 4px;
}
.main-faqs-page-index-view .md_faq_main_heading h3 a:after {
    content: "\f105";
    line-height: 18px;
    margin: 0px -16px 0 0;
    color: #333;
    font-size: 20px;
    font-family: 'FontAwesome';
    float: right;
}
.main-faqs-page-index-view .md_faq_main_heading h3 a:hover {
    background: #1979c3;
    color: #fff;
}
.main-faqs-page-index-view .md_faq_main_heading h3 a:hover:after {
    color: #fff;
}
.faqs-index-category .panel-default > .panel-heading + .panel-collapse > .panel-body {
    border: none;
}
.faq-index-list-view .faq-category {
    border-bottom: 1px solid #e6e6e6;
}
.faq-index-list-view .faq-category .faq-category-icon {
    height: 35px;
    margin: 0 25px 0 0;
    width: 35px;
    font-size: 45px;
    display: table-cell;
    vertical-align: middle;
    padding-right: 20px;
}
.faq-index-list-view .faq-category .faq-category-title {
    display: table-cell;
    vertical-align: middle;
    width: 100%;
}
.faq-index-list-view .faq-category-title h3 {
    margin: 0;
    font-size: 24px;
}
.faq-index-list-view .faqcat-desc {
    margin-top: 10px;
    font-size: 15px;
    line-height: 20px;
    color: #8a8a8a;
}
.faq-index-list-view .faq-category a {
    display: table;
    min-height: 75px;
    padding: 20px 10px;
    width: 100%;
    text-decoration: none;
}
.question-list .panel-heading {
    border: 1px solid #cccccc;
    cursor: pointer;
}
.panel_title {
    padding: 10px 0 10px 15px;
    margin: 0;
    font-size: 14px;
    line-height: 1.2;
}
.mage-faq-social {
    float: right;
}
.question-details .like-text {
    font-weight: 600;
    float: left;
    padding: 8px 8px 8px 0px;
}
.product-custometab-faq .main-faqs-page-index-view h3.faqcat-title {
    margin: 0 0 15px 0;
}
.catalog-product-view .panel-default > .panel-heading + .panel-collapse > .panel-body {
    border-top: none;
    margin: 15px 0 0 0;
}
.catalog-product-view .main-faqs-page-index-view ul,
.catalog-product-view .main-faqs-page-index ul {
    margin: 0 0 12px 0;
}
.catalog-product-view .main-faqs-page-index-view ul li,
.catalog-product-view .main-faqs-page-index ul li {
    padding: 0 0 5px 0;
}
.main-faqs-page-index .block-title,
.main-faqs-page-tags .block-title {
    border-bottom: 1px solid #cccccc;
    background-color: #f4f4f4;
    color: #636363;
    text-transform: uppercase;
    padding: 14px 16px;
}
.main-faqs-page-tags .tags-all-list {
    margin: 0;
    padding: 0;
    list-style: none;
}
.main-faqs-page-tags .md_faq_main_left ul li a {
    border-bottom: 0 none;
    border-radius: 10px;
    color: #333333;
    display: block;
    font-weight: bold;
    padding: 10px 8px;
    text-decoration: none;
}
.main-faqs-page-tags .faq-tag-list {
    display: inline-block;
    margin: 5px 0 5px 5px;
}
.main-faqs-page-tags .faq-tag-list.active a {
    background: #1979c3;
    color: #ffffff;
}
.panel_title {
    padding: 10px 0 10px 15px;
    margin: 0;
    font-size: 14px;
    line-height: 1.2;
}
.faqs-cat-image,
.faqs-cat-desc {
    margin-bottom: 20px;
}
.faqs-cat-desc {
    text-align: justify;
}
.question-list .panel-heading {
    border: 1px solid #cccccc;
    cursor: pointer;
}
.product-custometab-faq .block-title {
    font-weight: bold;
    font-size: 18px;
    color: #333;
    padding: 0 0 10px 0;
    margin: 0 0 20px 0;
    border-bottom: #ddd dashed 1px;
}
.product-custometab-faq .faq-block-content {
    margin: 0 0 30px 0;
    display: block;
}
.product-custometab-faq .faq-block-content ul {
    list-style: none;
    margin: 0;
    padding: 0;
}
.product-custometab-faq .faq-block-content ul li {
    margin: 0 0 18px 0;
}

.faqs-question-tags .panel-default > .panel-heading + .panel-collapse > .panel-body {
    border: none;
}
/**
 * Copyright 2016 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *  http://aws.amazon.com/apache2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
/**
 * Copyright 2016 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *  http://aws.amazon.com/apache2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
/**
* Amazon Pay variables
**/
.amazon-button-container {
    display: table;
    margin: 0 0 22px;
}
.amazon-button-container__cell {
    display: table-cell;
    min-width: 45px;
    position: relative;
}
.amazon-button-container img {
    display: block;
}
.amazon-button-container .field-tooltip {
    display: none;
    margin: -12px 0 0;
    right: 5px;
    top: 50%;
}
.checkout-shipping-address .amazon-button-container {
    margin: 22px 0;
}
.login-container .block-amazon-login .actions-toolbar {
    margin-top: 25px;
}
.amazon-validate-container .block .block-title {
    font-size: 1.8rem;
}
.amazon-validate-container .fieldset:after {
    margin: 10px 0 0;
    content: attr(data-hasrequired);
    display: block;
    letter-spacing: normal;
    word-spacing: normal;
    color: #e02b27;
    font-size: 1.2rem;
}
.amazon-validate-container .fieldset:after {
    margin-top: 35px;
}
@media all and (max-width: 768px) {
    .amazon-button-container {
        width: 100%;
    }
}
.amazon-logout-widget {
    display: none;
}
/**
 * Copyright 2016 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *  http://aws.amazon.com/apache2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
/** mini cart styling **/
.product-social-links,
.amazon-minicart-container {
    width: 100%;
    display: block;
    text-align: center;
}
.product-social-links .amazon-button-container,
.amazon-minicart-container .amazon-button-container {
    display: inline-block;
    margin: 12px 0 4px 0;
}
.product-social-links .field-tooltip .field-tooltip-content,
.amazon-minicart-container .field-tooltip .field-tooltip-content {
    top: 38px;
    left: -245px;
}
.product-social-links .field-tooltip .field-tooltip-content:before,
.amazon-minicart-container .field-tooltip .field-tooltip-content:before {
    border: none;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid #666666;
    top: -10px;
    left: 244px;
}
.product-social-links .field-tooltip .field-tooltip-content:after,
.amazon-minicart-container .field-tooltip .field-tooltip-content:after {
    border: none;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid #f4f4f4;
    top: -8px;
    left: 244px;
}
.amazon-addtoCart {
    display: block;
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
}
.revert-checkout {
    margin-top: 20px;
    display: block;
}
.centered-button {
    text-align: center;
    display: inline-block;
}
.catalog-product-view .centered-button {
    display: table;
}
.checkout-payment-method .amazon-sandbox-simulator {
    border-bottom: 1px solid #cccccc;
}
.checkout-payment-method .amazon-sandbox-simulator .payment-option-title {
    border-top: none;
    padding-top: 0;
}
.checkout-payment-method .amazon-sandbox-simulator .amazon-sandbox-simulator-scenario {
    margin-bottom: 10px;
}
.checkout-methods-items .centered-button {
    margin-bottom: 0;
}
@media all and (max-width: 768px) {
    .checkout-methods-items .centered-button {
        width: auto;
    }
}
.checkout-methods-items .field-tooltip {
    display: none;
}
.checkout-methods-items .field-tooltip .field-tooltip-content {
    right: -10px;
    top: 40px;
    left: auto;
}
.checkout-methods-items .field-tooltip .field-tooltip-content:before,
.checkout-methods-items .field-tooltip .field-tooltip-content:after {
    border: 10px solid transparent;
    height: 0;
    width: 0;
    margin-top: -21px;
    right: 10px;
    left: auto;
    top: 0;
}
.checkout-methods-items .field-tooltip .field-tooltip-content:before {
    border-bottom-color: #666666;
}
.checkout-methods-items .field-tooltip .field-tooltip-content:after {
    border-bottom-color: #f5f5f5;
    top: 1px;
}
@media all and (max-width: 768px) {
    .checkout-methods-items .field-tooltip .field-tooltip-content {
        right: -56px;
        top: 38px;
        left: auto;
    }
    .checkout-methods-items .field-tooltip .field-tooltip-content:before,
    .checkout-methods-items .field-tooltip .field-tooltip-content:after {
        right: 56px;
    }
}
.amazon-widget {
    max-width: 400px;
    height: 228px;
}
.amazon-widget--address {
    margin-top: 20px;
}
.amazon-addresses {
    margin-top: 20px;
}
.amazon-addresses .amazon-address {
    margin-bottom: 20px;
}
.checkout-payment-method .amazon-payment-method .payment-method-title {
    visibility: hidden;
    padding-bottom: 0;
}
/** Prevent movement after a soft decline to other payment steps **/
.opc-progress-bar-item._complete.lock-step {
    cursor: none;
    pointer-events: none;
}
@media all and (max-width: 768px) {
    .amazon-minicart-container .amazon-button-container {
        width: auto;
    }
}
.multi-checkout-fields label {
    display: block;
    margin-bottom: 7px;
}
.multi-checkout-fields .field {
    margin: 1.5em 0;
}
.multi-checkout-fields .field.checkbox label {
    display: inline-block;
    margin: 0 0 0 12px;
}
.collection-point-country,
.collection-point-postalcode,
.collection-point-button {
    display: inline-block;
    padding: 20px 10px 0 0;
}
.collection-point-list {
    border: 1px solid #c2c2c2;
    max-height: 200px;
    overflow: auto;
}
.collection-point-postalcode {
    max-width: 9em;
    vertical-align: top;
}
.field-error {
    color: #e02b27;
    font-size: 1.2rem;
}
table.collection-point-opening-hours > tbody > tr > td {
    padding-bottom: 2px;
}
.collection-point-message {
    padding-top: 10px;
}
.box-order-shipping-address .box-subtitle {
    font-weight: 600;
}
.box-order-shipping-address .order-shipping-secondary-address {
    margin-top: 1rem;
}
.column .block-addbysku .fieldset {
    margin: 0;
}
.column .block-addbysku .fieldset .fields {
    position: relative;
}
.column .block-addbysku .fieldset .fields .field {
    display: inline-block;
    margin-bottom: 10px;
    vertical-align: top;
}
.column .block-addbysku .fieldset .fields .actions-toolbar {
    position: absolute;
    right: 0;
    top: 4px;
}
.column .block-addbysku .sku {
    margin-right: -155px;
    padding-right: 160px;
    width: 100%;
}
.column .block-addbysku .skus > .label {
    margin-top: 10px;
}
.column .block-addbysku .qty .qty + .mage-error {
    width: 80px;
}
.column .block-addbysku .block-content .actions-toolbar {
    clear: both;
    margin: 0 0 20px;
    text-align: left;
}
.column .block-addbysku .block-content .actions-toolbar:before,
.column .block-addbysku .block-content .actions-toolbar:after {
    content: '';
    display: table;
}
.column .block-addbysku .block-content .actions-toolbar:after {
    clear: both;
}
.column .block-addbysku .block-content .actions-toolbar .primary {
    float: left;
}
.column .block-addbysku .block-content .actions-toolbar .primary,
.column .block-addbysku .block-content .actions-toolbar .secondary {
    display: inline-block;
}
.column .block-addbysku .block-content .actions-toolbar .primary a.action,
.column .block-addbysku .block-content .actions-toolbar .secondary a.action {
    display: inline-block;
}
.column .block-addbysku .block-content .actions-toolbar .primary .action {
    margin: 0 15px 0 0;
}
.column .block-addbysku .block-content .actions-toolbar .secondary a.action {
    margin-top: 6px;
}
.column .block-addbysku .block-content .actions-toolbar .primary,
.column .block-addbysku .block-content .actions-toolbar .secondary {
    display: block;
    float: none;
}
.column .block-addbysku .block-content .actions-toolbar > .secondary {
    text-align: left;
}
.column .block-addbysku .reset {
    margin: 10px 0;
}
.column .block-addbysku .note {
    display: block;
    margin: 0 0 20px;
}
.block-cart-failed {
    margin-top: 40px;
}
.block-cart-failed .block-title {
    font-size: 2.6rem;
}
.block-cart-failed .block-title strong {
    font-weight: 300;
}
.block-cart-failed .actions {
    text-align: center;
}
.block-cart-failed .actions .action {
    margin-bottom: 10px;
}
.payment-method-braintree .hosted-control {
    background: #ffffff;
    background-clip: padding-box;
    border: 1px solid #c2c2c2;
    border-radius: 1px;
    font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-size: 14px;
    height: 32px;
    line-height: 1.42857143;
    padding: 0 9px;
    vertical-align: baseline;
    width: 100%;
    box-sizing: border-box;
    position: relative;
    width: 225px;
}
.payment-method-braintree .hosted-control:disabled {
    opacity: 0.5;
}
.payment-method-braintree .hosted-control::-moz-placeholder {
    color: #c2c2c2;
}
.payment-method-braintree .hosted-control::-webkit-input-placeholder {
    color: #c2c2c2;
}
.payment-method-braintree .hosted-control:-ms-input-placeholder {
    color: #c2c2c2;
}
.payment-method-braintree .hosted-control.braintree-hosted-fields-focused {
    border-color: #006bb4;
}
.payment-method-braintree .hosted-control.braintree-hosted-fields-invalid:not(.braintree-hosted-fields-focused) {
    border-color: #e02b27;
}
.payment-method-braintree .hosted-control.braintree-hosted-fields-invalid:not(.braintree-hosted-fields-focused) ~ .hosted-error {
    height: auto;
    opacity: 1;
}
.payment-method-braintree .hosted-control.braintree-hosted-fields-valid {
    border-color: #006400;
}
.payment-method-braintree .hosted-control.hosted-cid {
    width: 5rem;
}
.payment-method-braintree .hosted-control.hosted-date {
    float: left;
    margin-bottom: 7px;
    width: 6rem;
}
.payment-method-braintree .hosted-control.hosted-date:first-of-type {
    margin-right: 50px;
}
.payment-method-braintree .hosted-control.hosted-date:first-of-type:after {
    content: '/';
    display: inline;
    padding-bottom: inherit;
    padding-top: inherit;
    position: absolute;
    right: -25px;
    top: 5px;
}
.payment-method-braintree .field-tooltip {
    right: 0;
}
.payment-method-braintree .hosted-error {
    clear: both;
    color: #e02b27;
    font-size: 1.2rem;
    height: 0;
    margin-top: 7px;
    opacity: 0;
}
.braintree-paypal-logo {
    margin-top: 10px;
    text-align: center;
}
.braintree-paypal-account {
    padding: 15px 0 15px 50px;
    position: relative;
    display: inline-block;
}
.braintree-paypal-account:before {
    background-image: url('../Magento_Braintree/images/paypal-small.png');
    background-position-x: 0;
    background-position-y: 0;
    line-height: 16px;
    vertical-align: middle;
    height: 16px;
    width: 16px;
    background-repeat: no-repeat;
    content: '';
    display: inline-block;
}
.braintree-paypal-account:before {
    left: 17px;
    margin-top: -8px;
    position: absolute;
    top: 50%;
}
.braintree-paypal-account .payment-method-type {
    font-weight: 700;
}
.braintree-paypal-account .payment-method-description {
    color: #575757;
}
.braintree-paypal-fieldset {
    border: 0;
    margin: 0;
    padding: 0;
}
.action-braintree-paypal-logo {
    background-image: none;
    background: none;
    -moz-box-sizing: content-box;
    border: 0;
    box-shadow: none;
    line-height: inherit;
    margin: 0;
    padding: 0;
    text-decoration: none;
    text-shadow: none;
    font-weight: 400;
}
.action-braintree-paypal-logo:focus,
.action-braintree-paypal-logo:active {
    background: none;
    border: none;
}
.action-braintree-paypal-logo:hover {
    background: none;
    border: none;
}
.action-braintree-paypal-logo.disabled,
.action-braintree-paypal-logo[disabled],
fieldset[disabled] .action-braintree-paypal-logo {
    pointer-events: none;
    opacity: 0.5;
}
.action-braintree-paypal-logo img {
    margin: 0;
    width: 114px;
}
.payment-method .payment-method-content .actions-toolbar.braintree-paypal-actions {
    margin-left: 0;
}
.account .table .col.paypal-account img {
    vertical-align: middle;
}
.account .table .col.paypal-account + .actions {
    vertical-align: middle;
}
.block-category-event {
    background: #f0f0f0;
    color: #333333;
    padding: 10px 0 15px;
    text-align: center;
}
.block-category-event.block:last-child {
    margin-bottom: 10px;
}
.block-category-event .block-title {
    margin: 0 0 10px;
}
.block-category-event .block-title strong {
    font-size: 1.5rem;
    font-weight: 300;
}
.block-category-event .ticker {
    margin: 0;
    padding: 0;
    list-style: none none;
}
.block-category-event .ticker li {
    display: none;
    margin: 0 15px;
}
.block-category-event .ticker .value {
    font-size: 1.8rem;
    font-weight: 300;
}
.block-category-event .ticker .label {
    font-size: 1rem;
    display: block;
    text-transform: lowercase;
}
.block-category-event .dates {
    display: table;
    margin: 0 auto;
}
.block-category-event .dates .start,
.block-category-event .dates .end {
    display: table-cell;
    vertical-align: top;
}
.block-category-event .dates .date {
    font-size: 1.8rem;
}
.block-category-event .dates .time {
    display: block;
}
.block-category-event .dates .start {
    padding-right: 40px;
    position: relative;
}
.block-category-event .dates .start:after {
    font-size: 1.8rem;
    content: '\2013';
    display: block;
    position: absolute;
    right: 15px;
    top: 0;
}
.cart-summary .block.giftcard .action.check {
    line-height: 1.2rem;
    padding: 5px 8px;
    font-size: 1.1rem;
    margin-top: 20px;
}
.page-product-giftcard .giftcard-amount {
    margin-bottom: 0;
    text-align: left;
}
.page-product-giftcard .giftcard-amount .field:last-child {
    padding-bottom: 20px;
}
.page-product-giftcard .product-options-wrapper .field:first-of-type {
    margin-top: 20px;
}
.giftcard-account .please-wait {
    display: none;
}
.giftcard-account .form-giftcard-redeem .giftcard.info {
    margin-bottom: 30px;
}
.opc-wrapper .form-giftcard-account {
    max-width: 500px;
}
.opc-wrapper .form-giftcard-account .field {
    margin: 0 0 20px;
}
.opc-wrapper .giftcard-account-info {
    margin: 0 0 20px;
}
.opc-wrapper .giftcard-account-info .giftcard-account-number {
    margin: 0 0 10px;
}
.gift-message .field {
    margin-bottom: 20px;
}
.gift-message .field .label {
    color: #666666;
    font-weight: 400;
}
.gift-options .actions-toolbar .action-cancel {
    display: none;
}
.gift-options-title {
    margin: 0 0 20px;
}
.gift-options-content .fieldset {
    margin: 0 0 20px;
}
.gift-summary .actions-toolbar > .secondary {
    float: none;
}
.gift-summary .actions-toolbar > .secondary .action {
    margin: 10px 20px 0 0;
}
.action-gift {
    display: inline-block;
    text-decoration: none;
}
.action-gift:after {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 32px;
    line-height: 16px;
    color: inherit;
    content: '\e622';
    font-family: 'luma-icons';
    margin: 0;
    vertical-align: top;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.action-gift:hover:after {
    color: inherit;
}
.action-gift:active:after {
    color: inherit;
}
.action-gift.active:after,
.action-gift._active:after {
    content: '\e621';
}
.cart.table-wrapper .gift-content {
    box-sizing: border-box;
    clear: left;
    display: none;
    float: left;
    margin: 10px 0 20px;
    text-align: left;
    width: 100%;
}
.cart.table-wrapper .gift-content._active {
    display: block;
}
.gift-item-block {
    margin: 0;
}
.gift-item-block._active .title:after {
    content: '\e621';
}
.gift-item-block .title {
    font-weight: 700;
    border-radius: 3px;
    background-image: none;
    background: #eeeeee;
    border: 1px solid #cccccc;
    color: #333333;
    cursor: pointer;
    font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-weight: 600;
    margin: 20px 0 0;
    padding: 7px 15px;
    font-size: 1.4rem;
    line-height: 1.6rem;
    box-sizing: border-box;
    vertical-align: middle;
    display: inline-block;
    text-decoration: none;
}
.gift-item-block .title:after {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 32px;
    line-height: 16px;
    color: inherit;
    content: '\e622';
    font-family: 'luma-icons';
    margin: 0;
    vertical-align: top;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.gift-item-block .title:hover:after {
    color: inherit;
}
.gift-item-block .title:active:after {
    color: inherit;
}
.gift-item-block .title:focus,
.gift-item-block .title:active {
    background: #bae588;
    border: 1px solid #cccccc;
    color: #333333;
}
.gift-item-block .title:hover {
    background: #e1e1e1;
    border: 1px solid #cccccc;
    color: #333333;
}
.gift-item-block .title.disabled,
.gift-item-block .title[disabled],
fieldset[disabled] .gift-item-block .title {
    opacity: 0.5;
    cursor: default;
    pointer-events: none;
}
.gift-item-block .title:active {
    box-shadow: inset 0 1px 0 0 #ffffff, inset 0 -1px 0 0 rgba(204, 204, 204, 0.3);
}
.gift-item-block .content {
    border-bottom: 1px solid #f5f5f5;
    padding: 10px 10px 20px;
    position: relative;
}
.order-details-items .order-gift-message .item-options dt,
.order-details-items .block-order-details-gift-message .item-options dt {
    font-weight: 400;
    margin: 10px 0;
}
.order-details-items .order-gift-message dt:after,
.order-details-items .block-order-details-gift-message dt:after {
    content: '';
}
.order-details-items .order-gift-message .item-message,
.order-details-items .block-order-details-gift-message .item-message {
    clear: left;
}
.order-details-items .order-items .action.show {
    display: inline-block;
    text-decoration: none;
    padding-right: 20px;
    position: relative;
}
.order-details-items .order-items .action.show:after {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 22px;
    line-height: inherit;
    color: inherit;
    content: '\e622';
    font-family: 'luma-icons';
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.order-details-items .order-items .action.show:after {
    position: absolute;
    right: 0;
    top: -4px;
}
.order-details-items .order-items .action.show.expanded:after {
    content: '\e621';
}
.block-order-details-gift-message {
    border-top: 1px solid #cccccc;
    padding-top: 10px;
}
.form-giftregistry-share .actions-toolbar:not(:last-child),
.form-giftregistry-edit .actions-toolbar:not(:last-child) {
    margin-bottom: 40px;
}
.form-giftregistry-share .fieldset .nested .field:not(.choice) .control,
.form-giftregistry-edit .fieldset .nested .field:not(.choice) .control {
    width: 100%;
}
.table-giftregistry-items .product-image {
    float: left;
    margin-right: 10px;
}
.cart-summary .form.giftregistry .fieldset {
    margin: 25px 0 5px;
}
.cart-summary .form.giftregistry .fieldset .field {
    margin: 0 0 10px;
}
.cart-summary .form.giftregistry .fieldset .field .control {
    float: none;
    width: auto;
}
.cart-summary .form.giftregistry .fieldset .action.primary {
    margin-bottom: 0;
}
.form-giftregistry-search {
    margin-bottom: 60px;
}
.form-giftregistry-search .fields-specific-options .field:nth-last-of-type(1) {
    margin-bottom: 0;
}
.block-giftregistry-results .toolbar-giftregistry-results {
    text-align: center;
}
.block-giftregistry .actions-toolbar {
    margin: 17px 0 0;
}
.block-giftregistry-shared .item-options .item-value {
    margin: 0 0 25px;
}
.block-giftregistry-shared-items .item {
    border-bottom: 1px solid #cccccc;
}
.block-giftregistry-shared-items .col {
    padding: 20px 8px 10px;
}
.block-giftregistry-shared-items .col .price-box {
    display: inline-block;
}
.block-giftregistry-shared-items .col .price-wrapper {
    font-size: 1.8rem;
    display: block;
    font-weight: 700;
    white-space: nowrap;
}
.block-giftregistry-shared-items .product .item-options {
    margin-bottom: 0;
}
.block-giftregistry-shared-items .product-item-photo {
    display: block;
    max-width: 65px;
    padding: 10px 0;
}
.block-giftregistry-shared-items .product-item-name {
    display: block;
    margin: 0 0 5px;
}
.gift-wrapping .label {
    margin: 0 0 8px;
    display: inline-block;
}
.gift-wrapping .field {
    margin-bottom: 10px;
}
.gift-wrapping-info {
    font-weight: 300;
    font-size: 18px;
    margin: 20px 0;
}
.gift-wrapping-list {
    list-style: none;
    margin: 20px 0 10px;
    padding: 0;
}
.gift-wrapping-list .no-image {
    background: #f2f2f2;
    display: inline-block;
    text-decoration: none;
}
.gift-wrapping-list .no-image > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.gift-wrapping-list .no-image:after {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 38px;
    line-height: 4rem;
    color: #494949;
    content: '\e62a';
    font-family: 'luma-icons';
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.gift-wrapping-list .no-image:after {
    display: block;
}
.gift-wrapping-preview,
.gift-wrapping-item > span {
    cursor: pointer;
    display: block;
    overflow: hidden;
    position: relative;
    z-index: 1;
}
.gift-wrapping-item {
    float: left;
    margin: 0 10px 10px 0;
}
.gift-wrapping-item:last-child {
    margin-right: 0;
}
.gift-wrapping-item._active span:before {
    border: 3px solid #333333;
    box-sizing: border-box;
    content: '';
    display: block;
    height: 100%;
    position: absolute;
    width: 100%;
    z-index: 2;
}
.gift-wrapping-item > span {
    height: 4rem;
    width: 4rem;
}
.gift-wrapping-title {
    margin: 10px 0;
}
.gift-wrapping-title .action-remove {
    margin-left: 5px;
}
.gift-options-content .fieldset > .field {
    margin: 0 0 5px;
}
.gift-options-content .fieldset > .field:before {
    display: none;
}
.gift-wrapping-summary .gift-wrapping-title {
    border: 0;
    display: inline-block;
    padding: 0;
}
.opc-wrapper .data.table .gift-wrapping,
.table-order-items .gift-wrapping,
.table-order-review .gift-wrapping {
    margin: 20px 0;
}
.opc-wrapper .data.table .gift-wrapping .title,
.table-order-items .gift-wrapping .title,
.table-order-review .gift-wrapping .title {
    float: none;
}
.opc-wrapper .data.table .gift-wrapping .content,
.table-order-items .gift-wrapping .content,
.table-order-review .gift-wrapping .content {
    margin: 0;
}
.opc-wrapper .data.table .gift-wrapping .content > img,
.table-order-items .gift-wrapping .content > img,
.table-order-review .gift-wrapping .content > img {
    float: left;
    margin: 0 10px 0 0;
    max-height: 50px;
    max-width: 50px;
}
.gift-summary .price-box,
.gift-options .price-box {
    margin-left: 22px;
}
.gift-summary .regular-price:before,
.gift-options .regular-price:before {
    content: attr(data-label) ': ';
}
.gift-summary .regular-price .price,
.gift-options .regular-price .price {
    font-weight: 700;
}
.order-options .item {
    padding-bottom: 20px;
}
.order-options .product {
    margin-bottom: 10px;
}
.order-options .product-image-container {
    float: left;
    padding-right: 10px;
}
.order-options .gift-wrapping {
    margin-bottom: 10px;
}
.form-add-invitations .action.add {
    display: inline-block;
    text-decoration: none;
    line-height: normal;
    padding: 2px 0;
    width: auto;
}
.form-add-invitations .action.add > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.form-add-invitations .action.add:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 31px;
    line-height: inherit;
    color: inherit;
    content: '\e61c';
    font-family: 'luma-icons';
    margin: 0;
    vertical-align: top;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.form-add-invitations .action.add:hover:before {
    color: inherit;
}
.form-add-invitations .action.add:active:before {
    color: inherit;
}
.map-popup-wrapper.popup .action.close {
    top: 10px;
}
.map-popup {
    background: #ffffff;
    border: 1px solid #adadad;
    padding: 22px;
    width: 100%;
    box-shadow: 0 3px 3px rgba(0, 0, 0, 0.15);
    transition: opacity 0.3s linear;
    left: 0;
    margin-top: 10px;
    position: absolute;
    top: 0;
    z-index: 1001;
    display: none;
    opacity: 0;
    box-sizing: border-box;
}
.map-popup .popup-header {
    margin: 0 0 25px;
    padding-right: 16px;
}
.map-popup .popup-header .title {
    font-weight: 300;
    line-height: 1.1;
    font-size: 2.6rem;
    margin-top: 2.5rem;
    margin-bottom: 2rem;
}
.map-popup .popup-content {
    margin: 10px 0 0;
}
.map-popup .popup-actions .action.close {
    position: absolute;
    display: inline-block;
    background-image: none;
    background: none;
    -moz-box-sizing: content-box;
    border: 0;
    box-shadow: none;
    line-height: inherit;
    margin: 0;
    padding: 0;
    text-decoration: none;
    text-shadow: none;
    font-weight: 400;
    right: 10px;
    top: 10px;
}
.map-popup .popup-actions .action.close > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.map-popup .popup-actions .action.close:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 16px;
    line-height: 16px;
    color: #8c8c8c;
    content: '\e616';
    font-family: 'luma-icons';
    margin: 0;
    vertical-align: top;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.map-popup .popup-actions .action.close:hover:before {
    color: #737373;
}
.map-popup .popup-actions .action.close:active:before {
    color: #737373;
}
.map-popup .popup-actions .action.close:focus,
.map-popup .popup-actions .action.close:active {
    background: none;
    border: none;
}
.map-popup .popup-actions .action.close:hover {
    background: none;
    border: none;
}
.map-popup .popup-actions .action.close.disabled,
.map-popup .popup-actions .action.close[disabled],
fieldset[disabled] .map-popup .popup-actions .action.close {
    pointer-events: none;
    opacity: 0.5;
}
.map-popup.active {
    opacity: 1;
}
.map-popup .map-info-price {
    margin-bottom: 15px;
}
.map-popup .map-info-price .price-box {
    margin: 0 0 20px;
}
.map-popup .map-info-price .price-box .label:after {
    content: ': ';
}
.map-popup .regular-price .price-label {
    display: none;
}
.map-old-price:not(:last-child),
.product-info-price .map-show-info:not(:last-child) {
    margin-right: 10px;
}
.map-old-price {
    text-decoration: none;
}
.map-old-price .price-wrapper {
    text-decoration: line-through;
}
.map-form-addtocart {
    display: inline-block;
    text-align: center;
}
.map-form-addtocart img {
    display: block;
    margin: 0 auto;
}
button.map-show-info {
    background-image: none;
    -moz-box-sizing: content-box;
    box-shadow: none;
    line-height: inherit;
    text-shadow: none;
    font-weight: 400;
    line-height: 1.42857143;
    margin: 0;
    padding: 0;
    color: #1979c3;
    text-decoration: none;
    background: none;
    border: 0;
    display: inline;
}
button.map-show-info:focus,
button.map-show-info:active {
    background: none;
    border: none;
}
button.map-show-info:hover {
    background: none;
    border: none;
}
button.map-show-info.disabled,
button.map-show-info[disabled],
fieldset[disabled] button.map-show-info {
    pointer-events: none;
    opacity: 0.5;
}
button.map-show-info:visited {
    color: #1979c3;
    text-decoration: none;
}
button.map-show-info:hover {
    color: #006bb4;
    text-decoration: underline;
}
button.map-show-info:active {
    color: #677752;
    text-decoration: underline;
}
button.map-show-info:hover {
    color: #006bb4;
}
button.map-show-info:hover,
button.map-show-info:active,
button.map-show-info:focus {
    background: none;
    border: 0;
}
button.map-show-info.disabled,
button.map-show-info[disabled],
fieldset[disabled] button.map-show-info {
    color: #1979c3;
    opacity: 0.5;
    cursor: default;
    pointer-events: none;
    text-decoration: underline;
}
.wishlist.split.button {
    display: inline-block;
    position: relative;
    vertical-align: middle;
}
.wishlist.split.button:before,
.wishlist.split.button:after {
    content: '';
    display: table;
}
.wishlist.split.button:after {
    clear: both;
}
.wishlist.split.button .action.split {
    float: left;
    margin: 0;
}
.wishlist.split.button .action.toggle {
    float: right;
    margin: 0;
}
.wishlist.split.button button.action.split {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0;
}
.wishlist.split.button button + .action.toggle {
    border-bottom-left-radius: 0;
    border-left: 0;
    border-top-left-radius: 0;
}
.wishlist.split.button .action.toggle {
    display: inline-block;
    text-decoration: none;
}
.wishlist.split.button .action.toggle > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.wishlist.split.button .action.toggle:after {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 22px;
    line-height: inherit;
    color: #999999;
    content: '\e622';
    font-family: 'luma-icons';
    margin: 0;
    vertical-align: top;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.wishlist.split.button .action.toggle:hover:after {
    color: #333333;
}
.wishlist.split.button .action.toggle:active:after {
    color: inherit;
}
.wishlist.split.button .action.toggle.active {
    display: inline-block;
    text-decoration: none;
}
.wishlist.split.button .action.toggle.active > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.wishlist.split.button .action.toggle.active:after {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 22px;
    line-height: inherit;
    color: #999999;
    content: '\e621';
    font-family: 'luma-icons';
    margin: 0;
    vertical-align: top;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.wishlist.split.button .action.toggle.active:hover:after {
    color: #333333;
}
.wishlist.split.button .action.toggle.active:active:after {
    color: inherit;
}
.wishlist.split.button .items {
    margin: 0;
    padding: 0;
    list-style: none none;
    background: #ffffff;
    border: 1px solid #bbbbbb;
    margin-top: 4px;
    min-width: 200px;
    z-index: 100;
    box-sizing: border-box;
    display: none;
    position: absolute;
    top: 100%;
    box-shadow: 0 3px 3px rgba(0, 0, 0, 0.15);
}
.wishlist.split.button .items li {
    margin: 0;
    padding: 5px 5px 5px 23px;
}
.wishlist.split.button .items li:hover {
    background: #e8e8e8;
    cursor: pointer;
}
.wishlist.split.button .items:before,
.wishlist.split.button .items:after {
    border-bottom-style: solid;
    content: '';
    display: block;
    height: 0;
    position: absolute;
    width: 0;
}
.wishlist.split.button .items:before {
    border: 6px solid;
    border-color: transparent transparent #ffffff transparent;
    z-index: 99;
}
.wishlist.split.button .items:after {
    border: 7px solid;
    border-color: transparent transparent #bbbbbb transparent;
    z-index: 98;
}
.wishlist.split.button .items:before {
    left: 10px;
    top: -12px;
}
.wishlist.split.button .items:after {
    left: 9px;
    top: -14px;
}
.wishlist.split.button.active {
    overflow: visible;
}
.wishlist.split.button.active .items {
    display: block;
}
.wishlist.split.button .items {
    text-align: left;
}
.wishlist.split.button .items .item:last-child:hover {
    background: #e8e8e8;
}
.table-comparison .wishlist.split.button > .action.split:before,
.product-items .wishlist.split.button > .action.split:before {
    content: '\e601';
}
.table-comparison .wishlist.split.button > .action:active,
.product-items .wishlist.split.button > .action:active,
.table-comparison .wishlist.split.button > .action:focus,
.product-items .wishlist.split.button > .action:focus,
.table-comparison .wishlist.split.button > .action:hover,
.product-items .wishlist.split.button > .action:hover {
    color: #333333;
}
.table-comparison .wishlist.split.button > .action.toggle:before,
.product-items .wishlist.split.button > .action.toggle:before {
    border-left: 1px solid #e5e5e5;
    content: '';
    float: left;
    height: 15px;
    margin-top: 3px;
}
.product-info-main .wishlist.split.button .action.split,
.block-bundle-summary .wishlist.split.button .action.split,
.product-add-form .wishlist.split.button .action.split,
.product-info-main .wishlist.split.button .action.toggle,
.block-bundle-summary .wishlist.split.button .action.toggle,
.product-add-form .wishlist.split.button .action.toggle {
    line-height: 1.2rem;
    padding: 5px 8px;
    font-size: 1.1rem;
}
.product-info-main .wishlist.split.button .action.toggle,
.block-bundle-summary .wishlist.split.button .action.toggle,
.product-add-form .wishlist.split.button .action.toggle {
    padding: 0;
}
.cart.table-wrapper .wishlist.split.button {
    margin-bottom: 10px;
}
.cart.table-wrapper .wishlist.split.button > .action.toggle {
    padding: 4px 5px;
}
.wishlist.window.popup {
    box-sizing: border-box;
    background: #ffffff;
    border: 1px solid #adadad;
    padding: 22px;
    width: 100%;
    box-shadow: 0 3px 3px rgba(0, 0, 0, 0.15);
    transition: opacity 0.3s linear;
    bottom: 0;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 1001;
    display: none;
    opacity: 0;
}
.wishlist.window.popup .popup-header {
    margin: 0 0 25px;
    padding-right: 16px;
}
.wishlist.window.popup .popup-header .title {
    font-weight: 300;
    line-height: 1.1;
    font-size: 2.6rem;
    margin-top: 2.5rem;
    margin-bottom: 2rem;
}
.wishlist.window.popup .popup-actions .action.close {
    position: absolute;
    display: inline-block;
    background-image: none;
    background: none;
    -moz-box-sizing: content-box;
    border: 0;
    box-shadow: none;
    line-height: inherit;
    margin: 0;
    padding: 0;
    text-decoration: none;
    text-shadow: none;
    font-weight: 400;
    right: 10px;
    top: 10px;
}
.wishlist.window.popup .popup-actions .action.close > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.wishlist.window.popup .popup-actions .action.close:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 16px;
    line-height: 16px;
    color: #8c8c8c;
    content: '\e616';
    font-family: 'luma-icons';
    margin: 0;
    vertical-align: top;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.wishlist.window.popup .popup-actions .action.close:hover:before {
    color: #737373;
}
.wishlist.window.popup .popup-actions .action.close:active:before {
    color: #737373;
}
.wishlist.window.popup .popup-actions .action.close:focus,
.wishlist.window.popup .popup-actions .action.close:active {
    background: none;
    border: none;
}
.wishlist.window.popup .popup-actions .action.close:hover {
    background: none;
    border: none;
}
.wishlist.window.popup .popup-actions .action.close.disabled,
.wishlist.window.popup .popup-actions .action.close[disabled],
fieldset[disabled] .wishlist.window.popup .popup-actions .action.close {
    pointer-events: none;
    opacity: 0.5;
}
.wishlist.window.popup.active {
    opacity: 1;
}
.wishlist.window.popup.active {
    display: block;
}
.wishlist.overlay {
    transition: opacity 0.15s linear;
    background: #8c8c8c;
    z-index: 899;
    bottom: 0;
    left: 0;
    opacity: 0;
    position: fixed;
    right: 0;
    top: 0;
}
.wishlist.overlay.active {
    opacity: 0.7;
    filter: alpha(opacity=70);
}
.wishlist.overlay.active {
    display: block;
}
.block-wishlist-management {
    position: relative;
}
.block-wishlist-management .wishlist-select .wishlist-name-label {
    font-weight: 600;
}
.block-wishlist-management .wishlist-title {
    margin-bottom: 10px;
}
.block-wishlist-management .wishlist-title strong {
    font-weight: 300;
    margin-right: 10px;
}
.block-wishlist-management .wishlist-notice {
    color: #7d7d7d;
    display: inline-block;
    margin-right: 20px;
}
.block-wishlist-management .wishlist-toolbar-select,
.block-wishlist-management .wishlist-toolbar-actions {
    display: inline-block;
}
.block-wishlist-management .wishlist-toolbar-select {
    margin-right: 20px;
}
.block-wishlist-management .wishlist-dropdown.move {
    margin-right: 20px;
}
.block-wishlist-search-form .form-wishlist-search {
    margin-bottom: 60px;
    max-width: 500px;
}
.block-wishlist-search-results .toolbar-wishlist-results {
    text-align: center;
}
.block-wishlist-info-items .block-title {
    margin-bottom: 20px;
    font-size: 2.2rem;
}
.block-wishlist-info-items .block-title > strong {
    font-weight: 300;
}
.block-wishlist-info-items .product-item {
    width: 100%;
}
.block-wishlist-info-items .product-item-photo {
    margin-left: 20px;
}
.block-wishlist-info-items .product-item-info {
    width: auto;
}
.block-wishlist-info-items .product-item-info:hover .split {
    visibility: visible;
}
.block-wishlist-info-items .price-wrapper {
    display: block;
    margin: 0 0 10px;
}
.block-wishlist-info-items .split {
    clear: both;
    padding: 10px 0;
}
.block-wishlist-info-items .action.towishlist {
    padding: 10px 0;
}
.block-wishlist-info-items .input-text.qty {
    margin-right: 10px;
}
.multicheckout.results h3,
.multicheckout.success h3 {
    font-size: 1.6rem;
    margin-bottom: 20px;
    margin-top: 30px;
}
.multicheckout.results h3 a,
.multicheckout.success h3 a {
    color: #333333;
}
.multicheckout.results h3 a:hover,
.multicheckout.success h3 a:hover {
    text-decoration: none;
}
.multicheckout.results ul.orders-list,
.multicheckout.success ul.orders-list {
    list-style: none;
    padding-left: 0;
}
.multicheckout.results .orders-list,
.multicheckout.success .orders-list {
    margin-top: 25px;
    padding-left: 16px;
}
.multicheckout.results .orders-list .shipping-list .shipping-item,
.multicheckout.success .orders-list .shipping-list .shipping-item {
    margin-left: 84px;
}
.multicheckout.results .orders-list .shipping-list .shipping-label,
.multicheckout.success .orders-list .shipping-list .shipping-label {
    font-weight: 700;
    margin-right: 10px;
}
.multicheckout.results .orders-list .shipping-list .shipping-address,
.multicheckout.success .orders-list .shipping-list .shipping-address {
    font-weight: 400;
}
.multicheckout.results .orders-list .shipping-list .error-block,
.multicheckout.success .orders-list .shipping-list .error-block {
    color: #e02b27;
}
.multicheckout.results .orders-list .shipping-list .error-block .error-label,
.multicheckout.success .orders-list .shipping-list .error-block .error-label {
    font-weight: 700;
    margin-right: 10px;
}
.multicheckout.results .orders-list .shipping-list .error-block .error-description,
.multicheckout.success .orders-list .shipping-list .error-block .error-description {
    font-weight: 400;
}
.multicheckout.results .orders-succeed .orders-list,
.multicheckout.success .orders-succeed .orders-list {
    margin-top: 0;
}
.multicheckout.results .orders-succeed .orders-list .shipping-list .order-id,
.multicheckout.success .orders-succeed .orders-list .shipping-list .order-id {
    float: left;
}
.multicheckout.results .orders-succeed .orders-list .shipping-list .shipping-item,
.multicheckout.success .orders-succeed .orders-list .shipping-list .shipping-item {
    margin-left: 100px;
}
.multicheckout .title {
    margin-bottom: 30px;
}
.multicheckout .title strong {
    font-weight: 400;
}
.multicheckout .table-wrapper {
    margin-bottom: 0;
}
.multicheckout .table-wrapper .action.delete {
    display: inline-block;
}
.multicheckout .table-wrapper .col .qty {
    display: inline-block;
}
.multicheckout .table-wrapper .col.item .action.edit {
    font-weight: 400;
    margin-left: 10px;
}
.multicheckout:not(.address) .table-wrapper .product-item-name {
    margin: 0;
}
.multicheckout > .actions-toolbar {
    margin-top: 40px;
}
.multicheckout .actions-toolbar > .secondary {
    display: block;
}
.multicheckout .actions-toolbar > .secondary .action {
    margin-bottom: 25px;
}
.multicheckout .actions-toolbar > .secondary .action.back {
    display: block;
    margin-left: 0;
}
.multicheckout .actions-toolbar > .primary {
    margin-right: 10px;
}
.multicheckout .item-options {
    margin: 10px 0 0;
}
.multicheckout .block .methods-shipping .item-content .fieldset > .field:before {
    display: none;
}
.multicheckout .block .methods-shipping .item-content .fieldset > .field .control {
    display: inline-block;
}
.multicheckout .block-title,
.multicheckout .block-content .title {
    border-bottom: 1px solid #cccccc;
    padding-bottom: 10px;
}
.multicheckout .block-title strong,
.multicheckout .block-content .title strong {
    font-weight: 400;
}
.multicheckout .block-title strong span,
.multicheckout .block-content .title strong span {
    color: #a6a6a6;
}
.multicheckout .block-content .title {
    border-bottom: none;
    padding-bottom: 0;
}
.multicheckout.order-review .block-title > strong {
    font-size: 2.4rem;
}
.multicheckout.order-review .block-shipping .block-content:not(:last-child) {
    margin-bottom: 40px;
}
.multicheckout.order-review .error-description {
    color: #e02b27;
    font-weight: 400;
    margin-bottom: 10px;
    margin-top: -10px;
}
.multicheckout .box-title span {
    margin-right: 10px;
}
.multicheckout .box-title > .action {
    margin: 0;
}
.multicheckout .box-shipping-method .price {
    font-weight: 700;
}
.multicheckout .box-billing-method .fieldset {
    margin: 0;
}
.multicheckout .box-billing-method .fieldset .legend.box-title {
    margin: 0 0 5px;
}
.multicheckout .checkout-review .grand.totals {
    font-size: 2.1rem;
    margin-bottom: 40px;
}
.multicheckout .checkout-review .grand.totals .mark {
    font-weight: 400;
}
[class^='multishipping-'] .logo {
    margin-left: 0;
}
.multishipping-checkout-success .nav-sections {
    display: block;
}
.block.newsletter {
    margin-bottom: 40px;
}
.block.newsletter .form.subscribe {
    display: table;
    width: 100%;
}
.block.newsletter .fieldset {
    display: table-cell;
    margin: 0;
    padding: 0;
    vertical-align: top;
}
.block.newsletter .field {
    margin: 0;
}
.block.newsletter .field .control {
    display: inline-block;
    text-decoration: none;
    display: block;
}
.block.newsletter .field .control:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 35px;
    line-height: 33px;
    color: #c2c2c2;
    content: '\e61d';
    font-family: 'luma-icons';
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.block.newsletter .field .control:before {
    position: absolute;
}
.block.newsletter input {
    padding-left: 35px;
}
.block.newsletter .title {
    display: none;
}
.block.newsletter .actions {
    display: table-cell;
    vertical-align: top;
    width: 1%;
}
.block.newsletter .action.subscribe {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
    margin-left: -1px;
}
.paypal.after:before {
    content: '- ' attr(data-label) ' -';
    display: block;
    margin: 5px 5px 10px;
    text-transform: uppercase;
}
.paypal.before:before {
    content: '- ' attr(data-label) ' -';
    display: block;
    margin: 10px 5px;
    text-align: center;
    text-transform: uppercase;
}
.paypal.acceptance {
    display: block;
    margin: 0 0 20px;
}
.paypal.acceptance img {
    max-width: 100%;
}
.box-tocart .paypal img,
.block-minicart .paypal img {
    display: block;
    margin: 0 auto;
}
.paypal-review .paypa-review-title > strong {
    font-weight: 600;
    line-height: 1.1;
    font-size: 1.8rem;
    margin-top: 2rem;
    margin-bottom: 1rem;
    display: inline-block;
}
.paypal-review .items-qty .item {
    white-space: nowrap;
}
.paypal-review .items-qty .title:after {
    content: ': ';
}
.paypal-review .paypal-review-title > strong {
    font-weight: 600;
    line-height: 1.1;
    font-size: 1.8rem;
    margin-top: 2rem;
    margin-bottom: 1rem;
    display: inline-block;
}
.paypal-review .actions-toolbar {
    margin-top: 10px;
}
.paypal-review .item-options dt {
    display: inline-block;
}
.paypal-review .item-options dt:after {
    content: ': ';
}
.paypal-review .item-options dd {
    margin: 0;
}
.paypal-review-discount {
    border-bottom: 1px solid #cccccc;
    margin: 20px 0;
}
.paypal-review-discount .block {
    margin-bottom: 0;
}
.paypal-review-discount .block .fieldset {
    padding: 10px 0 20px;
}
.fotorama-video-container:after {
    background: url(../Magento_ProductVideo/img/gallery-sprite.png) bottom right;
    bottom: 0;
    content: '';
    height: 100px;
    left: 0;
    margin: auto;
    position: absolute;
    right: 0;
    top: 12px;
    width: 100px;
}
.fotorama-video-container .magnify-lens {
    display: none !important;
}
.fotorama-video-container.video-unplayed:hover img {
    opacity: .6;
}
.fotorama-video-container.video-unplayed:hover:after {
    transform: scale(1.25);
}
.video-thumb-icon:after {
    background: url(../Magento_ProductVideo/img/gallery-sprite.png) bottom left;
    bottom: 0;
    content: '';
    height: 40px;
    left: 0;
    margin: auto;
    position: absolute;
    right: 0;
    top: 10px;
    width: 49px;
}
.product-video {
    bottom: 0;
    height: 75%;
    left: 0;
    margin: auto;
    position: absolute;
    right: 0;
    top: 0;
    width: 100%;
}
.product-video iframe {
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 9999;
}
.fotorama__stage__shaft:focus .fotorama__stage__frame.fotorama__active:after {
    bottom: 0;
    content: '';
    height: 100px;
    left: 0;
    margin: auto;
    position: absolute;
    right: 0;
    top: 12px;
    width: 100px;
}
.fotorama__product-video--loading:after {
    visibility: hidden;
}
@media only screen and (min-device-width: 320px) and (max-device-width: 780px) and (orientation: landscape) {
    .product-video {
        height: 100%;
        width: 81%;
    }
}
.rating-summary {
    overflow: hidden;
    white-space: nowrap;
}
.rating-summary .rating-result {
    width: 88px;
    display: inline-block;
    position: relative;
    vertical-align: middle;
}
.rating-summary .rating-result:before {
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 1;
    -webkit-font-smoothing: antialiased;
    color: #c7c7c7;
    font-family: 'luma-icons';
    font-size: 16px;
    height: 16px;
    letter-spacing: 2px;
    line-height: 16px;
    content: '\e605' '\e605' '\e605' '\e605' '\e605';
    display: block;
    font-style: normal;
    font-weight: normal;
    speak: none;
}
.rating-summary .rating-result > span {
    display: block;
    overflow: hidden;
}
.rating-summary .rating-result > span:before {
    position: relative;
    z-index: 2;
    -webkit-font-smoothing: antialiased;
    color: #ffd119;
    font-family: 'luma-icons';
    font-size: 16px;
    height: 16px;
    letter-spacing: 2px;
    line-height: 16px;
    content: '\e605' '\e605' '\e605' '\e605' '\e605';
    display: block;
    font-style: normal;
    font-weight: normal;
    speak: none;
}
.rating-summary .rating-result > span span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.rating-summary .rating-result {
}
.product-reviews-summary .rating-summary .label,
.table-reviews .rating-summary .label {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}

.review-control-vote {
    overflow: hidden;
}
.review-control-vote:before {
    color: #c7c7c7;
    font-family: 'luma-icons';
    font-size: 16px;
    height: 16px;
    letter-spacing: 2px;
    line-height: 16px;
    font-style: normal;
    font-weight: normal;
    speak: none;
    vertical-align: top;
    -webkit-font-smoothing: antialiased;
    content: '\e605' '\e605' '\e605' '\e605' '\e605';
    display: block;
    position: absolute;
    z-index: 1;
}
.review-control-vote input[type="radio"] {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.review-control-vote input[type="radio"]:hover + label:before,
.review-control-vote input[type="radio"]:checked + label:before {
    opacity: 1;
}
.review-control-vote label {
    cursor: pointer;
    display: block;
    position: absolute;
}
.review-control-vote label span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.review-control-vote label:before {
    color: #677752;
    font-family: 'luma-icons';
    font-size: 16px;
    height: 16px;
    letter-spacing: 2px;
    line-height: 16px;
    font-style: normal;
    font-weight: normal;
    speak: none;
    vertical-align: top;
    -webkit-font-smoothing: antialiased;
    opacity: 0;
}

.review-control-vote label:hover:before {
    opacity: 1;
}
.review-control-vote label:hover ~ label:before {
    opacity: 0;
}
.review-control-vote .rating-5 {
    z-index: 2;
}
.review-control-vote .rating-5:before {
    content: '\e605' '\e605' '\e605' '\e605' '\e605';
}
.review-control-vote .rating-4 {
    z-index: 3;
}
.review-control-vote .rating-4:before {
    content: '\e605' '\e605' '\e605' '\e605';
}
.review-control-vote .rating-3 {
    z-index: 4;
}
.review-control-vote .rating-3:before {
    content: '\e605' '\e605' '\e605';
}
.review-control-vote .rating-2 {
    z-index: 5;
}
.review-control-vote .rating-2:before {
    content: '\e605' '\e605';
}
.review-control-vote .rating-1 {
    z-index: 6;
}
.review-control-vote .rating-1:before {
    content: '\e605';
}

.block.add.review {
    margin-bottom: 40px;
}
.block.add.review > .title {
    font-weight: 300;
    line-height: 1.1;
    font-size: 2.6rem;
    margin-top: 2.5rem;
    margin-bottom: 2rem;
}
.block.add.review .legend {
    font-weight: 600;
    line-height: 1.1;
    font-size: 1.8rem;
    margin-top: 2rem;
    margin-bottom: 1rem;
    margin: 0 0 20px;
}
.block.add.review .field.rating {
    display: table-row;
}
.block.add.review .field.rating .label {
    display: table-cell;
    padding: 5px 0;
    vertical-align: middle;
}
.block.add.review .field.rating .control {
    display: table-cell;
    vertical-align: top;
}
.block.reviews.list > .title {
    font-weight: 300;
    line-height: 1.1;
    font-size: 2.6rem;
    margin-top: 2.5rem;
    margin-bottom: 2rem;
}
.block.reviews.list .review.item {
    margin-bottom: 40px;
}
.block.reviews.list .review.title {
    font-weight: 600;
    line-height: 1.1;
    font-size: 1.8rem;
    margin-top: 2rem;
    margin-bottom: 1rem;
}
.data.table.reviews .rating-summary {
    margin-top: -4px;
}
.customer-review.view {
    margin-bottom: 20px;
}
.customer-review.view .review.title,
.customer-review.view .ratings-summary,
.customer-review.view .product-details {
    margin-bottom: 20px;
}
.product-item .product-reviews-summary {
    display: table;
    margin-bottom: 0px;
    margin-top: -10px;
}
.product-info-review-container .product-reviews-summary {
    display: table;
    margin-bottom: 15px;
    margin-top: -20px;
    min-height: 5px;
}
.products.wrapper.list .product-reviews-summary {
    margin: 0 auto;
}
.product-reviews-summary.empty {
    margin-left: 0;
}
.product-reviews-summary .rating-summary {
    display: table-cell;
    position: relative;
    text-align: left;
    vertical-align: middle;
}
.product-reviews-summary .reviews-actions {
    display: table-cell;
    line-height: 16px;
    vertical-align: middle;

}
.product-reviews-summary .reviews-actions a {
    margin-left: 15px;
    margin-right: 15px;
    color: #636d70;
}
.product-reviews-summary.short .reviews-actions .action.add {
    display: none;
}
.customer-review .product-details {
    margin-bottom: 40px;
}
.customer-review .product-media {
    float: left;
    margin-right: 3%;
    max-width: 285px;
    width: 30%;
}
.customer-review .review-details .customer-review-rating {
    margin-bottom: 20px;
}
.customer-review .review-details .customer-review-rating .item {
    margin-bottom: 10px;
}
.customer-review .review-details .customer-review-rating .item:last-child {
    margin-bottom: 0;
}
.customer-review .review-details .review-title {
    line-height: 1.1;
    font-size: 1.8rem;
    margin-top: 2rem;
    margin-bottom: 1rem;
    font-weight: 600;
    margin-bottom: 20px;
}
.customer-review .review-details .review-content {
    margin-bottom: 20px;
}
.customer-review .product-reviews-summary .rating-summary,
.customer-review .product-reviews-summary .reviews-actions {
    display: block;
}
.review-field-ratings .nested {
    display: table;
}
.review-field-ratings .review-field-rating {
    display: table-row;
}
.review-field-ratings .review-field-rating .label,
.review-field-ratings .review-field-rating .control {
    display: table-cell;
    padding-bottom: 10px;
    vertical-align: top;
}
.review-field-ratings .review-field-rating .label {
    padding-right: 10px;
    padding-top: 6px;
}
.review-field-ratings .review-field-rating .control {
    padding-top: 2px;
}
.review-toolbar {
    border-top: 1px solid #c9c9c9;
    margin: 0 0 20px;
    text-align: center;
}
.review-toolbar:first-child {
    display: none;
}
.review-toolbar .pages {
    border-bottom: 1px solid #c9c9c9;
    display: block;
    padding: 20px 0;
    text-align: center;
}
.review-toolbar .limiter {
    display: none;
}
.review-add .block-title {
    display: none;
}
.review-form .action.submit.primary {
    background: #eeeeee;
    border: 1px solid #cccccc;
    color: #333333;
}
.review-form .action.submit.primary:focus,
.review-form .action.submit.primary:active {
    background: #bae588;
    border: 1px solid #cccccc;
    color: #333333;
}
.review-form .action.submit.primary:hover {
    background: #e1e1e1;
    border: 1px solid #cccccc;
    color: #333333;
}
.fieldset .review-legend.legend {
    margin-bottom: 20px;
}
.fieldset .review-legend.legend strong {
    display: block;
    margin-left: 0;
}
.review-field-rating .control {
    margin-bottom: 48px;
    margin-top: 10px;
}
.review-list {
    margin-bottom: 30px;
}
.review-list .block-title strong {
    font-weight: 300;
    line-height: 1.1;
    font-size: 2.6rem;
    margin-top: 2.5rem;
    margin-bottom: 2rem;
}
.review-item {
    border-bottom: 1px solid #c9c9c9;
    margin: 0;
    padding: 20px 0;
}
.review-item:after {
    clear: both;
    content: '';
    display: table;
}
.review-item:last-child {
    border-width: 0;
}
.review-ratings {
    display: table;
    margin-bottom: 10px;
    max-width: 100%;
}
.review-author {
    display: inline;
}
.review-title {
    font-weight: 600;
    line-height: 1.1;
    font-size: 1.8rem;
    margin-top: 2rem;
    margin-bottom: 1rem;
    margin: 0 0 20px;
}
.review-content {
    margin-bottom: 20px;
}
.block-reward-info .reward-rates:not(:last-child),
.block-reward-info .reward-limit:not(:last-child),
.block-reward-info .reward-expiring:not(:last-child) {
    margin-bottom: 25px;
}
.block-reward-info .reward-rates .title,
.block-reward-info .reward-limit .title,
.block-reward-info .reward-expiring .title {
    display: inline-block;
    margin-bottom: 10px;
}
.form-create-return .field .control > span,
.form-create-return .field address {
    display: inline-block;
    padding-top: 6px;
}
.form-create-return .additional:first-child {
    margin-top: 0;
}
.form-create-return .additional .field:nth-last-child(2) {
    margin-bottom: 0;
}
.form-create-return .additional .field:last-child {
    margin-top: 20px;
}
.form-create-return .field.comment {
    margin-top: 55px;
}
.block-returns-comments .returns-comments dt,
.block-returns-comments .returns-comments dd {
    margin: 0;
}
.block-returns-comments .returns-comments dt {
    font-weight: 400;
}
.block-returns-comments .returns-comments dd {
    margin-bottom: 15px;
}
.block-returns-comments .returns-comments dd:last-child {
    margin-bottom: 0;
}
.block-returns-comments .fieldset {
    margin-bottom: 20px;
}
/* //TODO remove later */
#tab-label-reviews-title .counter,
#product-review-container,
.product-reviews-summary.short
{
   /* display: none !important; */
}



#tab-label-reviews-title .counter:before {
    content: '(';
}
#tab-label-reviews-title .counter:after {
    content: ')';
}
.magento-rma-returns-returns .page-title-wrapper .page-title,
.magento-rma-guest-returns .page-title-wrapper .page-title,
.magento-rma-returns-view .page-title-wrapper .page-title {
    margin-right: 25px;
}
.opc-wrapper .form-discount {
    max-width: 500px;
}
.action.mailto.friend {
    margin-left: -7px;
    display: inline-block;
    text-decoration: none;
}
.action.mailto.friend:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 28px;
    line-height: 28px;
    color: inherit;
    content: '\e61d';
    font-family: 'luma-icons';
    vertical-align: top;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.action.mailto.friend span {
    display: inline-block;
    line-height: 28px;
    vertical-align: top;
}
.my-credit-cards .status {
    font-style: italic;
}
.my-credit-cards .action.delete {
    background-image: none;
    -moz-box-sizing: content-box;
    box-shadow: none;
    line-height: inherit;
    text-shadow: none;
    font-weight: 400;
    line-height: 1.42857143;
    margin: 0;
    padding: 0;
    color: #1979c3;
    text-decoration: none;
    background: none;
    border: 0;
    display: inline;
}
.my-credit-cards .action.delete:focus,
.my-credit-cards .action.delete:active {
    background: none;
    border: none;
}
.my-credit-cards .action.delete:hover {
    background: none;
    border: none;
}
.my-credit-cards .action.delete.disabled,
.my-credit-cards .action.delete[disabled],
fieldset[disabled] .my-credit-cards .action.delete {
    pointer-events: none;
    opacity: 0.5;
}
.my-credit-cards .action.delete:visited {
    color: #1979c3;
    text-decoration: none;
}
.my-credit-cards .action.delete:hover {
    color: #006bb4;
    text-decoration: underline;
}
.my-credit-cards .action.delete:active {
    color: #677752;
    text-decoration: underline;
}
.my-credit-cards .action.delete:hover {
    color: #006bb4;
}
.my-credit-cards .action.delete:hover,
.my-credit-cards .action.delete:active,
.my-credit-cards .action.delete:focus {
    background: none;
    border: 0;
}
.my-credit-cards .action.delete.disabled,
.my-credit-cards .action.delete[disabled],
fieldset[disabled] .my-credit-cards .action.delete {
    color: #1979c3;
    opacity: 0.5;
    cursor: default;
    pointer-events: none;
    text-decoration: underline;
}
.my-credit-cards-popup .modal-footer {
    text-align: right;
}
.my-credit-cards-popup .action.secondary {
    background-image: none;
    -moz-box-sizing: content-box;
    box-shadow: none;
    line-height: inherit;
    text-shadow: none;
    font-weight: 400;
    line-height: 1.42857143;
    margin: 0;
    padding: 0;
    color: #1979c3;
    text-decoration: none;
    background: none;
    border: 0;
    display: inline;
    margin-right: 20px;
}
.my-credit-cards-popup .action.secondary:focus,
.my-credit-cards-popup .action.secondary:active {
    background: none;
    border: none;
}
.my-credit-cards-popup .action.secondary:hover {
    background: none;
    border: none;
}
.my-credit-cards-popup .action.secondary.disabled,
.my-credit-cards-popup .action.secondary[disabled],
fieldset[disabled] .my-credit-cards-popup .action.secondary {
    pointer-events: none;
    opacity: 0.5;
}
.my-credit-cards-popup .action.secondary:visited {
    color: #1979c3;
    text-decoration: none;
}
.my-credit-cards-popup .action.secondary:hover {
    color: #006bb4;
    text-decoration: underline;
}
.my-credit-cards-popup .action.secondary:active {
    color: #677752;
    text-decoration: underline;
}
.my-credit-cards-popup .action.secondary:hover {
    color: #006bb4;
}
.my-credit-cards-popup .action.secondary:hover,
.my-credit-cards-popup .action.secondary:active,
.my-credit-cards-popup .action.secondary:focus {
    background: none;
    border: 0;
}
.my-credit-cards-popup .action.secondary.disabled,
.my-credit-cards-popup .action.secondary[disabled],
fieldset[disabled] .my-credit-cards-popup .action.secondary {
    color: #1979c3;
    opacity: 0.5;
    cursor: default;
    pointer-events: none;
    text-decoration: underline;
}
.minilist .weee {
    display: table-row;
    font-size: 1.1rem;
}
.minilist .weee:before {
    display: none;
}
.price-container.weee:before {
    display: none;
}
.product-items .action.towishlist:before,
.products.list.items .action.towishlist:before,
.table-comparison .action.towishlist:before {
    content: '\e601';
}
.account .table-wrapper .data.table.wishlist > thead > tr > th,
.account .table-wrapper .data.table.wishlist > tbody > tr > th,
.account .table-wrapper .data.table.wishlist > tfoot > tr > th,
.account .table-wrapper .data.table.wishlist > thead > tr > td,
.account .table-wrapper .data.table.wishlist > tbody > tr > td,
.account .table-wrapper .data.table.wishlist > tfoot > tr > td {
    border-top: 1px solid #cccccc;
}
.account .table-wrapper .data.table.wishlist > caption + thead > tr:first-child > th,
.account .table-wrapper .data.table.wishlist > colgroup + thead > tr:first-child > th,
.account .table-wrapper .data.table.wishlist > thead:first-child > tr:first-child > th,
.account .table-wrapper .data.table.wishlist > caption + thead > tr:first-child > td,
.account .table-wrapper .data.table.wishlist > colgroup + thead > tr:first-child > td,
.account .table-wrapper .data.table.wishlist > thead:first-child > tr:first-child > td {
    border-top: 0;
}
.account .table-wrapper .data.table.wishlist > tbody + tbody {
    border-top: 1px solid #cccccc;
}
.account .table-wrapper .data.table.wishlist thead > tr > th {
    border-bottom: 0;
}
.account .table-wrapper .data.table.wishlist tbody > tr:last-child > td {
    border-bottom: 1px solid #cccccc;
}
.account .table-wrapper .data.table.wishlist .product.name {
    display: inline-block;
    margin-bottom: 10px;
}
.account .table-wrapper .data.table.wishlist .box-tocart {
    margin: 10px 0;
}
.account .table-wrapper .data.table.wishlist .box-tocart .qty {
    vertical-align: middle;
}
.account .table-wrapper .data.table.wishlist .col.item {
    width: 50%;
}
.account .table-wrapper .data.table.wishlist .col.photo {
    max-width: 150px;
}
.account .table-wrapper .data.table.wishlist .col.selector {
    max-width: 15px;
}
.account .table-wrapper .data.table.wishlist textarea {
    margin: 10px 0;
}
.account .table-wrapper .data.table.wishlist .input-text.qty {
    margin-bottom: 10px;
}
.account .table-wrapper .data.table.wishlist .action.primary {
    vertical-align: top;
}
.account .table-wrapper .data.table.wishlist .price {
    font-weight: 700;
}
.block-wishlist .product-item-name {
    margin-right: 25px;
}
.products-grid.wishlist .product-item-photo {
    display: block;
    margin-bottom: 10px;
}
.products-grid.wishlist .product-item-name {
    margin-top: 0;
}
.products-grid.wishlist .product-item .price-box {
    margin: 0;
}
.products-grid.wishlist .product-item-comment {
    display: block;
    height: 42px;
    margin: 10px 0;
}
.products-grid.wishlist .product-item-actions > * {
    margin-right: 15px;
}
.products-grid.wishlist .product-item-actions > *:last-child {
    margin-right: 0;
}
.products-grid.wishlist .product-item .box-tocart input.qty {
    height: 32px;
}
.catalogsearch-advanced-result .message.error {
    margin-top: -60px;
}
.bundle-actions {
    margin: 0 0 30px;
}
.bundle-options-container {
    clear: both;
    margin-bottom: 40px;
}
.bundle-options-container .legend.title {
    font-weight: 300;
    line-height: 1.1;
    font-size: 2.6rem;
    margin-top: 0rem;
    margin-bottom: 2rem;
    border: 0;
    padding: 0;
}
.bundle-options-container .product-add-form {
    display: none;
}
.bundle-options-container .product-options-wrapper {
    margin-bottom: 30px;
}
.bundle-options-container .product-options-wrapper .fieldset > .field {
    border-top: 1px #e4e4e4 solid;
    padding-top: 20px;
}
.bundle-options-container .product-options-wrapper .fieldset > .field:first-of-type {
    border-top: 0;
    padding-top: 0;
}
.bundle-options-container .product-options-wrapper .field.choice input {
    float: left;
}
.bundle-options-container .product-options-wrapper .field.choice .label {
    display: block;
    margin-left: 24px;
}
.bundle-options-container .product-options-wrapper .field.choice .price-excluding-tax {
    display: inline-block;
}
.bundle-options-container .action.back {
    margin-bottom: 30px;
}
.bundle-options-container .block-bundle-summary > .title {
    margin-bottom: 15px;
}
.bundle-options-container .block-bundle-summary > .title > strong {
    font-weight: 300;
    line-height: 1.1;
    font-size: 2.6rem;
    margin-top: 2.5rem;
    margin-bottom: 2rem;
}
.bundle-options-container .block-bundle-summary > .title,
.bundle-options-container .block-bundle-summary .bundle-summary .subtitle {
    border-bottom: 1px #e4e4e4 solid;
    margin-bottom: 25px;
    padding-bottom: 16px;
}
.bundle-options-container .block-bundle-summary .price-box {
    margin-bottom: 20px;
}
.bundle-options-container .block-bundle-summary .price-box .price-label {
    display: block;
    margin-bottom: 5px;
}
.bundle-options-container .block-bundle-summary .bundle-summary {
    margin-top: 30px;
}
.bundle-options-container .block-bundle-summary .bundle-summary.empty {
    display: none;
}
.bundle-options-container .block-bundle-summary .bundle-summary > .subtitle {
    font-weight: 300;
    line-height: 1.1;
    font-size: 2.6rem;
    margin-top: 2.5rem;
    margin-bottom: 2rem;
    display: block;
}
.bundle-options-container .block-bundle-summary .bundle.items > li {
    margin-bottom: 20px;
}
.bundle-options-container .block-bundle-summary .product-addto-links {
    text-align: center;
}
.bundle-options-container .block-bundle-summary .product-addto-links > .action {
    margin-right: 5%;
}
.bundle-options-container .block-bundle-summary .product-addto-links > .action.tocompare:before {
    content: '\e61e';
}
.bundle-options-container p.required {
    color: #e02b27;
}
.bundle-options-container .nested .field.qty {
    margin: 0 0 20px;
    margin-top: 20px;
}
.bundle-options-container .nested .field.qty > .label {
    margin: 0 0 8px;
    display: inline-block;
}
.bundle-options-container .nested .field.qty:last-child {
    margin-bottom: 0;
}
.bundle-options-container .price {
    font-weight: 600;
}
.bundle-options-container .product-options-bottom {
    border-top: 1px solid #cccccc;
    clear: left;
    margin: 0 0 40px;
    padding-top: 20px;
}
.products {
    margin: 0 0 30px 0;
}
.product-item {
    vertical-align: top;
}
.products-grid .product-item {
    display: inline-block;
    width: 50%;
}
.product-item-name {
    -moz-hyphens: auto;
    -ms-hyphens: auto;
    -webkit-hyphens: auto;
    display: block;
    hyphens: auto;
    margin: 5px 0;
    word-wrap: break-word;
}
.product-item-info {
    max-width: 100%;
    width: 152px;
}
.page-products .product-item-info {
    width: 240px;
}
.product-item-actions {
}
.product-item-actions .actions-secondary > .action:before {
    margin: 0;
}
.product-item-description {
    margin: 25px 0;
}
.product-item .product-reviews-summary .rating-summary {
    margin: 0 4px 0 0;
}
.product-item .product-reviews-summary .reviews-actions {
    font-size: 12px;
    margin-top: 5px;
}
.product-item .price-box {
    margin: 10px 0 25px;
}
.product-item .price-box .price {
    font-weight: 700;
    white-space: nowrap;
}
.product-item .price-box .price-label {
    color: #7d7d7d;
    font-size: 12px;
}
.product-item .old-price {
    margin: 5px 0;
}
.product-item .old-price .price {
    font-weight: normal;
}
.product-item .minimal-price .price-container {
    display: block;
}
.product-item .minimal-price-link {
    margin-top: 5px;
}
.product-item .price-from,
.product-item .price-to {
    margin: 0;
}
.product-item .tocompare:before {
    content: '\e61e';
}
.product-item .tocart {
    border-radius: 0;
    line-height: 1;
    padding-bottom: 10px;
    padding-top: 10px;
    white-space: nowrap;
}
.column.main .product-items {
    margin-left: -20px;
}
.column.main .product-item {
    padding-left: 20px;
}
.price-container .price {
    font-size: 1.4rem;
}
.price-container .price-including-tax + .price-excluding-tax,
.price-container .weee {
    margin-top: 5px;
}
.price-container .price-including-tax + .price-excluding-tax,
.price-container .weee,
.price-container .price-including-tax + .price-excluding-tax .price,
.price-container .weee .price,
.price-container .weee + .price-excluding-tax:before,
.price-container .weee + .price-excluding-tax .price {
    font-size: 1.1rem;
}
.price-container .weee:before {
    content: '(' attr(data-label) ': ';
}
.price-container .weee:after {
    content: ')';
}
.price-container .weee + .price-excluding-tax:before {
    content: attr(data-label) ': ';
}
.products-list .product-item {
    display: table;
    width: 100%;
}
.products-list .product-item-info {
    display: table-row;
}
.products-list .product-item-photo {
    display: table-cell;
    padding: 0 30px 30px 0;
    vertical-align: top;
    width: 1%;
}
.products-list .product-item-details {
    display: table-cell;
    vertical-align: top;
}
.page-products .columns {
    padding-top: 60px;
    position: relative;
    z-index: 1;
}
.toolbar {
    margin-bottom: 30px;
    text-align: center;
}
.toolbar select {
    background-color: #f0f0f0;
    box-shadow: inset 0 1px 0 0 #ffffff, inset 0 -1px 0 0 rgba(204, 204, 204, 0.3);
    border-radius: 3px;
}
.toolbar-amount {
    left: 0;
    line-height: 18px;
    margin: 0;
    padding: 7px 0;
    position: absolute;
    text-align: left;
    top: 0;
    vertical-align: middle;
}
.products.wrapper ~ .toolbar .toolbar-amount {
    display: none;
}
.page-with-filter .toolbar-amount {
    position: static;
}
.toolbar-products {
    background-color: transparent;
}
.products.wrapper ~ .toolbar-products .pages {
    display: block;
}
.toolbar .pages {
    margin-bottom: 25px;
}
.sorter {
    float: right;
}
.page-products .sorter {
    position: absolute;
    right: 10px;
    top: 0;
    z-index: 1;
}
.products.wrapper ~ .toolbar .sorter {
    display: none;
}
.sorter-options {
    margin: 0 5px 0 7px;
    width: auto;
}
.sorter-action {
    display: inline-block;
    text-decoration: none;
}
.sorter-action:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 16px;
    line-height: inherit;
    color: #8f8f8f;
    content: '\e613';
    font-family: 'luma-icons';
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.sorter-action:hover:before {
    color: #333333;
}
.sorter-action > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.sorter-action.sort-desc:before {
    content: '\e614';
}
.modes {
    display: none;
}
.limiter {
    display: none;
}
.limiter-options {
    margin: 0 5px 0 7px;
    width: auto;
}
.limiter-label {
    font-weight: 400;
}
.page-products .toolbar .limiter {
    display: none;
}
.limiter .control {
    display: inline-block;
}
.old-price,
.old.price {
    color: #7d7d7d;
}
.prices-tier .price-container .price-including-tax + .price-excluding-tax:before {
    content: '(' attr(data-label) ':';
}
.prices-tier .price-container .price-including-tax + .price-excluding-tax:last-child:after {
    content: ')';
}
.prices-tier .price-container .weee[data-label] {
    display: inline;
}
.prices-tier .price-container .weee[data-label]:before {
    content: ' +' attr(data-label) ':';
}
.actual-price {
    font-weight: 700;
}
.category-image .image {
    display: block;
    height: auto;
    max-width: 100%;
}
.category-image,
.category-description {
    margin-bottom: 10px;
}
.product-image-container {
    display: inline-block;
    max-width: 100%;
}
.product-image-wrapper {
    display: block;
    overflow: hidden;
    position: relative;
    z-index: 1;
}
.product-image-photo {
    bottom: 0;
    display: block;
    height: auto;
    left: 0;
    margin: auto;
    max-width: 100%;
    right: 0;
    top: 0;
}
.product.media .placeholder .photo.container {
    max-width: 100%;
}
.product.media .notice {
    margin: 10px 0;
    color: #7d7d7d;
    font-size: 1.2rem;
}
.product.media .product.thumbs {
    margin: 10px 0 25px;
}
.product.media .items.thumbs {
    margin: 0;
    padding: 0;
    list-style: none none;
}
.product.media .items.thumbs > li {
    display: inline-block;
    vertical-align: top;
}
.product.media .items.thumbs > li {
    margin: 1rem 0 0;
}
.product.media .items.thumbs img {
    display: block;
}
.product.media .items.thumbs .active {
    display: block;
    line-height: 1;
}
.product.info.detailed {
    clear: both;
    margin-bottom: 30px;
}
.product.info.detailed .additional-attributes {
    width: auto;
    border: none;
}
.product.info.detailed .additional-attributes > thead > tr > th,
.product.info.detailed .additional-attributes > tbody > tr > th,
.product.info.detailed .additional-attributes > tfoot > tr > th,
.product.info.detailed .additional-attributes > thead > tr > td,
.product.info.detailed .additional-attributes > tbody > tr > td,
.product.info.detailed .additional-attributes > tfoot > tr > td {
    border: none;
}
.product.info.detailed .additional-attributes > thead > tr > td,
.product.info.detailed .additional-attributes > tbody > tr > td,
.product.info.detailed .additional-attributes > tfoot > tr > td {
    padding: 5.5px 5px 10px 5px;
}
.product.info.detailed .additional-attributes > thead > tr > th,
.product.info.detailed .additional-attributes > tbody > tr > th,
.product.info.detailed .additional-attributes > tfoot > tr > th {
    padding: 5.5px 30px 10px 0;
}
.product-info-main .product-info-price {
    color: #575757;
    border-bottom: 1px solid #f5f5f5;
    display: table;
    margin-bottom: 15px;
    width: 100%;
}
.product-info-main .product-info-price .price-box {
    display: table-cell;
    vertical-align: top;
    width: 1px;
}
.product-info-main .product-info-price .price-box .price-container > span {
    margin-bottom: 5px;
}
.product-info-main .product-info-price .price-box .price-container > span:last-child {
    margin-bottom: 0;
}
.product-info-main .product-info-price .price-including-tax + .price-excluding-tax,
.product-info-main .product-info-price .weee + .price-excluding-tax,
.product-info-main .product-info-price .weee {
    font-size: 1.4rem;
}
.product-info-main .product-info-price .price-including-tax + .price-excluding-tax .price,
.product-info-main .product-info-price .weee + .price-excluding-tax .price,
.product-info-main .product-info-price .weee .price {
    font-size: 1.4rem;
    font-weight: 600;
    line-height: 16px;
}
.product-info-main .product-info-price .price-wrapper .price {
    font-size: 2.2rem;
    font-weight: 600;
    line-height: 22px;
}
.product-info-main .product-info-price .old-price .price-wrapper .price {
    font-size: 2rem;
    font-weight: 300;
}
.product-info-main .product-info-price .special-price .price-label:after {
    content: ': ';
}
.product-info-main .product-info-price .price {
    white-space: nowrap;
}
.product-info-main .product-info-stock-sku {
    display: table-cell;
    padding-bottom: 10px;
    padding-left: 10%;
    text-align: right;
    vertical-align: top;
}
.product-info-main .product.attribute.sku {
    word-break: break-all;
    word-wrap: break-word;
}
.product-info-main .product-add-form {
    clear: both;
    padding-top: 15px;
}
.product-info-main .product.attribute.sku .type {
    font-weight: normal;
    margin-right: 5px;
}
.product-info-main .product.attribute.sku .type:after {
    content: '#:';
}
.product-info-main .product.attribute.sku .value {
    display: inline-block;
}
.product-info-main .product.attribute.overview {
    margin: 20px 0;
}
.product-info-main .product.alert {
    margin: 10px 0;
}
.product-info-main .product-reviews-summary {
    /*float: left;*/
}
.product-info-main .product-options-bottom .box-tocart {
    margin-top: 20px;
}
.product-options-wrapper .fieldset-product-options-inner .legend {
    font-weight: 600;
    font-size: 1.4rem;
    border: none;
    display: inline-block;
    float: none;
    margin: 0 0 8px;
    padding: 0;
}
.product-options-wrapper .fieldset-product-options-inner.required .legend:after,
.product-options-wrapper .fieldset-product-options-inner._required .legend:after {
    content: '*';
    color: #e02b27;
    font-size: 1.2rem;
}
.product-options-wrapper .field .note {
    display: block;
    margin-top: 5px;
}
.product-options-bottom .price-box,
.product-info-price .price-box {
    color: #575757;
    display: table-cell;
    padding-bottom: 10px;
    vertical-align: top;
}
.product-options-bottom .price-box .old-price,
.product-info-price .price-box .old-price {
    font-size: 20px;
    font-weight: 300;
}
.product-options-bottom .price-box .old-price .price-container > span,
.product-info-price .price-box .old-price .price-container > span {
    display: inline-block;
}
.product-options-bottom .price-box .price-container > span,
.product-info-price .price-box .price-container > span {
    margin-bottom: 5px;
}
.product-options-bottom .price-box .price-container > span:last-child,
.product-info-price .price-box .price-container > span:last-child {
    margin-bottom: 0;
}
.product-options-bottom .price-box .price-container .price,
.product-info-price .price-box .price-container .price {
    font-size: 22px;
    font-weight: 600;
    line-height: 22px;
}
.product-options-bottom .price-box .price-container .price-including-tax + .price-excluding-tax,
.product-info-price .price-box .price-container .price-including-tax + .price-excluding-tax,
.product-options-bottom .price-box .price-container .weee + .price-excluding-tax,
.product-info-price .price-box .price-container .weee + .price-excluding-tax,
.product-options-bottom .price-box .price-container .weee,
.product-info-price .price-box .price-container .weee {
    font-size: 1.2rem;
    line-height: 14px;
}
.product-options-bottom .price-box .price-container .price-including-tax + .price-excluding-tax .price,
.product-info-price .price-box .price-container .price-including-tax + .price-excluding-tax .price,
.product-options-bottom .price-box .price-container .weee + .price-excluding-tax .price,
.product-info-price .price-box .price-container .weee + .price-excluding-tax .price,
.product-options-bottom .price-box .price-container .weee .price,
.product-info-price .price-box .price-container .weee .price {
    font-size: 1.2rem;
    font-weight: 700;
}
.product-addto-links {
    display: inline;
}
.product-addto-links .action {
    margin-right: 5%;
}
.product-social-links {
    margin: 0 0 20px;
    text-align: center;
}
.product-social-links .action.tocompare:before {
    content: '\e61e';
}
.prices-tier {
    margin-bottom: 15px;
}
.prices-tier .item {
    margin-bottom: 10px;
}
.prices-tier .item:last-child {
    margin-bottom: 0;
}
.prices-tier .price-excluding-tax,
.prices-tier .price-including-tax {
    display: inline-block;
}
.minimal-price-link,
.price-excluding-tax,
.price-including-tax {
    display: block;
    white-space: nowrap;
}
.ui-dialog-titlebar-close {
    line-height: 1.42857143;
    margin: 0;
    padding: 0;
    color: #1979c3;
    text-decoration: none;
    background: none;
    border: 0;
    display: inline;
}
.ui-dialog-titlebar-close:visited {
    color: #1979c3;
    text-decoration: none;
}
.ui-dialog-titlebar-close:hover {
    color: #006bb4;
    text-decoration: underline;
}
.ui-dialog-titlebar-close:active {
    color: #677752;
    text-decoration: underline;
}
.ui-dialog-titlebar-close:hover {
    color: #006bb4;
}
.ui-dialog-titlebar-close:hover,
.ui-dialog-titlebar-close:active,
.ui-dialog-titlebar-close:focus {
    background: none;
    border: 0;
}
.ui-dialog-titlebar-close.disabled,
.ui-dialog-titlebar-close[disabled],
fieldset[disabled] .ui-dialog-titlebar-close {
    color: #1979c3;
    opacity: 0.5;
    cursor: default;
    pointer-events: none;
    text-decoration: underline;
}
.sidebar .product-items .product-item {
    margin-bottom: 20px;
    position: relative;
}
.sidebar .product-items .product-item-info {
    position: relative;
    width: auto;
}
.sidebar .product-items .product-item-info .product-item-photo {
    left: 0;
    position: absolute;
    top: 0;
}
.sidebar .product-items .product-item-name {
    margin-top: 0;
}
.sidebar .product-items .product-item-details {
    margin: 0 0 0 85px;
}
.sidebar .product-items .product-item-actions {
    display: block;
    margin-top: 10px;
}
.sidebar .product-items .price-box {
    display: block;
    margin: 7px 0;
}
.sidebar .product-items .text {
    margin-right: 8px;
}
.sidebar .product-items .counter {
    color: #7d7d7d;
    font-size: 12px;
    white-space: nowrap;
}
.sidebar .product-items .minilist .price {
    display: inline;
    padding: 0;
}
.sidebar .product-items .minilist .weee:before {
    display: inline-block;
}
.sidebar .product-items .action.delete {
    position: absolute;
    right: 0;
    top: 0;
}
.sidebar .action.tocart {
    border-radius: 0;
}
.sidebar .product-items-images {
    margin-left: -5px;
}
.sidebar .product-items-images .product-item {
    float: left;
    padding-left: 5px;
}
.sidebar .product-items-names .product-item {
    margin-bottom: 10px;
}
.sidebar .product-items-names .product-item-name {
    margin: 0;
}
body.catalog-product-compare-index .action.print {
    float: right;
    margin: 15px 0;
}
.table-wrapper.comparison {
    clear: both;
    max-width: 100%;
    overflow-x: auto;
}
.table-wrapper.comparison .table-comparison > tbody > tr > th,
.table-wrapper.comparison .table-comparison > tbody > tr > td {
    border-top: 0;
}
.table-comparison {
    table-layout: fixed;
}
.table-comparison .cell.label{
    border-right: 1px solid #cccccc;
}
.table-comparison .cell {
    padding: 15px;
    width: 180px;
}
.table-comparison .cell .attribute.value {
    overflow: hidden;
    width: 100%;
}
.table-comparison .cell.product.info,
.table-comparison .cell.product.label {
    border-bottom: 1px solid #cccccc;
}
.table-comparison .cell.label .attribute.label {
    display: block;
    width: 100%;
    word-wrap: break-word;
}
.table-comparison .cell.attribute {
}
.table-comparison .cell.attribute img {
    height: auto;
    max-width: 100%;
}
.table-comparison .cell.remove {
    padding-bottom: 0;
    padding-top: 0;
    text-align: right;
}
.table-comparison .cell.remove .action.delete {
    margin-right: .6rem;
}
.table-comparison .product-item-photo {
    display: block;
    margin: 0 auto 15px;
}
.table-comparison .product-image-photo {
    margin-left: 0;
}
.table-comparison .product-item-actions,
.table-comparison .price-box,
.table-comparison .product.rating,
.table-comparison .product-item-name {
    display: block;
    margin: 15px 0;
}
.table-comparison .product-addto-links {
    margin-top: 15px;
}
.table-comparison .product-addto-links .action.split,
.table-comparison .product-addto-links .action.toggle {
    line-height: 1.2rem;
    padding: 5px 8px;
    font-size: 1.1rem;
}
.table-comparison .product-addto-links .action.toggle {
    padding: 0;
}
.table-comparison .action.tocart {
    white-space: nowrap;
}
.comparison.headings {
    background: #ffffff;
    left: 0;
    position: absolute;
    top: 0;
    width: auto;
    z-index: 2;
}
.block-compare .product-item .product-item-name {
    margin-left: 22px;
}
.block-compare .action.delete {
    left: 0;
    position: absolute;
    top: 0;
}
.block-compare .actions-toolbar {
    margin: 17px 0 0;
}
.block.related .action.select {
    vertical-align: top;
}
.block.related .product-item-details {
    position: relative;
    z-index: 1;
}
.block.related .related-available .product-item-name {
    margin-left: 20px;
}
.block.related .field.choice {
    left: 0;
    position: absolute;
    top: 2px;
}
.block-search {
    margin-bottom: 0;
}
.block-search .block-title {
    display: none;
}
.block-search .block-content {
    margin-bottom: 0;
}
.block-search .label {
    text-decoration: none;
    display: inline-block;
    float: right;
}
.block-search .label > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.block-search .label:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 22px;
    line-height: 28px;
    color: #ffffff;
    content: '\e615';
    font-family: 'luma-icons';
    margin: 0 10px 0 0;
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.block-search .label:hover:before {
    color: #2f3943;
}
.block-search .label:active:before {
    color: #2f3943;
}
.block-search .label.active + .control input {
    position: static;
}
.block-search .action.search {
    display: none;
}
.block-search .control {
    border-top: 1px solid #cccccc;
    clear: both;
    margin: 0 -15px -1px;
    padding: 0 15px;
}
.block-search input {
    left: -300%;
    margin: 15px 0;
    position: absolute;
}
.block-search .nested {
    display: none;
}
.search-autocomplete {
    display: none;
    margin-top: -15px;
    overflow: hidden;
    position: absolute;
    z-index: 3;
}
.search-autocomplete ul {
    margin: 0;
    padding: 0;
    list-style: none none;
}
.search-autocomplete ul:not(:empty) {
    background: #ffffff;
    border: 1px solid #c2c2c2;
    border-top: 0;
}
.search-autocomplete ul li {
    border-top: 1px solid #e5e5e5;
    cursor: pointer;
    margin: 0;
    padding: 5px 40px 5px 10px;
    position: relative;
    text-align: left;
    white-space: normal;
}
.search-autocomplete ul li:first-child {
    border-top: none;
}
.search-autocomplete ul li:hover,
.search-autocomplete ul li.selected {
    background: #e8e8e8;
}
.search-autocomplete ul li .amount {
    color: #999999;
    position: absolute;
    right: 7px;
    top: 5px;
}
.form.search.advanced .fields.range .field:first-child {
    position: relative;
}
.form.search.advanced .fields.range .field:first-child .control {
    padding-right: 25px;
}
.form.search.advanced .fields.range .field:first-child .control:after {
    content: ' \2013 ';
    display: inline-block;
    position: absolute;
    right: 0;
    text-align: center;
    top: 6px;
    width: 25px;
}
.form.search.advanced .fields.range .field:last-child {
    position: relative;
}
.form.search.advanced .fields.range .field:last-child div.mage-error[generated] {
    left: 0;
    position: absolute;
    top: 32px;
}
.form.search.advanced .fields.range .field.with-addon .control {
    padding-right: 45px;
}
.form.search.advanced .group.price .addon .addafter {
    background: none;
    border: 0;
    padding-top: 6px;
    position: absolute;
    right: 0;
    top: 0;
}
.search-terms {
    line-height: 2em;
}
.search-terms > li {
    display: inline-block;
    margin-right: 10px;
}
.search.found {
    margin-bottom: 10px;
}
.checkout-cart-index .page-main {
    padding-left: 0;
    padding-right: 0;
}
.checkout-cart-index .page-title-wrapper {
    padding-left: 15px;
    padding-right: 15px;
}
.cart-summary {
    background: #f5f5f5;
    margin-bottom: 35px;
    padding: 1px 0 25px;
}
.cart-summary > .title {
    font-size: 18px;
    font-weight: 500;
    margin: 8px 0;
}
.cart-summary .block form:not(:last-of-type) .fieldset {
    margin: 0 0 25px;
}
.cart-summary .block .price {
    font-weight: 700;
}
.cart-summary .block .field {
    margin: 0 0 16px;
}
.cart-summary .block .field.note {
    display: none;
}
.cart-summary .block .actions-toolbar > .primary {
    text-align: left;
}
.cart-summary .block .actions-toolbar > .primary .action.primary {
    width: auto;
}
.cart-summary .block .title {
    font-size: 1.4rem;
    font-weight: 500;
}
.cart-summary .block .item-options {
    margin: 0 0 16px;
}
.cart-summary .block .item-options .field .radio {
    float: left;
}
.cart-summary .block .item-options .field .radio + .label {
    display: block;
    margin: 0;
    overflow: hidden;
}
.page-main .cart-summary .block {
    margin-bottom: 0;
}
.cart-summary .checkout-methods-items {
    margin: 20px 0 0;
    padding: 0 15px;
    text-align: center;
}
.cart-summary .checkout-methods-items .action.primary.checkout {
    width: 100%;
}
.cart-summary .checkout-methods-items .item {
    margin-bottom: 25px;
}
.cart-summary .checkout-methods-items .item:last-child {
    margin-bottom: 0;
}
.cart-summary .message {
    padding-left: 20px;
}
.cart-summary .message > *:first-child:before {
    display: none;
}
.cart-totals tbody .mark,
.cart-totals tfoot .mark {
    text-align: left;
}
.cart-totals .totals-tax-details {
    display: table-row;
}
.cart.table-wrapper .cart thead tr th.col {
    border-bottom: 1px solid #cccccc;
    font-weight: 500;
    font-size: 18px;
}
.cart.table-wrapper .cart tbody td {
    border: 0;
}
.cart.table-wrapper .cart > .item {
    border-bottom: 1px solid #cccccc;
    position: relative;
}
.cart.table-wrapper .col {
    padding-top: 10px;
}
.cart.table-wrapper .col.price,
.cart.table-wrapper .col.subtotal,
.cart.table-wrapper .col.msrp {
    padding: 10px 11px 10px;
    text-align: center;
}
.cart.table-wrapper .col.qty {
    padding: 10px 11px 10px;
    text-align: center;
}
.cart.table-wrapper .col.qty .input-text {
    height: 36px;
    margin-top: 0;
    text-align: center;
    width: 45px;
}
.cart.table-wrapper .col > .price {
    color: #7d7d7d;
    font-size: 1.8rem;
    font-weight: 700;
}
.cart.table-wrapper .item-actions td {
    padding-bottom: 0;
    padding-left: 15px;
    padding-right: 15px;
    white-space: normal;
}
.cart.table-wrapper .item .col.item {
    position: relative;
}
.cart.table-wrapper .item .col.price .cart-price,
.cart.table-wrapper .item .col.subtotal .cart-price,
.cart.table-wrapper .item .col.price .old-price,
.cart.table-wrapper .item .col.subtotal .old-price,
.cart.table-wrapper .item .col.price .save-info-block,
.cart.table-wrapper .item .col.subtotal .save-info-block {
    display: block;
}
.cart.table-wrapper .item .col.subtotal {

}
.cart.table-wrapper .item .col.subtotal .save-info-block {
    position: absolute;
    right: 10px;
    margin-top: 10px;
    color: #677752;
    text-align: right;
}
.cart.table-wrapper .actions-toolbar {
    min-height: 20px;
    padding-bottom: 15px;
    position: relative;
}
.cart.table-wrapper .action {
    margin-right: 0;
    display: block;
}
.cart.table-wrapper .action:last-child {
    margin-right: 0;
}
.cart.table-wrapper .action:before {
    font-family: 'luma-icons';
    color: inherit;
    border-bottom: none;
    display: inline-block;
    width: auto;
    position: inherit;
    left: auto;
    bottom: auto;
    margin-right: 5px;
}
.cart.table-wrapper .action.action-edit:before {
    content: '\e601';
}
.cart.table-wrapper .action.help.map {
    font-weight: 400;
}
.cart.table-wrapper .product-item-photo {
    display: block;
    left: 15px;
    max-width: 65px;
    padding: 0;
    position: absolute;
    top: 15px;
    width: 100%;
}
.cart.table-wrapper .product-item-name {
    font-size: 1.8rem;
    display: block;
    margin: -3px 0 5px;
}
.cart.table-wrapper .gift-registry-name-label:after {
    content: ':';
}
.cart.table-wrapper .item-options {
    margin-bottom: 0;
}

.cart.table-wrapper .action.configure {
    display: inline-block;
    margin: 10px 0 0;
}
.cart.table-wrapper .item .message {
    margin-top: 20px;
}
.cart-products-toolbar .toolbar-amount {
    left: inherit;
    margin: 25px 0 15px;
    padding: 0;
    position: relative;
    text-align: center;
    top: inherit;
}
.cart-products-toolbar-top {
    border-bottom: 1px solid #cccccc;
}
.cart-discount {
    border-bottom: 1px solid #cccccc;
    clear: left;
}
.cart-empty {
    padding-left: 15px;
    padding-right: 15px;
}
.cart-tax-info + .cart-tax-total,
.cart .cart-tax-info + .cart-tax-total {
    display: block;
}
.cart.table-wrapper .col.price,
.order-items.table-wrapper .col.price,
.cart.table-wrapper .col.qty,
.order-items.table-wrapper .col.qty,
.cart.table-wrapper .col.subtotal,
.order-items.table-wrapper .col.subtotal,
.cart.table-wrapper .col.msrp,
.order-items.table-wrapper .col.msrp {
    text-align: right;
}
.checkout-shipping-additional {
    margin-bottom: 20px;
}
.checkout-shipping-additional .shipping-additional-info-block {
    padding-top: 0;
    padding-bottom: 0;
    max-width: 1570px;
    margin: 0 auto;
}
.checkout-shipping-additional .shipping-additional-info-block .delivery-info-block {
    display: flex;
    margin-bottom: 20px;
}
.checkout-shipping-additional .shipping-additional-info-block .delivery-info-block .delivery-date-field,
.checkout-shipping-additional .shipping-additional-info-block .delivery-info-block .delivery-comment,
.checkout-shipping-additional .shipping-additional-info-block .delivery-info-block .delivery-date-cms-block {
    background: #FFF;
    border: none;
    flex: 0 0 auto;
    padding-left: 15px;
    max-width: 100%;
    box-shadow: 0 0 15px 0 rgba(0,0,0,0.1);
}
.checkout-shipping-additional .shipping-additional-info-block .delivery-info-block .delivery-date-field {
    position: relative;
}
.checkout-shipping-additional .shipping-additional-info-block .delivery-info-block .delivery-date-field .delivery-date-info {
    margin-bottom: 20px;
}
.checkout-shipping-additional .shipping-additional-info-block .delivery-info-block .delivery-date-cms-block h4 {
    margin-top: 20px;
    margin-left: 20px;
}
.checkout-shipping-additional .shipping-additional-info-block .delivery-info-block .delivery-date-cms-block ul {
    padding-left: 43px;
}
.checkout-shipping-additional .shipping-additional-info-block .delivery-info-block .delivery-date-cms-block .secure-shopping-info td:last-child {
    position: relative;
    width: 100%;
    overflow: hidden;
}
.checkout-shipping-additional .shipping-additional-info-block .delivery-info-block .delivery-date-cms-block .secure-shopping-info td:last-child img {
    position: absolute;
    top: 0;
    right: 0;
    height: auto;
    width: 100%;
}
.checkout-shipping-additional .shipping-additional-info-block .delivery-comment label {
    display: none;
}
.checkout-shipping-additional .shipping-additional-info-block .delivery-comment #delivery_comment {
    max-width: calc(100% - 15px);
}
.checkout-shipping-additional .shipping-additional-info-block .actions-toolbar {
    text-align: right;
    float: right;
}
.checkout-shipping-additional .shipping-additional-info-block .actions-toolbar button {
    margin-right: 0;
}
.checkout-shipping-additional .shipping-additional-info-block .actions-toolbar:after {
    clear: both;
    content: "";
    display: table;
}
.checkout-shipping-additional:after {
    clear: both;
    content: "";
    display: table;
}
.cart-gift-container {
    display: none;
}
.cart-gift-container .block.gift {
    float: none;
}
.cart-gift-container .gift-info-block {
    margin-top: 10px;
}
.cart-gift-container .gift-info-block h2 {
    margin-top: 10px;
}
.block.crosssell {
    padding: 0 15px;
    margin-top: 20px;
}
.block-minicart .items-total {
    float: left;
    margin: 0 10px;
    display: none;
}
.block-minicart .items-total .count {
    font-weight: 700;
}
.block-minicart .subtotal {
    margin: 10px 0;
    text-align: right;
}
.block-minicart .subtotal span.label, .block-minicart .subtotal .amount { display: inline-block; }

.block-minicart .amount .price-wrapper:first-child .price {
    font-size: 18px;
    font-weight: 700;
}
.block-minicart .subtitle {
    display: none;
}
.block-minicart .subtitle.empty {
    display: block;
    font-size: 14px;
    padding: 30px 0 20px;
    text-align: center;
}
.block-minicart .text.empty {
    text-align: center;
}
.block-minicart .block-content > .actions {
    margin-top: 15px;
}
.block-minicart .block-content > .actions > .secondary {
    text-align: center;
}
.block-minicart .block-content > .actions > .primary {
    margin: 0 10px 15px;
}
.block-minicart .block-content > .actions > .primary .action.primary {
    display: block;
    width: 100%;
}
.block-minicart .block-content > .actions .paypal-logo {
    margin-top: 15px;
    text-align: center;
    display: none;
}
.block-minicart .block-category-link,
.block-minicart .block-product-link,
.block-minicart .block-cms-link,
.block-minicart .block-banners {
    margin: 15px 0 0;
    text-align: center;
}
.minicart-wrapper {
    display: inline-block;
    position: relative;
    float: right;
}
.minicart-wrapper:before,
.minicart-wrapper:after {
    content: '';
    display: table;
}
.minicart-wrapper:after {
    clear: both;
}
.minicart-wrapper .action.showcart {
    cursor: pointer;
    display: inline-block;
    text-decoration: none;
}
.minicart-wrapper .action.showcart:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 22px;
    line-height: 28px;
    color: #ffffff;
    content: '\e611';
    font-family: 'luma-icons';
    margin: 0;
    vertical-align: top;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.minicart-wrapper .action.showcart:hover:before {
    color: #2f3943;
}
.minicart-wrapper .action.showcart:active:before {
    color: #ffffff;
}
.minicart-wrapper .action.showcart.active {
    display: inline-block;
    text-decoration: none;
}
.minicart-wrapper .action.showcart.active:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 22px;
    line-height: 28px;
    color: #ffffff;
    content: '\e611';
    font-family: 'luma-icons';
    margin: 0;
    vertical-align: top;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.minicart-wrapper .action.showcart.active:hover:before {
    color: #2f3943;
}
.minicart-wrapper .action.showcart.active:active:before {
    color: #ffffff;
}
.minicart-wrapper .block-minicart {
    margin: 0;
    padding: 0;
    list-style: none none;
    background: #ffffff;
    border: 1px solid #bbbbbb;
    z-index: 1000;
    box-sizing: border-box;
    display: none;
    position: absolute;
    top: auto;
    margin-top: 20px;
    box-shadow: 0 3px 3px rgba(0, 0, 0, 0.15);
}
.minicart-wrapper .block-minicart li {
    margin: 0;
    position: relative;
}
.minicart-wrapper .block-minicart li:hover {
    cursor: pointer;
}
.minicart-wrapper .block-minicart:before,
.minicart-wrapper .block-minicart:after {
    border-bottom-style: solid;
    content: '';
    display: block;
    height: 0;
    position: absolute;
    width: 0;
}
.minicart-wrapper .block-minicart:before {
    border: 6px solid;
    border-color: transparent transparent #ffffff transparent;
    z-index: 99;
}
.minicart-wrapper .block-minicart:after {
    border: 7px solid;
    border-color: transparent transparent #bbbbbb transparent;
    z-index: 98;
}
.minicart-wrapper .block-minicart:before {
    left: 10px;
    top: -12px;
}
.minicart-wrapper .block-minicart:after {
    left: 9px;
    top: -14px;
}
.minicart-wrapper.active {
    overflow: visible;
}
.minicart-wrapper.active .block-minicart {
    display: block;
}
.minicart-wrapper .block-minicart {
    padding: 20px;
    right: 0;
    width: 360px;
}
.minicart-wrapper .block-minicart .block-title {
    display: none;
}
.minicart-wrapper .block-minicart:after {
    left: auto;
    right: 31px;
}
.minicart-wrapper .block-minicart:before {
    left: auto;
    right: 32px;
}
.minicart-wrapper .product .actions a::before {
    width: 20px;
    height: 20px;
}
.minicart-wrapper .product .actions > .primary,
.minicart-wrapper .product .actions > .secondary {
    display: inline;
}
.minicart-wrapper .product .actions > .primary:not(:last-child),
.minicart-wrapper .product .actions > .secondary:not(:last-child) {
    margin-right: 15px;
}
.minicart-wrapper .action.close {
    height: 40px;
    position: absolute;
    right: 0;
    top: 0;
    width: 40px;
    background-image: none;
    background: none;
    -moz-box-sizing: content-box;
    border: 0;
    box-shadow: none;
    line-height: inherit;
    margin: 0;
    padding: 0;
    text-shadow: none;
    font-weight: 400;
    text-decoration: none;
    display: none;
}
.minicart-wrapper .action.close:focus,
.minicart-wrapper .action.close:active {
    background: none;
    border: none;
}
.minicart-wrapper .action.close:hover {
    background: none;
    border: none;
}
.minicart-wrapper .action.close.disabled,
.minicart-wrapper .action.close[disabled],
fieldset[disabled] .minicart-wrapper .action.close {
    pointer-events: none;
    opacity: 0.5;
}
.minicart-wrapper .action.close > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.minicart-wrapper .action.close:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 16px;
    line-height: 16px;
    color: #ffffff;
    content: '\e616';
    font-family: 'luma-icons';
    margin: 0;
    vertical-align: top;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.minicart-wrapper .action.close:hover:before {
    color: inherit;
}
.minicart-wrapper .action.close:active:before {
    color: inherit;
}
.minicart-wrapper .action.showcart {
    white-space: nowrap;
}
.minicart-wrapper .action.showcart .counter.qty {
    background: #677752;
    color: #ffffff;
    height: 24px;
    line-height: 24px;
    border-radius: 0px;
    display: inline-block;
    margin: 3px 0 0;
    min-width: 18px;
    overflow: hidden;
    padding: 0 3px;
    text-align: center;
    white-space: normal;
}
.minicart-wrapper .action.showcart .counter.qty.empty {
    display: none;
}
.minicart-wrapper .action.showcart .counter.qty .loader > img {
    max-width: 24px;
}
.minicart-items {
    margin: 0;
    padding: 0;
    list-style: none none;
}
.minicart-items .product-item {
    padding: 20px 0;
}
.minicart-items .product-item:not(:first-child) {
    border-top: 1px solid #cccccc;
}
.minicart-items .product-item:first-child {
    padding-top: 0;
}
.minicart-items .product-item-pricing .label {
    display: inline-block;
    width: 4.5rem;
}
.minicart-items .price-minicart {
    margin-bottom: 5px;
}
.minicart-items .product-item-name {
    font-weight: 500;
    margin: 0;
    line-height: normal;
}
.minicart-items .product-item-name a {
    color: #636d70;
}
.minicart-items .product-item-details .price {
    font-weight: 700;
}
.minicart-items .product-item-details .price-including-tax,
.minicart-items .product-item-details .price-excluding-tax {
    margin: 5px 0;
}
.minicart-items .product-item-details .weee[data-label] {
    font-size: 1.1rem;
}
.minicart-items .product-item-details .details-qty {
    margin: 0;
}
.minicart-items .product-item-details > .actions { position: absolute; right: 5px; top: 25px; }

.minicart-items .product > .product-item-photo,
.minicart-items .product > .product-image-container {
    float: left;
}
.minicart-items .product .toggle {
    border: 0;
    padding: 0 40px 5px 0;
}
.minicart-items .product .toggle:after {
    color: #8f8f8f;
    margin: 0 0 0 5px;
    position: static;
}
.minicart-items .product .active > .toggle:after {
    content: '\e621';
}
.minicart-items .product.pricing {
    margin-top: 3px;
}
.minicart-items .product.options .toggle.tooltip {
    display: inline-block;
    text-decoration: none;
}
.minicart-items .product.options .toggle.tooltip > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.minicart-items .product.options .toggle.tooltip:after {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 12px;
    line-height: 12px;
    color: inherit;
    content: '\e622';
    font-family: 'luma-icons';
    margin: -3px 0 0 7px;
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.minicart-items .product.options .details {
    display: none;
}
.minicart-items .item-qty {
    margin-right: 10px;
    text-align: center;
    width: 40px;
}
.minicart-items .update-cart-item {
    font-size: 1.1rem;
    vertical-align: top;
}
.minicart-items .subtitle {
    display: none;
}
.minicart-items .action.edit,
.minicart-items .action.delete {
    display: inline-block;
    text-decoration: none;
}
.minicart-items .action.edit > span,
.minicart-items .action.delete > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.minicart-items .action.edit:before,
.minicart-items .action.delete:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 18px;
    line-height: 20px;
    content: '\e601';
    font-family: 'luma-icons';
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.minicart-items .action.delete:before {
    content: '\e900';
}
.checkout-container {
    margin: 0 0 20px;
}
.opc-wrapper {
    margin: 0 0 20px;
}
.opc-wrapper .step-title {
    border-bottom: 1px solid #cccccc;
    margin-bottom: 20px;
    margin-top: 20px;
}
.opc-wrapper .step-content {
    margin: 0;
}
.checkout-index-index .nav-sections,
.checkout-index-index .nav-toggle {
    display: none;
}
.checkout-index-index .logo {
    margin-left: 0;
}
.checkout-index-index .payment-method .payment-method.payolution select.dob-day {
    width: 90px;
}
.opc-estimated-wrapper {
    border-bottom: 1px solid #cccccc;
    margin: 0 0 15px;
    padding: 18px 15px;
}
.opc-estimated-wrapper .estimated-block {
    font-size: 22px;
    font-weight: 700;
    float: left;
}
.opc-estimated-wrapper .estimated-block .estimated-label {
    display: block;
    margin: 0 0 5px;
}
.opc-estimated-wrapper .minicart-wrapper .action.showcart:before {
    color: rgb(99, 109, 112);
}
.opc-estimated-wrapper .minicart-wrapper .action.showcart:hover:before {
    color: rgb(99, 109, 112);
}
.opc-progress-bar {
    margin: 0 0 20px;
    counter-reset: i;
    font-size: 0;
}
.opc-progress-bar-item {
    margin: 0 0 10px;
    display: inline-block;
    position: relative;
    text-align: center;
    vertical-align: top;
}

.opc-progress-bar-item > span {
    display: inline-block;
    padding-top: 45px;
    width: 100%;
    word-wrap: break-word;
    font-weight: 500;
    font-size: 1.8rem;
}
.opc-progress-bar-item > span:before,
.opc-progress-bar-item > span:after {
    background: #e4e4e4;
    height: 38px;
    margin-left: -19px;
    width: 38px;
    border-radius: 50%;
    content: '';
    left: 50%;
    position: absolute;
    top: 0;
}
.opc-progress-bar-item > span:after {
    background: #ffffff;
    height: 26px;
    margin-left: -13px;
    top: 6px;
    width: 26px;
    content: counter(i);
    counter-increment: i;
    color: #333333;
    font-weight: 600;
    font-size: 1.8rem;
}
.opc-progress-bar-item._active:before {
    background: #677752;
}
.opc-progress-bar-item._active > span {
    color: #636d70;
}
.opc-progress-bar-item._active > span:before {
    background: #677752;
}
.opc-progress-bar-item._active > span:after {
    content: '\e610';
    font-family: 'luma-icons';
    line-height: 1;
    font-size: 2.8rem;
}
.opc-progress-bar-item._complete {
    cursor: pointer;
}
.opc-progress-bar-item._complete > span {
    color: #636d70;
}
.opc-progress-bar-item._complete > span:after {
    content: '\e610';
    font-family: 'luma-icons';
    line-height: 1;
    font-size: 2.8rem;
}
.field.choice .field-tooltip {
    display: inline-block;
    margin-left: 10px;
    position: relative;
    top: -3px;
}
.field.choice .field-tooltip-action {
    line-height: 21px;
}
.field._error .control input,
.field._error .control select,
.field._error .control textarea {
    border-color: #ed8380;
}
.opc-wrapper .fieldset > .field > .label {
    font-weight: 400;
}
.field-tooltip {
    cursor: pointer;
    position: absolute;
    right: 0;
    top: 1px;
}
.field-tooltip._active {
    z-index: 100;
}
.field-tooltip._active .field-tooltip-content {
    display: block;
}
.field-tooltip._active .field-tooltip-action:before {
    color: #333333;
}
.field-tooltip .field-tooltip-action {
    display: inline-block;
    text-decoration: none;
}
.field-tooltip .field-tooltip-action > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.checkout-index-index .field input:after {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 21px;
    line-height: inherit;
    color: #bbbbbb;
    content: "\f00c";
    font-family: 'Font Awesome 5 Free';
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.field-tooltip .field-tooltip-action:hover:before {
    color: #333333;
}
._keyfocus .field-tooltip .field-tooltip-action:focus {
    z-index: 100;
}
._keyfocus .field-tooltip .field-tooltip-action:focus + .field-tooltip-content {
    display: block;
}
._keyfocus .field-tooltip .field-tooltip-action:focus:before {
    color: #333333;
}
.field-tooltip .field-tooltip-content {
    background: #f5f5f5;
    border: 1px solid #999999;
    border-radius: 1px;
    font-size: 14px;
    padding: 12px;
    width: 270px;
    display: none;
    left: 38px;
    position: absolute;
    text-transform: none;
    top: -9px;
    word-wrap: break-word;
    z-index: 2;
}
.field-tooltip .field-tooltip-content:before,
.field-tooltip .field-tooltip-content:after {
    border: 10px solid transparent;
    height: 0;
    width: 0;
    border-right-color: #f5f5f5;
    left: -21px;
    top: 12px;
    content: '';
    display: block;
    position: absolute;
    z-index: 3;
}
.field-tooltip .field-tooltip-content:before {
    border-right-color: #666666;
}
.field-tooltip .field-tooltip-content:after {
    border-right-color: #f5f5f5;
    width: 1px;
    z-index: 4;
}
.opc-wrapper .form-login,
.opc-wrapper .form-shipping-address {
    margin-bottom: 20px;
}
.opc-wrapper .form-login .fieldset .note,
.opc-wrapper .form-shipping-address .fieldset .note {
    font-size: 14px;
    margin-top: 10px;
}
.opc-wrapper .shipping-address-items {
    font-size: 0;
}
.opc-wrapper .shipping-address-item {
    border: 2px solid transparent;
    line-height: 30px;
    margin: 0 0 20px;
    padding: 20px 35px 20px 20px;
    transition: 0.3s border-color;
    width: 50%;
    display: inline-block;
    font-size: 14px;
    position: relative;
    vertical-align: top;
    word-wrap: break-word;
}
.opc-wrapper .shipping-address-item.selected-item {
    border-color: #677752;
}
.opc-wrapper .shipping-address-item.selected-item:after {
    background: #677752;
    color: #ffffff;
    content: '\e610';
    font-family: 'luma-icons';
    height: 27px;
    width: 29px;
    font-size: 19px;
    line-height: 21px;
    padding-top: 2px;
    position: absolute;
    right: 0;
    text-align: center;
    top: 0;
}
.opc-wrapper .shipping-address-item.selected-item .action-select-shipping-item {
    visibility: hidden;
}
.opc-wrapper .action-show-popup {
    margin: 0 0 20px;
}
.opc-wrapper .action-show-popup > span:before {
    content: '+';
    padding-right: 5px;
}
.opc-wrapper .action-select-shipping-item {
    float: right;
    margin: 20px 0 0;
}
.opc-wrapper .edit-address-link {
    display: block;
    float: left;
    margin: 26px 5px 0 0;
}
.checkout-shipping-method .step-title {
    margin-bottom: 0;
}
.checkout-shipping-method .no-quotes-block {
    margin: 20px 0;
}
.table-checkout-shipping-method thead th {
    display: none;
}
.table-checkout-shipping-method tbody td {
    border-top: 1px solid #cccccc;
    padding-bottom: 20px;
    padding-top: 20px;
}
.table-checkout-shipping-method tbody td:first-child {
    padding-left: 0;
    padding-right: 0;
    width: 20px;
}
.table-checkout-shipping-method tbody tr:first-child td {
    border-top: none;
}
.table-checkout-shipping-method tbody .row-error td {
    border-top: none;
    padding-bottom: 10px;
    padding-top: 0;
}
.checkout-shipping-method {
    position: relative;
}
.shipping-policy-block.field-tooltip {
    top: 12px;
}
.shipping-policy-block.field-tooltip .field-tooltip-action {
    color: #1979c3;
    cursor: pointer;
}
.shipping-policy-block.field-tooltip .field-tooltip-action:before {
    display: none;
}
.shipping-policy-block.field-tooltip .field-tooltip-content {
    width: 420px;
    top: 30px;
}
.opc-block-summary {
    background: #f5f5f5;
    padding: unset;
    margin: 0;
}
.opc-block-summary > .title {
    display: none;
}
.opc-block-summary .table-totals .totals-tax-details {
    display: table-row;
}
.opc-block-summary .mark .value {
    color: #999999;
    display: block;
}
.opc-block-summary .discount.coupon {
    display: none;
}
.opc-block-summary .grand.incl + .grand.excl .mark,
.opc-block-summary .grand.incl + .grand.excl .amount {
    border-top: 0;
    font-size: 1.4rem;
    padding-top: 0;
}
.opc-block-summary .grand.incl + .grand.excl .mark strong,
.opc-block-summary .grand.incl + .grand.excl .amount strong {
    font-weight: 400;
}
.opc-block-summary .not-calculated {
    font-style: italic;
}
.opc-block-summary .items-in-cart > .title {
    border-bottom: 1px solid #cccccc;
    padding: 8px 40px 8px 0;
    cursor: pointer;
    display: block;
    text-decoration: none;
    margin-bottom: 0;
    position: relative;
}
.opc-block-summary .items-in-cart > .title > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.opc-block-summary .items-in-cart > .title:after {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 14px;
    line-height: 20px;
    color: inherit;
    content: '\e622';
    font-family: 'luma-icons';
    margin: 3px 0 0;
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.opc-block-summary .items-in-cart > .title:after {
    position: absolute;
    right: 0;
    top: 10px;
}
.opc-block-summary .items-in-cart > .title strong {
    font-size: 18px;
    font-weight: 500;
    margin: 0;
}
.opc-block-summary .items-in-cart.active > .title:after {
    content: '\e621';
}
.opc-block-summary .items-in-cart .product {
    display: flex;
    align-items: flex-start;
    flex-direction: row;
    flex-wrap: nowrap;
    gap: 0.5em;
}

.opc-block-summary .items-in-cart .product .product-item-details {
    flex-grow: 1;
}

.opc-block-summary .minicart-items-wrapper {
    margin-top: 10px;
}
.column.main .opc-block-summary .product-item {
    margin: 0;
    padding-left: 0;
}
.opc-block-summary .product-item .product-item-inner {
    display: flex;
    justify-content: space-between;
}
.opc-block-summary .product-item .product-item-name-block {
    display: table-cell;
    padding-right: 5px;
    text-align: left;
    font-size: 1.7rem;
}
.opc-block-summary .product-item .subtotal {
    display: table-cell;
    text-align: right;
    vertical-align: bottom;
}
.opc-block-summary .product-item .price {
    font-size: 1.6rem;
    font-weight: 400;
}
.opc-block-summary .product-item .price-including-tax + .price-excluding-tax {
    margin: 0;
}
.opc-block-summary .product-item .price-including-tax + .price-excluding-tax .price {
    font-size: 1rem;
}
.opc-block-summary .actions-toolbar {
    position: relative;
    z-index: 1;
}
.opc-block-summary .actions-toolbar .secondary {
    border-top: 1px solid #cccccc;
    display: block;
    float: none;
    margin: -1px 0 0;
    padding: 15px 0 0;
    text-align: left;
}
.opc-block-summary .actions-toolbar .secondary .action {
    margin: 0;
}
.opc-block-summary .actions-toolbar .secondary .action.viewcart {
    display: none;
}
.opc-block-summary .data.table.table-totals {
    margin-bottom: 20px;
}
.authentication-dropdown {
    box-sizing: border-box;
}
.authentication-dropdown .modal-inner-wrap {
    padding: 25px;
}
.authentication-wrapper {
    float: right;
    margin-top: -60px;
    max-width: 50%;
    position: relative;
    z-index: 1;
}
._has-auth-shown .authentication-wrapper {
    z-index: 900;
}
.block-authentication .block-title {
    font-size: 1.8rem;
    border-bottom: 0;
    margin-bottom: 25px;
}
.block-authentication .block-title strong {
    font-weight: 300;
}
.block-authentication .field .label {
    font-weight: 400;
}
.block-authentication .actions-toolbar {
    margin-bottom: 5px;
}
.block-authentication .actions-toolbar > .secondary {
    padding-top: 25px;
    text-align: left;
}
.block-authentication .block[class] {
    margin: 0;
}
.block-authentication .block[class] ul {
    list-style: none;
    padding-left: 10px;
}
.block-authentication .block[class] .field .control,
.block-authentication .block[class] .field .label {
    float: none;
    width: auto;
}
.block-authentication .block[class] + .block {
    border-top: 1px solid #f5f5f5;
    margin-top: 40px;
    padding-top: 40px;
    position: relative;
}
.block-authentication .block[class] + .block:before {
    height: 36px;
    line-height: 34px;
    margin: -19px 0 0 -18px;
    min-width: 36px;
    background: #ffffff;
    border: 1px solid #f5f5f5;
    border-radius: 50%;
    box-sizing: border-box;
    color: #f5f5f5;
    content: attr(data-label);
    display: inline-block;
    left: 50%;
    letter-spacing: normal;
    padding: 0 .2rem;
    position: absolute;
    text-align: center;
    text-transform: uppercase;
    top: 0;
}
.checkout-payment-method .step-title {
}
.checkout-payment-method .payment-method._active .payment-method-content {
    display: flex;
    align-items: flex-end;
    flex-direction: column;
    gap: 2em;
}

.checkout-payment-method .payment-method-title {
    padding: 20px 0;
    margin: 0;
}
.checkout-payment-method .payment-method-title .payment-icon {
    display: inline-block;
    margin-right: 5px;
    vertical-align: middle;
}
.checkout-payment-method .payment-method-title .action-help {
    display: inline-block;
    margin-left: 5px;
}
.checkout-payment-method .payment-method-content {
    display: none;
    padding: 0 0 10px 0;
}
.checkout-payment-method .payment-method-content .fieldset:not(:last-child) {
    margin: 0 0 20px;
}
.checkout-payment-method .payment-group + .payment-group .step-title {
    margin: 20px 0 0;
}
.checkout-payment-method .field-select-billing,
.checkout-payment-method .billing-address-form {
    max-width: 600px;
}
.checkout-payment-method .billing-address-same-as-shipping-block {
    margin: 0 0 10px;
}
.checkout-payment-method .checkout-billing-address {
    margin: 0 0 20px;
}
.checkout-payment-method .checkout-billing-address .primary .action-update {
    margin-right: 0;
}
.checkout-payment-method .checkout-billing-address .billing-address-details {
    line-height: 27px;
    padding: 0 0 0 23px;
}
.checkout-payment-method .payment-method-note + .checkout-billing-address {
    margin-top: 20px;
}
.checkout-payment-method .payment-method-iframe {
    background-color: transparent;
    display: none;
    width: 100%;
}
.checkout-payment-method .no-payments-block {
    margin: 20px 0;
}
.checkout-payment-method .payment-option._active .payment-option-title .action-toggle:after {
    content: '\e621';
}
.checkout-payment-method .payment-option._collapsible .payment-option-title {
    cursor: pointer;
}
.checkout-payment-method .payment-option._collapsible .payment-option-content {
    display: none;
}
.checkout-payment-method .payment-option-title {
    border-top: 1px solid #cccccc;
    padding: 20px 0;
}
.checkout-payment-method .payment-option-title .action-toggle {
    color: #1979c3;
    display: inline-block;
    text-decoration: none;
}
.checkout-payment-method .payment-option-title .action-toggle:after {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 14px;
    line-height: 16px;
    color: #ffffff;
    content: '\e622';
    font-family: 'luma-icons';
    margin: 0 0 0 10px;
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.checkout-payment-method .payment-option-title .action-toggle:hover:after {
    color: #333333;
}
.checkout-payment-method .payment-option-title .action-toggle:active:after {
    color: #ffffff;
}
.checkout-payment-method .payment-option-content {
    padding: 0 0 20px 22px;
}
.checkout-payment-method .payment-option-inner {
    margin: 0 0 20px;
}
.checkout-payment-method .credit-card-types {
    padding: 0;
}
.checkout-payment-method .credit-card-types .item {
    display: inline-block;
    list-style: none;
    margin: 0 5px 0 0;
    vertical-align: top;
}
.checkout-payment-method .credit-card-types .item._active {
    font-weight: 700;
}
.checkout-payment-method .credit-card-types .item._active img {
    -webkit-filter: grayscale(0%);
}
.checkout-payment-method .credit-card-types .item._inactive {
    opacity: .4;
}
.checkout-payment-method .credit-card-types .item span {
    display: inline-block;
    padding-top: 6px;
    vertical-align: top;
}
.checkout-payment-method .credit-card-types img {
    -webkit-filter: grayscale(100%);
    -webkit-transition: all .6s ease;
}
.checkout-payment-method .ccard .fields > .year {
    padding-left: 5px;
}
.checkout-payment-method .ccard .fields .select {
    padding-left: 5px;
    padding-right: 5px;
}
.checkout-payment-method .ccard .month .select {
    width: 140px;
}
.checkout-payment-method .ccard .year .select {
    width: 80px;
}
.checkout-payment-method .ccard .number .input-text {
    width: 225px;
}
.checkout-payment-method .ccard > .field.cvv > .control {
    padding-right: 20px;
    width: auto;
}
.checkout-payment-method .ccard .cvv .input-text {
    width: 55px;
}
.checkout-payment-method .ccard.fieldset > .field .fields.group.group-2 .field {
    width: auto !important;
}
.checkout-agreements-block .checkout-agreements {
    margin-bottom: 20px;
}
.checkout-agreements-block .action-show {
    vertical-align: baseline;
    margin: 0;
    text-align: left;
}
.checkout-agreements-items {
    padding-bottom: 30px;
}
.checkout-agreements-items .checkout-agreements-item {
    margin-bottom: 20px;
}
.checkout-agreements-items .checkout-agreements-item-title {
    border-bottom: 0;
}
.checkout-agreement-item-content {
    overflow: auto;
}
.login-container .block-new-customer .actions-toolbar {
    margin-top: 25px;
}
.login-container .block .block-title {
    font-size: 1.8rem;
}
.login-container .fieldset:after {
    margin: 10px 0 0;
    content: attr(data-hasrequired);
    display: block;
    letter-spacing: normal;
    word-spacing: normal;
    color: #e02b27;
    font-size: 1.2rem;
}
.login-container .fieldset:after {
    margin-top: 35px;
}
.block-addresses-list .items.addresses > .item {
    margin-bottom: 20px;
}
.block-addresses-list .items.addresses > .item:last-child {
    margin-bottom: 0;
}
.block-addresses-list .items.addresses .item.actions {
    margin-top: 5px;
}
.form-address-edit #region_id, .form-address-edit .field.region {
    display: none !important;
}
.form-edit-account .fieldset.password {
    display: none;
}
.form-create-account .fieldset-fullname .fields .field {
    float: none;
}
.account .page-title-wrapper .page-title {
    display: inline-block;
}
.account .messages {
    margin-bottom: 20px;
}
.account .column.main {
    margin-bottom: 30px;
}
.account .column.main h2 {
    margin-top: 0;
}
.account .sidebar-additional {
    margin-top: 40px;
}
.account .table-wrapper {
    margin-bottom: 20px;
    border-bottom: 1px solid #e4e4e4;
}
.account .table-wrapper:last-child {
    margin-bottom: 0;
}
.account .data.table .col.actions .action.delete,
[class^='sales-guest-'] .data.table .col.actions .action.delete,
.sales-guest-view .data.table .col.actions .action.delete {
    color: #d10029;
}
.column.main .block:last-child {
    margin-bottom: 0;
}
.block .title {
    margin-bottom: 10px;
}
.block .title strong {
    font-weight: 700;
    line-height: 1.1;
    font-size: 1.4rem;
    margin-top: 2rem;
    margin-bottom: 2rem;
}
.block p:last-child {
    margin: 0;
}
.block .box-actions {
    margin-top: 5px;
}
.field.password .control {
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
}
.field.password .control .mage-error {
    -ms-flex-order: 2;
    -webkit-order: 2;
    order: 2;
}
.field.password .control .input-text {
    -ms-flex-order: 0;
    -webkit-order: 0;
    order: 0;
}
.password-strength-meter {
    background-color: #f5f5f5;
    height: 32px;
    line-height: 32px;
    padding: 0 9px;
    position: relative;
    z-index: 1;
}
.password-strength-meter:before {
    content: '';
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    z-index: -1;
}
.password-strength-meter-0 .password-strength-meter:before {
    background-color: #f5f5f5;
    width: 100%;
}
.password-strength-meter-1 .password-strength-meter:before {
    background-color: #ffafae;
    width: 25%;
}
.password-strength-meter-2 .password-strength-meter:before {
    background-color: #ffd6b3;
    width: 50%;
}
.password-strength-meter-3 .password-strength-meter:before {
    background-color: #c5eeac;
    width: 75%;
}
.password-strength-meter-4 .password-strength-meter:before {
    background-color: #81b562;
    width: 100%;
}
.control.captcha-image {
    margin-top: 10px;
}
.control.captcha-image .captcha-img {
    vertical-align: middle;
}
.order-products-toolbar {
    position: relative;
}
.table-downloadable-products .product-name {
    font-weight: 400;
    margin-right: 15px;
}
.table-downloadable-products .action.download {
    display: inline-block;
    text-decoration: none;
}
.table-downloadable-products .action.download:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: inherit;
    line-height: inherit;
    color: inherit;
    content: '\e626';
    font-family: 'luma-icons';
    margin: 0 5px 0 0;
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.page-product-downloadable .product-add-form {
    clear: both;
    margin-bottom: 25px;
}
.page-product-downloadable .product-options-bottom .field.qty {
    display: none;
}
.samples-item-title {
    font-size: 1.4rem;
    font-weight: 600;
    margin: 0 0 10px;
}
.samples-item {
    margin: 0 0 10px;
}
.field.downloads {
    margin-bottom: 10px;
}
.page-product-downloadable .product-add-form .product-options-wrapper .field.downloads > .control {
    width: 100%;
}
.field.downloads .price-container {
    display: inline;
    white-space: nowrap;
}
.field.downloads .price-container:before {
    content: ' + ';
    display: inline;
}
.field.downloads .price-excluding-tax {
    display: inline;
}
.field.downloads .price-excluding-tax:before {
    content: ' (' attr(data-label) ': ';
}
.field.downloads .price-excluding-tax:after {
    content: ')';
}
.field.downloads .price-including-tax {
    display: inline;
    font-weight: 600;
    font-size: 1.4rem;
}
.field.downloads .field.choice {
    border-bottom: 1px solid #ebebeb;
    box-sizing: border-box;
    margin-bottom: 10px;
    padding-bottom: 10px;
    padding-left: 25px;
    position: relative;
    width: 100%;
    z-index: 1;
}
.field.downloads .field.choice input {
    left: 0;
    position: absolute;
    top: 2px;
}
.field.downloads .field.downloads-all {
    border-bottom: 0;
    padding-left: 0;
}
.field.downloads .sample.link {
    float: right;
}
.field.downloads-all .label {
    color: #1979c3;
    text-decoration: none;
    cursor: pointer;
}
.field.downloads-all .label:visited {
    color: #1979c3;
    text-decoration: none;
}
.field.downloads-all .label:hover {
    color: #006bb4;
    text-decoration: underline;
}
.field.downloads-all .label:active {
    color: #677752;
    text-decoration: underline;
}
.field.downloads-all input {
    opacity: 0;
}
.field.downloads.downloads-no-separately .field.choice {
    padding-left: 0;
}
.table-wrapper.grouped {
    width: auto;
}
.table.grouped .row-tier-price > td {
    border-top: 0;
}
.table.grouped .col.item {
    width: 90%;
}
.table.grouped .col.qty {
    width: 10%;
}
.table.grouped .product-item-name {
    display: inline-block;
    font-weight: 400;
    margin-bottom: 10px;
}
.table.grouped .price-box p {
    margin: 0;
}
.table.grouped .price-box .price {
    font-weight: 700;
}
.table.grouped .price-box .old-price .price {
    font-weight: normal;
}
.page-product-grouped .product-info-main .alert {
    clear: left;
}
.filter.block {
    margin-bottom: 0;
    display: none;
}
.filter-title strong {
    background-color: #f0f0f0;
    box-shadow: inset 0 1px 0 0 #ffffff, inset 0 -1px 0 0 rgba(204, 204, 204, 0.3);
    border: 1px solid #cccccc;
    border-radius: 3px;
    font-weight: 400;
    left: 0;
    line-height: 16px;
    padding: 7px 10px;
    position: absolute;
    text-align: center;
    top: 0;
    z-index: 2;
}
.filter-title strong[data-count]:after {
    color: #ffffff;
    background: #677752;
    border-radius: 2px;
    content: attr(data-count);
    display: inline-block;
    font-size: .8em;
    line-height: 1;
    margin: 0 5px;
    min-width: 1em;
    padding: 2px;
}
.filter-title strong.disabled {
    opacity: .5;
}
.filter .block-subtitle {
    border-bottom: 1px solid #cccccc;
    font-size: 14px;
    font-weight: 600;
    line-height: 1em;
    padding: 10px 10px 20px;
}
.filter-subtitle {
    display: none;
}
.filter-current {
    margin: 0;
}
.filter-current .items {
    padding: 0;
}
.filter-current .item {
    padding-left: 0;
    position: relative;
    z-index: 1;
}
.filter .filter-current-subtitle {
    border: none;
    display: block;
    padding-bottom: 10px;
}
.filter-current .action.remove {
    left: -2px;
    position: absolute;
    top: -1px;
}
.filter-actions {
    margin-bottom: 25px;
    padding: 0 10px;
}
.filter-label {
    font-weight: 700;
}
.filter-label:after {
    content: ': ';
}
.filter-value {
    color: #858585;
}
.filter-options {
    display: none;
    margin: 0;
}
.filter-options-item {
    border-bottom: 1px solid #cccccc;
    padding-bottom: 10px;
}
.filter-options-title {
    cursor: pointer;
    font-weight: 600;
    margin: 0;
    overflow: hidden;
    padding: 10px 40px 0 10px;
    position: relative;
    text-transform: uppercase;
    word-break: break-all;
    z-index: 1;
    display: block;
    text-decoration: none;
}
.filter-options-title:after {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 13px;
    line-height: inherit;
    color: inherit;
    content: '\e622';
    font-family: 'luma-icons';
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.filter-options-title:after {
    position: absolute;
    right: 13px;
    top: 12px;
}
.filter-options-title:hover {
    color: #000000;
}
.active > .filter-options-title:after {
    content: '\e621';
}
.filter-options-content {
    margin: 0;
    padding: 10px;
}
.filter-options-content .item {
    line-height: 1.5em;
    margin: 10px 0;
}
.filter-options-content a {
    color: #494949;
    margin-left: -5px;
    margin-right: -5px;
    padding-left: 5px;
    padding-right: 7px;
}
.filter-options-content a:hover {
    background-color: #e8e8e8;
    text-decoration: none;
}
.filter-options-content a:hover + .count {
    background-color: #e8e8e8;
}
.filter-options-content .count {
    color: #858585;
    padding-right: 5px;
}
.filter-options-content .count:before {
    content: '(';
}
.filter-options-content .count:after {
    content: ')';
}
.order-links .item {
    line-height: 40px;
    margin: 0 0 -1px;
}
.order-links .item strong {
    background: #ffffff;
    border: 1px solid #cccccc;
    display: block;
    font-weight: 400;
    padding: 0 20px;
}
.order-links .item a {
    background: #f6f6f6;
    color: #7d7d7d;
    border: 1px solid #cccccc;
    display: block;
    font-weight: 400;
    padding: 0 20px;
}
.order-links .item a:hover {
    background: #ffffff;
    text-decoration: none;
}
.order-details-items {
    border: 1px solid #cccccc;
    margin-bottom: 40px;
    padding: 10px;
}
.order-details-items .order-title {
    border-bottom: 1px solid #cccccc;
    padding: 0 0 10px;
}
.order-details-items .order-title > strong {
    font-size: 2.4rem;
    font-weight: 300;
}
.order-details-items .order-title .action {
    display: inline-block;
}
.order-details-items .table-wrapper {
    margin: 0;
}
.order-details-items .table-wrapper + .actions-toolbar {
    display: none;
}
.order-details-items .table-wrapper:not(:last-child) {
    margin-bottom: 30px;
}
.order-details-items .table-order-items > thead > tr > th {
    border-bottom: 1px solid #cccccc;
}
.order-details-items .table-order-items tbody + tbody {
    border-top: 1px solid #cccccc;
}
.account .order-details-items .table-order-items tbody tr:nth-child(even) td {
    background: none;
}
.order-details-items .table-order-items tbody td {
    padding-bottom: 20px;
    padding-top: 20px;
}
.order-details-items .table-order-items tbody .col.label {
    font-weight: 700;
    padding-bottom: 5px;
    padding-top: 0;
}
.order-details-items .table-order-items tbody .col.options {
    padding: 0 0 15px;
}
.order-details-items .table-order-items tbody > .item-parent td {
    padding-bottom: 5px;
}
.order-details-items .table-order-items tbody > .item-parent + tr td {
    padding-top: 5px;
}
.order-details-items .table-order-items tbody .item-options-container td {
    padding-bottom: 15px;
    padding-top: 0;
}
.order-details-items .table-order-items .product-item-name {
    margin: 0 0 10px;
}
.order-details-items .item-options {
    margin: 0;
}
.order-details-items .item-options dt {
    margin: 0;
}
.order-details-items .item-options dd {
    margin: 0 0 15px;
}
.order-details-items .item-options dd:last-child {
    margin-bottom: 0;
}
.order-details-items .item-options.links dt {
    display: inline-block;
}
.order-details-items .item-options.links dt:after {
    content: ': ';
}
.order-details-items .item-options.links dd {
    margin: 0;
}
.order-details-items thead .col.qty {
    text-align: center;
}
.order-details-items .col.name {
    padding-top: 16px;
}
.order-details-items .action.show {
    display: inline-block;
    text-decoration: none;
    color: #333333;
}
.order-details-items .action.show > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.order-details-items .action.show:after {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 10px;
    line-height: 10px;
    color: inherit;
    content: '\e622';
    font-family: 'luma-icons';
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.order-details-items .action.show.expanded:after {
    content: '\e621';
}
.order-details-items .product-item-name {
    font-size: 1.8rem;
}
.order-details-items .items-qty .item {
    white-space: nowrap;
}
.order-details-items .items-qty .title:after {
    content: ': ';
}
.order-details-items .pages-items .item:first-child,
.order-details-items .pages-items .item:last-child {
    margin-right: 0;
}
.block-order-details-comments {
    margin: 0 0 40px;
}
.block-order-details-comments .comment-date {
    font-weight: 600;
}
.block-order-details-comments .comment-content {
    line-height: 1.6;
    margin: 0 0 20px;
}
.block-order-details-view .block-content .box {
    margin-bottom: 30px;
}
.block-order-details-view .box-title {
    font-weight: 600;
}
.block-order-details-view .box-content .payment-method .title {
    font-weight: 400;
}
.block-order-details-view .box-content .payment-method .content {
    margin: 0;
}
.block-order-details-view .box-content .payment-method .content > strong {
    font-weight: 400;
}
.block-order-details-view .box-content .payment-method .content > strong:after {
    content: ': ';
}
.column.main .order-details-items .block-order-details-gift-message {
    margin: 40px 0 0;
}
.block-order-details-gift-message + .actions-toolbar {
    display: none;
}
.order-tracking {
    margin: 0;
    padding: 20px 0;
}
.order-title + .order-tracking {
    border-bottom: 1px solid #cccccc;
}
.order-tracking .tracking-title {
    display: inline-block;
}
.order-tracking .tracking-content {
    display: inline-block;
    margin: 0 0 0 5px;
}
.order-actions-toolbar {
    margin-bottom: 25px;
}
.order-actions-toolbar .action {
    margin-right: 30px;
}
.order-actions-toolbar .action.print {
    display: none;
    margin: 0;
}
.form-orders-search .field:last-child {
    margin-bottom: 20px;
}
.block-reorder .actions-toolbar {
    margin: 17px 0;
}
.block-reorder .product-item-name {
    display: inline-block;
}
.block-reorder .product-item .field.item {
    display: inline-block;
}
.block-collapsible-nav .content {
    background: #f5f5f5;
    padding: 15px 0;
}
.block-collapsible-nav .item {
    margin: 3px 0 0;
}
.block-collapsible-nav .item:first-child {
    margin-top: 0;
}
.block-collapsible-nav .item a,
.block-collapsible-nav .item > strong {
    border-left: 3px solid transparent;
    color: #575757;
    display: block;
    padding: 5px 18px 5px 15px;
}
.block-collapsible-nav .item a {
    text-decoration: none;
}
.block-collapsible-nav .item a:hover {
    background: #e8e8e8;
}
.block-collapsible-nav .item.current a,
.block-collapsible-nav .item.current > strong {
    border-color: #677752;
    color: #000000;
    font-weight: 600;
}
.block-collapsible-nav .item.current a {
    border-color: #677752;
}
body {
    background-color: #ffffff;
}
.ie9 .page-wrapper {
    background-color: #ffffff;
    min-height: 0;
}
.page-header {
    border-bottom: 1px solid #cccccc;
    margin-bottom: 20px;
}
.page-header .panel.wrapper {
    background-color: #7e807e;
    color: #ffffff;
    margin: 0;
}
.header.panel > .header.links {
    margin: 0;
    padding: 0;
    list-style: none none;
    float: right;
    font-size: 0;
    margin-left: auto;
    margin-right: 20px;
}
.header.panel > .header.links > li {
    display: inline-block;
    vertical-align: top;
}
.header.panel > .header.links > li {
    font-size: 14px;
    margin: 0 0 0 15px;
}
.header.panel > .header.links > li > a {
    color: #ffffff;
    text-decoration: none;
}
.header.panel > .header.links > li > a:visited {
    color: #ffffff;
    text-decoration: none;
}
.header.panel > .header.links > li > a:hover {
    color: #ffffff;
    text-decoration: underline;
}
.header.panel > .header.links > li > a:active {
    color: #ffffff;
    text-decoration: underline;
}
.header.content {
    padding-top: 10px;
    position: relative;
}
.logo {
    float: left;
    margin: 0 0 10px 40px;
    position: relative;
}
.logo img {
    display: block;
}
.page-print .logo {
    display: inline-block;
    float: none;
}
.page-main > .page-title-wrapper .page-title + .action {
    margin-top: 30px;
}
.action.skip:focus {
    background: #f0f0f0;
    padding: 10px;
    box-sizing: border-box;
    left: 0;
    position: absolute;
    text-align: center;
    top: 0;
    width: 100%;
    z-index: 15;
}
.message.global p {
    margin: 0;
}
.message.global.noscript,
.message.global.cookie {
    margin: 0 0 10px;
    padding: 12px 20px 12px 25px;
    display: block;
    font-size: 1.3rem;
    background: #ffee9c;
    border-color: #d6ca8e;
    color: #333333;
    margin: 0;
}
.message.global.noscript a,
.message.global.cookie a {
    color: #1979c3;
}
.message.global.noscript a:hover,
.message.global.cookie a:hover {
    color: #006bb4;
}
.message.global.noscript a:active,
.message.global.cookie a:active {
    color: #677752;
}
.message.global.cookie {
    bottom: 0;
    left: 0;
    position: fixed;
    right: 0;
    z-index: 3;
}
.message.global.cookie .actions {
    margin-top: 10px;
}
.message.global.demo {
    margin: 0 0 10px;
    padding: 12px 20px 12px 25px;
    display: block;
    font-size: 1.3rem;
    background: #ff0101;
    border-color: none;
    color: #ffffff;
    margin-bottom: 0;
    text-align: center;
}
.message.global.demo a {
    color: #1979c3;
}
.message.global.demo a:hover {
    color: #006bb4;
}
.message.global.demo a:active {
    color: #677752;
}
.page-footer {
    margin-top: 25px;
}
.footer.content {
    border-top: 1px solid #cecece;
    padding-top: 20px;
}
.footer.content .links > li {
    background: #f5f5f5;
    font-size: 1.6rem;
    border-top: 1px solid #cecece;
    margin: 0 -15px;
    padding: 0 15px;
}
.footer.content .links a,
.footer.content .links strong {
    display: block;
    padding: 10px 0;
}
.footer.content .links strong {
    color: #a6a6a6;
    font-weight: normal;
}
.footer.content .links a {
    color: #575757;
    text-decoration: none;
}
.footer.content .links a:visited {
    color: #575757;
    text-decoration: none;
}
.footer.content .links a:hover {
    color: #333333;
    text-decoration: underline;
}
.footer.content .links a:active {
    color: #333333;
    text-decoration: underline;
}
.page-header .switcher .options,
.page-footer .switcher .options {
    display: inline-block;
    position: relative;
}
.page-header .switcher .options:before,
.page-footer .switcher .options:before,
.page-header .switcher .options:after,
.page-footer .switcher .options:after {
    content: '';
    display: table;
}
.page-header .switcher .options:after,
.page-footer .switcher .options:after {
    clear: both;
}
.page-header .switcher .options .action.toggle,
.page-footer .switcher .options .action.toggle {
    padding: 0;
    cursor: pointer;
    display: inline-block;
    text-decoration: none;
}
.page-header .switcher .options .action.toggle > span,
.page-footer .switcher .options .action.toggle > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.page-header .switcher .options .action.toggle:after,
.page-footer .switcher .options .action.toggle:after {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 10px;
    line-height: 22px;
    color: inherit;
    content: '\e622';
    font-family: 'luma-icons';
    margin: 0;
    vertical-align: top;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.page-header .switcher .options .action.toggle:hover:after,
.page-footer .switcher .options .action.toggle:hover:after {
    color: inherit;
}
.page-header .switcher .options .action.toggle:active:after,
.page-footer .switcher .options .action.toggle:active:after {
    color: inherit;
}
.page-header .switcher .options .action.toggle.active,
.page-footer .switcher .options .action.toggle.active {
    display: inline-block;
    text-decoration: none;
}
.page-header .switcher .options .action.toggle.active > span,
.page-footer .switcher .options .action.toggle.active > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.page-header .switcher .options .action.toggle.active:after,
.page-footer .switcher .options .action.toggle.active:after {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 10px;
    line-height: 22px;
    color: inherit;
    content: '\e621';
    font-family: 'luma-icons';
    margin: 0;
    vertical-align: top;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.page-header .switcher .options .action.toggle.active:hover:after,
.page-footer .switcher .options .action.toggle.active:hover:after {
    color: inherit;
}
.page-header .switcher .options .action.toggle.active:active:after,
.page-footer .switcher .options .action.toggle.active:active:after {
    color: inherit;
}
.page-header .switcher .options ul.dropdown,
.page-footer .switcher .options ul.dropdown {
    margin: 0;
    padding: 0;
    list-style: none none;
    background: #ffffff;
    border: 1px solid #bbbbbb;
    margin-top: 4px;
    min-width: 160px;
    z-index: 100;
    box-sizing: border-box;
    display: none;
    position: absolute;
    top: 100%;
    box-shadow: 0 3px 3px rgba(0, 0, 0, 0.15);
}
.page-header .switcher .options ul.dropdown li,
.page-footer .switcher .options ul.dropdown li {
    margin: 0;
    padding: 0;
}
.page-header .switcher .options ul.dropdown li:hover,
.page-footer .switcher .options ul.dropdown li:hover {
    background: #e8e8e8;
    cursor: pointer;
}
.page-header .switcher .options ul.dropdown:before,
.page-footer .switcher .options ul.dropdown:before,
.page-header .switcher .options ul.dropdown:after,
.page-footer .switcher .options ul.dropdown:after {
    border-bottom-style: solid;
    content: '';
    display: block;
    height: 0;
    position: absolute;
    width: 0;
}
.page-header .switcher .options ul.dropdown:before,
.page-footer .switcher .options ul.dropdown:before {
    border: 6px solid;
    border-color: transparent transparent #ffffff transparent;
    z-index: 99;
}
.page-header .switcher .options ul.dropdown:after,
.page-footer .switcher .options ul.dropdown:after {
    border: 7px solid;
    border-color: transparent transparent #bbbbbb transparent;
    z-index: 98;
}
.page-header .switcher .options ul.dropdown:before,
.page-footer .switcher .options ul.dropdown:before {
    left: 10px;
    top: -12px;
}
.page-header .switcher .options ul.dropdown:after,
.page-footer .switcher .options ul.dropdown:after {
    left: 9px;
    top: -14px;
}
.page-header .switcher .options.active,
.page-footer .switcher .options.active {
    overflow: visible;
}
.page-header .switcher .options.active ul.dropdown,
.page-footer .switcher .options.active ul.dropdown {
    display: block;
}
.page-header .switcher .options ul.dropdown a,
.page-footer .switcher .options ul.dropdown a {
    color: #333333;
    text-decoration: none;
    display: block;
    padding: 8px;
}
.page-header .switcher .options ul.dropdown a:visited,
.page-footer .switcher .options ul.dropdown a:visited {
    color: #333333;
    text-decoration: none;
}
.page-header .switcher .options ul.dropdown a:hover,
.page-footer .switcher .options ul.dropdown a:hover {
    color: #333333;
    text-decoration: none;
}
.page-header .switcher .options ul.dropdown a:active,
.page-footer .switcher .options ul.dropdown a:active {
    color: #333333;
    text-decoration: none;
}
.page-header .switcher strong,
.page-footer .switcher strong {
    font-weight: 400;
}
.sidebar .widget.block:not(:last-child),
.sidebar .widget:not(:last-child) {
    margin-bottom: 40px;
}
.widget {
    clear: both;
}
.page-header .widget.block,
.page-footer .widget.block {
    margin: 0;
}
.ui-datepicker td {
    padding: 0;
}
.ui-tooltip {
    background: #ffffff;
    border: 1px solid #bbbbbb;
    padding: 10px;
}
.sidebar .block-addbysku .fieldset {
    margin: 0;
}
.sidebar .block-addbysku .fieldset .fields {
    position: relative;
}
.sidebar .block-addbysku .fieldset .fields .field {
    display: inline-block;
    margin-bottom: 10px;
    vertical-align: top;
}
.sidebar .block-addbysku .fieldset .fields .field.sku {
    margin-right: -85px;
    padding-right: 90px;
    width: 100%;
}
.sidebar .block-addbysku .fieldset .fields .field.qty .qty + .mage-error {
    width: 80px;
}
.sidebar .block-addbysku .fieldset .fields .actions-toolbar {
    position: absolute;
    right: 0;
    top: 6px;
}
.sidebar .block-addbysku .form-addbysku .actions-toolbar {
    margin-bottom: 20px;
}
.sidebar .block-addbysku .form-addbysku .actions-toolbar .primary {
    text-align: left;
}
.sidebar .block-addbysku .form-addbysku .actions-toolbar .primary .action {
    width: auto;
}
.sidebar .block-addbysku .form-addbysku .actions-toolbar .secondary {
    float: none;
    text-align: left;
}
.sidebar .block-addbysku .action.add {
    display: inline-block;
    text-decoration: none;
    line-height: normal;
    padding: 2px 0;
    width: auto;
}
.sidebar .block-addbysku .action.add > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.sidebar .block-addbysku .action.add:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 31px;
    line-height: inherit;
    color: inherit;
    content: '\e61c';
    font-family: 'luma-icons';
    margin: 0;
    vertical-align: top;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.sidebar .block-addbysku .action.add:hover:before {
    color: inherit;
}
.sidebar .block-addbysku .action.add:active:before {
    color: inherit;
}
.sidebar .block-addbysku .action.links {
    display: inline-block;
    margin: 20px 0 0;
}
.sidebar .block-addbysku .action.reset {
    display: block;
    margin: 10px 0;
}
.sidebar .block-addbysku .action.remove {
    display: inline-block;
    background-image: none;
    background: none;
    -moz-box-sizing: content-box;
    border: 0;
    box-shadow: none;
    line-height: inherit;
    margin: 0;
    padding: 0;
    text-decoration: none;
    text-shadow: none;
    font-weight: 400;
}
.sidebar .block-addbysku .action.remove > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.sidebar .block-addbysku .action.remove:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 28px;
    line-height: inherit;
    color: inherit;
    content: '\e616';
    font-family: 'luma-icons';
    margin: 0;
    vertical-align: top;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.sidebar .block-addbysku .action.remove:hover:before {
    color: inherit;
}
.sidebar .block-addbysku .action.remove:active:before {
    color: inherit;
}
.sidebar .block-addbysku .action.remove:focus,
.sidebar .block-addbysku .action.remove:active {
    background: none;
    border: none;
}
.sidebar .block-addbysku .action.remove:hover {
    background: none;
    border: none;
}
.sidebar .block-addbysku .action.remove.disabled,
.sidebar .block-addbysku .action.remove[disabled],
fieldset[disabled] .sidebar .block-addbysku .action.remove {
    pointer-events: none;
    opacity: 0.5;
}
.block-banners .banner-item,
.block-banners-inline .banner-item {
    display: block;
}
.block-banners .banner-item-content,
.block-banners-inline .banner-item-content {
    margin-bottom: 20px;
}
.block-banners .banner-item-content img,
.block-banners-inline .banner-item-content img {
    display: block;
    margin: 0 auto;
}
.block-product-link.widget,
.block-category-link.widget {
    display: block;
    margin-bottom: 20px;
}
.block-product-link-inline.widget {
    margin: 0;
}
.block.widget .product-item-info {
    width: auto;
}
.block.widget .pager {
    padding: 0;
}
.block.widget .pager .toolbar-amount {
    float: none;
    font-size: 1.2rem;
}
.block.widget .pager .pages-item-previous {
    padding-left: 0;
}
.block.widget .pager .pages-item-next {
    position: relative;
}
.block.widget .pager .items {
    white-space: nowrap;
}
.columns .block-event {
    position: relative;
    z-index: 2;
}
.block-event .block-content {
    position: relative;
}
.block-event .block-content > .action {
    cursor: pointer;
    margin-top: -6px;
    position: absolute;
    top: 50%;
    z-index: 3;
}
.block-event .block-content > .action.backward,
.block-event .block-content > .action.forward {
    opacity: .5;
    overflow: hidden;
    width: 20px;
    display: inline-block;
    text-decoration: none;
}
.block-event .block-content > .action.backward > span,
.block-event .block-content > .action.forward > span {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.block-event .block-content > .action.backward:before,
.block-event .block-content > .action.forward:before {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 55px;
    line-height: 30px;
    color: inherit;
    content: '\e617';
    font-family: 'luma-icons';
    vertical-align: middle;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
}
.block-event .block-content > .action.backward:before,
.block-event .block-content > .action.forward:before {
    margin-left: -15px;
}
.block-event .block-content > .action.backward {
    left: 0;
}
.block-event .block-content > .action.forward {
    right: 0;
}
.block-event .block-content > .action.forward:before {
    content: '\e608';
}
.block-event .block-content > .action.forward:before {
    margin-left: -20px;
}
.block-event .block-content > .action.inactive {
    cursor: default;
    display: none;
}
.block-event .block-content > .action:hover {
    opacity: 1;
}
.block-event .slider-panel {
    overflow: hidden;
    position: relative;
    z-index: 2;
}
.block-event .slider-panel .slider {
    white-space: nowrap;
}
.block-event .slider-panel .slider .item {
    background: #f0f0f0;
    display: inline-block;
    text-align: center;
    vertical-align: top;
    white-space: normal;
    width: 16.35%;
}
.sidebar-main .block-event .slider-panel .slider .item,
.sidebar-additional .block-event .slider-panel .slider .item {
    width: 100%;
}
.block-event .slider-panel .slider .item img {
    max-width: 100%;
}
.block-event .slider-panel .slider .item .category-name {
    display: inline-block;
    font-size: 1.8rem;
    padding: 15px 5px 0;
    word-break: break-all;
}
.block-event .slider-panel .slider .item .ticker {
    margin: 0;
    padding: 0;
    list-style: none none;
}
.block-event .slider-panel .slider .item .ticker li {
    display: none;
    margin: 0 5px;
}
.block-event .slider-panel .slider .item .ticker .value {
    font-size: 2.6rem;
    font-weight: 300;
}
.block-event .slider-panel .slider .item .ticker .label {
    display: block;
    font-size: 1.3rem;
}
.block-event .slider-panel .slider .item .dates {
    font-weight: 300;
}
.block-event .slider-panel .slider .item .dates .start,
.block-event .slider-panel .slider .item .dates .end,
.block-event .slider-panel .slider .item .dates .date {
    display: block;
}
.block-event .slider-panel .slider .item .dates .start:after {
    font-size: 1.8rem;
    content: '\2013';
    display: block;
}
.block-event .slider-panel .slider .item .dates .date {
    font-size: 1.8rem;
    font-weight: 300;
}
.block-event .slider-panel .slider .item .dates .time {
    font-size: 1.3rem;
}
.block-event .slider-panel .slider .item .box-event .box-content {
    padding: 5px 0 10px;
}
.block-event .slider-panel .slider .item .box-event .box-title {
    display: block;
    font-weight: 300;
    margin: 10px 0 0;
}
.block-event .slider-panel .slider .item .action.show {
    display: block;
    padding: 0;
}
.block-event .slider-panel .slider .item .action.show .category.name {
    display: inline-block;
    padding: 30px 10px 0;
}
.block-event .slider-panel .slider .item .action.show img {
    display: block;
}
.block-event .slider-panel .slider .item .action.event {
    background-image: none;
    background: #1979c3;
    border: 1px solid #1979c3;
    color: #ffffff;
    cursor: pointer;
    display: inline-block;
    font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-weight: 600;
    padding: 7px 15px;
    font-size: 1.4rem;
    box-sizing: border-box;
    vertical-align: middle;
    margin-top: 15px;
}
.block-event .slider-panel .slider .item .action.event:focus,
.block-event .slider-panel .slider .item .action.event:active {
    background: #006bb4;
    border: 1px solid #006bb4;
    color: #ffffff;
}
.block-event .slider-panel .slider .item .action.event:hover {
    background: #006bb4;
    border: 1px solid #006bb4;
    color: #ffffff;
}
.block-event .slider-panel .slider .item .action.event.disabled,
.block-event .slider-panel .slider .item .action.event[disabled],
fieldset[disabled] .block-event .slider-panel .slider .item .action.event {
    opacity: 0.5;
    cursor: default;
    pointer-events: none;
}
.block-event .slider-panel .slider .item .action.event:hover {
    text-decoration: none;
}
.block-static-block.widget,
.block-cms-link.widget {
    margin-bottom: 20px;
}
.links .block-static-block.widget,
.links .block-cms-link.widget {
    margin-bottom: 0;
}
.block-cms-link-inline.widget {
    margin: 0;
}
.block-wishlist-search .form-wishlist-search {
    margin: 20px 0 0;
}
.block-cms-hierarchy-link.widget {
    display: block;
    margin-bottom: 20px;
}
.block-cms-hierarchy-link-inline.widget {
    margin: 0;
}
@media only screen and (max-width: 767px) {
    .abs-visually-hidden-mobile-m {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px;
    }
    .abs-add-clearfix-mobile-m:before,
    .abs-add-clearfix-mobile-m:after,
    .form-create-return .fieldset > .actions-toolbar:before,
    .form-create-return .fieldset > .actions-toolbar:after,
    .account .column.main .block:not(.widget) .block-title:before,
    .account .column.main .block:not(.widget) .block-title:after {
        content: '';
        display: table;
    }
    .abs-add-clearfix-mobile-m:after,
    .form-create-return .fieldset > .actions-toolbar:after,
    .account .column.main .block:not(.widget) .block-title:after {
        clear: both;
    }
    .abs-toggling-title-mobile,
    .block-wishlist-management .wishlist-select .wishlist-name,
    .block-collapsible-nav .title {
        border-top: 1px solid #cccccc;
        cursor: pointer;
        margin-bottom: 0;
        position: relative;
        border-bottom: 1px solid #cccccc;
        padding: 10px 40px 10px 15px;
        display: block;
        text-decoration: none;
    }
    .block-wishlist-management .wishlist-select .wishlist-name:after,
    .block-collapsible-nav .title:after {
        position: absolute;
        right: 20px;
        top: 10px;
    }
    .abs-toggling-title-mobile:after,
    .block-wishlist-management .wishlist-select .wishlist-name:after,
    .block-collapsible-nav .title:after {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 12px;
        line-height: inherit;
        color: inherit;
        content: '\e622';
        font-family: 'luma-icons';
        margin: 3px 0 0 0;
        vertical-align: middle;
        display: inline-block;
        font-weight: normal;
        overflow: hidden;
        speak: none;
        text-align: center;
    }
    .abs-toggling-title-mobile.active:after,
    .block-wishlist-management .wishlist-select .wishlist-name.active:after,
    .block-collapsible-nav .title.active:after {
        content: '\e621';
    }
    .abs-icon-add-mobile,
    .block-wishlist-management .wishlist-add.item .add {
        display: block;
        text-decoration: none;
    }
    .abs-icon-add-mobile:before,
    .block-wishlist-management .wishlist-add.item .add:before {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 10px;
        line-height: 10px;
        color: inherit;
        content: '\e61c';
        font-family: 'luma-icons';
        margin: 0 5px 0 0;
        vertical-align: middle;
        display: inline-block;
        font-weight: normal;
        overflow: hidden;
        speak: none;
        text-align: center;
    }
    .abs-checkout-tooltip-content-position-top-mobile,
    .field-tooltip .field-tooltip-content {
        right: -10px;
        top: 40px;
        left: auto;
    }
    .field-tooltip .field-tooltip-content:before,
    .field-tooltip .field-tooltip-content:after {
        border: 10px solid transparent;
        height: 0;
        width: 0;
        margin-top: -21px;
        right: 10px;
        left: auto;
        top: 0;
    }
    .field-tooltip .field-tooltip-content:before {
        border-bottom-color: #666666;
    }
    .field-tooltip .field-tooltip-content:after {
        border-bottom-color: #f5f5f5;
        top: 1px;
    }
    .navigation,
    .breadcrumbs,
    .page-header .header.panel,
    .header.content,
    .footer.content,
    .page-main,
    .page-wrapper > .widget,
    .page-wrapper > .page-bottom,
    .block.category.event,
    .top-container {
        padding-left: 15px;
        padding-right: 15px;
    }
    .account .page-main,
    .cms-privacy-policy .page-main {
        padding-top: 41px;
        position: relative;
    }
    .navigation {
        padding: 0;
    }
    .navigation .parent .level-top {
        position: relative;
        display: block;
        text-decoration: none;
    }
    .nav-sections {
        -webkit-overflow-scrolling: touch;
        -webkit-transition: left 0.3s;
        -moz-transition: left 0.3s;
        -ms-transition: left 0.3s;
        transition: left 0.3s;
        height: 100%;
        left: -80%;
        left: calc(-1 * (100% - 54px));
        overflow: auto;
        position: fixed;
        top: 0;
        width: 80%;
        width: calc(100% - 54px);
    }
    .nav-sections .switcher {
        border-top: 1px solid #d1d1d1;
        font-size: 1.6rem;
        font-weight: 700;
        margin: 0;
        padding: .8rem 3.5rem .8rem 2rem;
    }
    .nav-sections .switcher .label {
        display: block;
        margin-bottom: 5px;
    }
    .nav-sections .switcher:last-child {
        border-bottom: 1px solid #d1d1d1;
    }
    .nav-sections .switcher-trigger strong {
        position: relative;
        display: block;
        text-decoration: none;
    }
    .nav-sections .switcher-trigger strong:after {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 42px;
        line-height: inherit;
        color: inherit;
        content: '\e622';
        font-family: 'luma-icons';
        vertical-align: middle;
        display: inline-block;
        font-weight: normal;
        overflow: hidden;
        speak: none;
        text-align: center;
    }
    .nav-sections .switcher-trigger strong:after {
        position: absolute;
        right: -40px;
        top: -16px;
    }
    .nav-sections .switcher-trigger.active strong:after {
        content: '\e621';
    }
    .nav-sections .switcher-dropdown {
        margin: 0;
        padding: 0;
        list-style: none none;
        padding: 10px 0;
    }
    .nav-sections .header.links {
        margin: 0;
        padding: 0;
        list-style: none none;
        border-bottom: 1px solid #d1d1d1;
    }
    .nav-sections .header.links li {
        font-size: 1.6rem;
        margin: 0;
    }
    .nav-sections .header.links li.greet.welcome {
        border-top: 1px solid #d1d1d1;
        font-weight: 700;
        padding: 0.8rem 20px;
    }
    .nav-sections .header.links li > a {
        border-top: 1px solid #d1d1d1;
    }
    .nav-sections .header.links a,
    .nav-sections .header.links a:hover {
        color: #575757;
        text-decoration: none;
        display: block;
        font-weight: 700;
        padding: 0.8rem 20px;
    }
    .nav-sections .header.links .header.links {
        border: 0;
    }
    .nav-before-open {
        height: 100%;
        overflow-x: hidden;
        width: 100%;
    }
    .nav-before-open .page-wrapper {
        -webkit-transition: left 0.3s;
        -moz-transition: left 0.3s;
        -ms-transition: left 0.3s;
        transition: left 0.3s;
        height: 100%;
        left: 0;
        overflow: hidden;
        position: relative;
    }
    .nav-before-open body {
        height: 100%;
        overflow: hidden;
        position: relative;
        width: 100%;
    }
    .nav-open .page-wrapper {
        left: 80%;
        left: calc(100% - 54px);
    }
    .nav-open .nav-sections {
        -webkit-box-shadow: 0 0 5px 0 rgba(50, 50, 50, 0.75);
        -moz-box-shadow: 0 0 5px 0 rgba(50, 50, 50, 0.75);
        -ms-box-shadow: 0 0 5px 0 rgba(50, 50, 50, 0.75);
        box-shadow: 0 0 5px 0 rgba(50, 50, 50, 0.75);
        left: 0;
        z-index: 99;
    }
    .nav-open .nav-toggle:after {
        background: rgba(0, 0, 0, 0.7);
        content: '';
        display: block;
        height: 100%;
        position: fixed;
        right: 0;
        top: 0;
        width: 100%;
        z-index: 1;
    }
    .nav-sections-items {
        position: relative;
        z-index: 1;
    }
    .nav-sections-items:before,
    .nav-sections-items:after {
        content: '';
        display: table;
    }
    .nav-sections-items:after {
        clear: both;
    }
    .nav-sections-item-title {
        background: #e3e3e3;
        border: solid #d7d7d7;
        border-width: 0 0 1px 1px;
        box-sizing: border-box;
        float: left;
        height: 71px;
        padding-top: 24px;
        text-align: center;
        width: 33.33%;
    }
    .nav-sections-item-title.active {
        background: transparent;
        border-bottom: 0;
    }
    .nav-sections-item-title .nav-sections-item-switch:hover {
        text-decoration: none;
    }
    .nav-sections-item-content {
        box-sizing: border-box;
        float: right;
        margin-left: -100%;
        margin-top: 71px;
        width: 100%;
        padding: 25px 0;
    }
    .nav-sections-item-content:before,
    .nav-sections-item-content:after {
        content: '';
        display: table;
    }
    .nav-sections-item-content:after {
        clear: both;
    }
    .nav-sections-item-content.active {
        display: block;
    }
    .navigation {
        background: #f0f0f0;
        box-sizing: border-box;
    }
    .navigation ul {
        margin: 0;
        padding: 0;
    }
    .navigation li {
        margin: 0;
    }
    .navigation a {
        display: block;
        padding-top: 10px;
        padding-right: 0;
        padding-bottom: 10px;
        padding-left: 15px;
    }
    .navigation a,
    .navigation a:hover {
        color: #575757;
        text-decoration: none;
    }
    .navigation .level0 {
        border-top: 1px solid #d1d1d1;
        font-size: 1.6rem;
    }
    .navigation .level0 > .level-top {
        font-weight: 600;
        padding: 8px 40px 8px 20px;
        text-transform: uppercase;
        word-wrap: break-word;
    }
    .navigation .level0 > .level-top:hover {
        color: #333333;
    }
    .navigation .level0.active .all-category .ui-state-focus {
        border-color: #677752;
        border-style: solid;
        border-width: 0 0 0 8px;
        color: #333333;
        padding-left: 7px;
        display: inline-block;
    }
    .navigation .level0 > .level1 {
        font-weight: 600;
    }
    .navigation .level0.active > a:not(.ui-state-active),
    .navigation .level0.has-active > a:not(.ui-state-active) {
        border-color: #677752;
        border-style: solid;
        border-width: 0 0 0 8px;
        color: #333333;
    }
    .navigation .level0.active > a:not(.ui-state-active) span:not(.ui-menu-icon),
    .navigation .level0.has-active > a:not(.ui-state-active) span:not(.ui-menu-icon) {
        margin-left: -8px;
    }
    .navigation li.level0:last-child {
        border-bottom: 1px solid #d1d1d1;
    }
    .navigation .submenu {
        display: none!important;
    }
    .navigation .submenu > li {
        word-wrap: break-word;
    }
    .navigation .submenu > li > a:hover {
        color: #333333;
    }
    .navigation .submenu:not(:first-child) {
        font-weight: 400;
        line-height: 1.3;
        left: auto !important;
        overflow-x: hidden;
        padding: 0;
        position: relative;
        top: auto !important;
        transition: left 0.3s ease-out;
    }
    .navigation .submenu:not(:first-child) > li > a {
        padding-left: 15px;
    }
    .navigation .submenu:not(:first-child) > li:last-child {
        margin-bottom: 0;
    }
    .navigation .submenu:not(:first-child) ul {
        display: block;
        padding-left: 15px;
    }
    .navigation .submenu:not(:first-child) ul > li {
        margin: 0;
    }
    .navigation .submenu:not(:first-child) ul > li a {
        color: #575757;
        display: block;
        line-height: normal;
        padding: 10px 20px;
    }
    .navigation .submenu:not(:first-child) ul > li a:hover {
        color: #333333;
    }
    .navigation .submenu:not(:first-child).expanded {
        display: block !important;
        padding-right: 0;
        top: 0 !important;
    }
    .navigation .submenu:not(:first-child) .active > a {
        border-color: #677752;
        border-style: solid;
        border-width: 0 0 0 8px;
        color: #333333;
        padding-left: 7px;
    }
    .navigation .submenu:not(:first-child) .level1.active > a {
        padding-left: 7px;
    }
    .breadcrumbs {
        display: none;
    }
    .modal-popup.modal-slide {
        left: 0;
        z-index: 900;
    }
    .modal-popup.modal-slide._show .modal-inner-wrap {
        -webkit-transform: translateX(0);
        transform: translateX(0);
    }
    .modal-popup.modal-slide .modal-inner-wrap {
        height: 100%;
        overflow-y: auto;
        position: static;
        -webkit-transform: translateX(100%);
        transform: translateX(100%);
        -webkit-transition: -webkit-transform 0.3s ease-in-out;
        transition: transform 0.3s ease-in-out;
        width: auto;
    }
    .modal-popup.modal-slide .modal-inner-wrap {
        margin: 0;
        max-height: none;
    }
    .custom-slide {
        bottom: 0;
        min-width: 0;
        position: fixed;
        right: 0;
        top: 0;
        visibility: hidden;
        opacity: 0;
        -webkit-transition: visibility 0s .3s, opacity .3s ease;
        transition: visibility 0s .3s, opacity .3s ease;
        left: 0;
        z-index: 900;
    }
    .custom-slide._show {
        visibility: visible;
        opacity: 1;
        -webkit-transition: opacity .3s ease;
        transition: opacity .3s ease;
    }
    .custom-slide._show .modal-inner-wrap {
        -webkit-transform: translate(0, 0);
        transform: translate(0, 0);
    }
    .custom-slide .modal-inner-wrap {
        background-color: #ffffff;
        opacity: 1;
        pointer-events: auto;
    }
    .custom-slide._show .modal-inner-wrap {
        -webkit-transform: translateX(0);
        transform: translateX(0);
    }
    .custom-slide .modal-inner-wrap {
        height: 100%;
        overflow-y: auto;
        position: static;
        -webkit-transform: translateX(100%);
        transform: translateX(100%);
        -webkit-transition: -webkit-transform 0.3s ease-in-out;
        transition: transform 0.3s ease-in-out;
        width: auto;
    }
    .custom-slide._show {
        -webkit-overflow-scrolling: touch;
        overflow-x: hidden;
        overflow-y: auto;
    }
    .custom-slide .modal-inner-wrap {
        background-color: #ffffff;
        box-sizing: border-box;
        height: auto;
        min-height: 100%;
    }
    body._has-modal-custom {
        height: 100vh;
        overflow: hidden;
        width: 100vw;
    }
    body._has-modal-custom .modal-custom-overlay {
        background-color: rgba(51, 51, 51, 0.55);
    }
    .modal-content table {
        display: block;
    }
    .modal-content table tr,
    .modal-content table td {
        display: block;
        width: 100%;
    }
    .modal-popup.modal-slide .modal-inner-wrap[class] {
        background-color: #ffffff;
    }
    .modal-popup.modal-slide._inner-scroll._show {
        -webkit-overflow-scrolling: touch;
        overflow-y: auto;
    }
    .modal-popup.modal-slide._inner-scroll .modal-inner-wrap {
        height: auto;
        min-height: 100%;
    }
    .modal-popup .modal-title {
        font-size: 14px;
        font-weight: 700;
    }
    .gift-options .actions-toolbar .action-update {
        font-size: 2rem;
        padding: 15px;
        width: 100%;
    }
    .gift-item-block {
        border-top: 1px solid #f5f5f5;
        border-bottom: 0;
    }
    .cart.table-wrapper .gift-content {
        margin-right: -10px;
    }
    .gift-wrapping {
        border-bottom: 1px solid #f5f5f5;
        margin-bottom: 20px;
        padding-bottom: 10px;
    }
    .gift-wrapping-list {
        width: 100%;
    }
    .gift-wrapping-preview img {
        width: 100%;
    }
    .item-actions .actions-toolbar .gift-options .gift-wrapping,
    .item-actions .actions-toolbar .gift-options .gift-message {
        display: block;
    }
    .form-add-invitations .additional,
    .form-add-invitations .field.text {
        margin-top: 20px;
    }
    .map-popup {
        max-width: 100%;
    }
    .map-old-price,
    .map-show-info {
        display: inline-block;
    }
    .map-old-price:not(:last-child),
    .map-show-info:not(:last-child) {
        margin-bottom: 10px;
    }
    .block-wishlist-management {
        clear: both;
        margin-top: -21px;
    }
    .block-wishlist-management .wishlist-select {
        margin: 0 -15px 20px;
    }
    .block-wishlist-management .wishlist-select-items {
        border-bottom: 1px solid #cccccc;
        background: #f5f5f5;
        display: none;
        padding: 15px 0;
    }
    .block-wishlist-management .wishlist-select-items.active {
        display: block;
    }
    .block-wishlist-management .wishlist-select-items .item {
        margin: 3px 0 0;
    }
    .block-wishlist-management .wishlist-select-items .item:first-child {
        margin-top: 0;
    }
    .block-wishlist-management .wishlist-select-items .item.current {
        display: none;
    }
    .block-wishlist-management .wishlist-select-items .item a {
        display: block;
        padding: 5px 18px;
        text-decoration: none;
    }
    .block-wishlist-management .wishlist-select-items .item a:hover {
        background: #e8e8e8;
    }
    .block-wishlist-management .wishlist-title strong {
        font-size: 2.6rem;
    }
    .block-wishlist-management .wishlist-info {
        margin-bottom: 15px;
    }
    .block-wishlist-management .wishlist-toolbar-select,
    .block-wishlist-management .wishlist-toolbar-actions {
        margin-bottom: 15px;
    }
    .products-grid.wishlist .product-item-checkbox {
        left: 20px;
        position: absolute;
        top: 20px;
    }
    .page-multiple-wishlist .products-grid.wishlist .product-item-photo {
        margin-left: 25px;
    }
    .page-multiple-wishlist .products-grid.wishlist .product-item-name,
    .page-multiple-wishlist .products-grid.wishlist .product-item-description,
    .page-multiple-wishlist .products-grid.wishlist .product-item .price-box,
    .page-multiple-wishlist .products-grid.wishlist .product-item-tooltip {
        margin-left: 115px;
    }
    .products-grid.wishlist .wishlist-dropdown {
        display: none;
    }
    .multishipping-checkout-success .nav-toggle {
        display: block;
    }
    .multishipping-checkout-success .logo {
        margin-left: 40px;
    }
    .reward-settings + .actions-toolbar {
        margin-top: 10px;
    }
    .account .column.main .returns-details-items .block-returns-tracking .block-title > .action.track {
        display: block;
        float: none;
        margin: 10px 0 0;
    }
    .my-credit-cards .table:not(.totals):not(.cart):not(.table-comparison) .status {
        display: none;
    }
    .my-credit-cards .table:not(.totals):not(.cart):not(.table-comparison) .status:before {
        display: none;
    }
    .my-credit-cards .table:not(.totals):not(.cart):not(.table-comparison) .status._default {
        display: block;
    }
    .my-credit-cards .card-type img {
        vertical-align: middle;
    }
    .products-grid.wishlist {
        margin-bottom: 30px;
        margin-right: -10px;
    }
    .products-grid.wishlist .product-item {
        padding: 20px 10px 20px 20px;
        position: relative;
    }
    .products-grid.wishlist .product-item-photo {
        float: left;
        margin-right: 20px;
    }
    .products-grid.wishlist .product-item-name {
        font-size: 1.6rem;
    }
    .products-grid.wishlist .product-item-actions {
        display: block;
    }
    .products-grid.wishlist .product-item-actions .action {
        margin-right: 15px;
    }
    .products-grid.wishlist .product-item-actions .action:last-child {
        margin-right: 0;
    }
    .products-grid.wishlist .product-item-actions .action.edit {
        float: left;
    }
    .products-grid.wishlist .product-item-actions .action.delete {
        float: right;
    }
    .products-grid.wishlist .product-item-actions .action.edit,
    .products-grid.wishlist .product-item-actions .action.delete {
        margin-top: 7px;
    }
    .products-grid.wishlist .product-item-name,
    .products-grid.wishlist .product-item-description,
    .products-grid.wishlist .product-item .price-box,
    .products-grid.wishlist .product-item-tooltip {
        margin-left: 95px;
    }
    .products-grid.wishlist .product-item .box-tocart {
        float: left;
        margin-right: 20px;
    }
    .products-grid.wishlist .product-item .box-tocart .stock {
        margin-top: 7px;
    }
    .products-grid.wishlist .product-item .giftregisty-dropdown,
    .products-grid.wishlist .product-item .field.qty {
        display: none;
    }
    .products-grid.wishlist .product-image-container {
        max-width: 80px;
    }
    .wishlist-index-index .product-item {
        width: 100%;
    }
    .wishlist-index-index .product-item-info {
        width: auto;
    }
    .catalog-product-view .column.main {
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
    }
    .catalog-product-view .product.media {
        -ms-flex-order: -1;
        -webkit-order: -1;
        order: -1;
    }
    .compare.wrapper,
    [class*='block-compare'] {
        display: none;
    }
    .cart-container .form-cart {
        margin-bottom: 0;
    }
    .cart-container .form-cart .actions.main {
        text-align: center;
        padding-left: 15px;
        padding-right: 15px;
    }
    .cart-discount {
        border-bottom: 1px solid #cccccc;
    }
    .cart.table-wrapper {
        border-top: none;
    }
    .cart.table-wrapper thead .col.item,
    .cart.table-wrapper thead .col.qty,
    .cart.table-wrapper thead .col.price,
    .cart.table-wrapper thead .col.subtotal,
    .cart.table-wrapper thead .col.msrp {
        display: none;
    }
    .cart.table-wrapper .col.qty,
    .cart.table-wrapper .col.price,
    .cart.table-wrapper .col.subtotal,
    .cart.table-wrapper .col.msrp {
        box-sizing: border-box;
        display: block;
        float: left;
        white-space: nowrap;
        width: 33% !important;
    }
    .cart.table-wrapper .col.qty:before,
    .cart.table-wrapper .col.price:before,
    .cart.table-wrapper .col.subtotal:before,
    .cart.table-wrapper .col.msrp:before {
        content: attr(data-th);
        display: block;
        font-weight: 500;
        padding-bottom: 10px;
    }
    .cart.table-wrapper .col.msrp {
        white-space: normal;
    }
    .cart.table-wrapper .item .col.item {
        padding-bottom: 0;
    }

    .cart.table-wrapper tbody > tr > td:last-child {
        border: 0;
    }
    .cart-totals {
        padding-left: 15px;
        padding-right: 15px;
    }
    .cart-totals .table-wrapper {
        border-top: 0;
    }
    .cart-totals .totals tbody > tr:not(:last-child) > td:last-child {
        border: 0;
    }
    .block.discount {
        padding-left: 15px;
        padding-right: 15px;
    }
    .block.discount .content {
        padding: 0;
    }
    .checkout-shipping-additional .shipping-additional-info-block .delivery-info-block {
        flex-direction: row;
        flex-wrap: wrap;
    }
    .checkout-shipping-additional .shipping-additional-info-block .actions-toolbar {
        padding-left: 15px;
        padding-right: 15px;
        float: none;
    }
    .checkout-shipping-additional .shipping-additional-info-block .actions-toolbar button.action {
        text-align: center;
    }
    .cart-gift-container {
        padding-left: 15px;
        padding-right: 15px;
    }
    .cart-gift-container .button {
        width: 100%;
    }
    .checkout-payment-method .payment-methods {
        margin: 0;
    }
    .checkout-payment-method .step-title {

    }
    .checkout-payment-method .payment-method-title {
        padding: 15px;
    }
    .checkout-payment-method .payment-method-content {
        padding: 0;
    }
    .checkout-payment-method .checkout-billing-address .action-cancel {
        margin-top: 10px;
    }
    .checkout-payment-method .payment-option {
        margin: 0 -15px;
    }
    .checkout-payment-method .payment-option .payment-option-title {
        padding: 15px;
    }
    .checkout-payment-method .payment-option .payment-option-content {
        padding: 0 15px 20px;
    }
    .login-container .fieldset:after {
        text-align: center;
    }
    .account .page.messages {
        margin-bottom: 40px;
    }
    .account .column.main .block:not(.widget) .block-title > .action {
        float: right;
        margin-top: 5px;
    }
    .account .column.main .block:not(.widget) .block-content .box {
        margin-bottom: 20px;
    }
    .account .column.main .block:not(.widget) .block-content .box:last-child {
        margin-bottom: 0;
    }
    .control.captcha-image .captcha-img {
        display: block;
        margin-bottom: 10px;
    }
    .customer-account-index .page-title-wrapper {
        position: relative;
    }
    .table-wrapper.grouped {
        margin-left: -15px;
        margin-right: -15px;
    }
    body.filter-active .page-header {
        display: none;
    }
    body.filter-active .page-wrapper {
        height: 0;
        margin-top: -999999em;
        visibility: hidden;
    }
    body.filter-active .columns {
        z-index: 999;
    }
    .filter.active {
        position: relative;
        visibility: visible;
        z-index: 99;
    }
    .filter.active .filter-options-item:last-child {
        margin-bottom: 40px;
    }
    .filter.active .filter-title {
        border-bottom: 1px solid #cccccc;
        height: 48px;
        left: 0;
        position: fixed;
        right: 0;
        top: 0;
        z-index: 2;
    }
    .filter.active .filter-title strong {
        box-shadow: none;
        background: none;
        border: 0;
        color: transparent;
        left: auto;
        right: 3px;
        top: 10px;
        display: inline-block;
        text-decoration: none;
    }
    .filter.active .filter-title strong:after {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 16px;
        line-height: inherit;
        color: #7d7d7d;
        content: '\e616';
        font-family: 'luma-icons';
        vertical-align: middle;
        display: inline-block;
        font-weight: normal;
        overflow: hidden;
        speak: none;
        text-align: center;
    }
    .filter.active .filter-subtitle {
        background: transparent;
        display: block;
        height: 50px;
        left: 0;
        line-height: 32px;
        position: fixed;
        right: 0;
        top: 0;
        z-index: 1;
    }
    .filter.active .filter-options {
        background: #ffffff;
        bottom: 0;
        display: block;
        left: 0;
        overflow: scroll;
        position: fixed;
        right: 0;
        top: 50px;
        z-index: 10;
    }
    .filter .filter-subtitle {
        font-size: 20px;
        font-weight: 300;
    }
    .filter-actions {
        margin: -35px -10px 25px;
    }
    .filter-options-content {
        padding: 5px 10px;
    }
    .filter .filter-current {
        border: solid #cccccc;
        border-width: 1px 0;
        margin: 5px -10px 0;
    }
    .filter .filter-current .items {
        display: none;
    }
    .filter .filter-current-subtitle {
        position: relative;
        text-transform: uppercase;
        z-index: 1;
        display: block;
        text-decoration: none;
    }
    .filter .filter-current-subtitle:before {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 13px;
        line-height: inherit;
        color: inherit;
        content: '\e622';
        font-family: 'luma-icons';
        vertical-align: middle;
        display: inline-block;
        font-weight: normal;
        overflow: hidden;
        speak: none;
        text-align: center;
    }
    .filter .filter-current-subtitle:before {
        position: absolute;
        right: 10px;
        top: 10px;
    }
    .filter .filter-current-subtitle:after {
        color: #7d7d7d;
        content: ' (' attr(data-count) ')';
        font-size: .9em;
    }
    .filter .filter-current.active {
        padding-bottom: 30px;
    }
    .filter .filter-current.active .block-subtitle:before {
        content: '\e621';
    }
    .filter .filter-current.active .items {
        display: block;
    }
    .filter .filter-current.active + .block-actions {
        display: block;
    }
    .filter .filter-current + .block-actions {
        display: none;
    }
    .filter-no-options .filter-title:before {
        background: rgba(255, 255, 255, 0.5);
        content: '';
        display: block;
        height: 40px;
        left: -15px;
        margin-top: -60px;
        position: relative;
        width: 100px;
        z-index: 99;
    }
    .filter-no-options .filter-content {
        margin-bottom: 20px;
    }
    .page-with-filter .columns .sidebar-main {
        -ms-flex-order: 0;
        -webkit-order: 0;
        order: 0;
    }
    .order-pager-wrapper .toolbar-amount {
        left: inherit;
        position: relative;
        text-align: center;
        top: inherit;
    }
    .order-pager-wrapper .pages {
        text-align: center;
    }
    .order-pager-wrapper .action.previous,
    .order-pager-wrapper .action.next {
        margin: 0;
    }
    .block-collapsible-nav {
        left: 0;
        position: absolute;
        top: -21px;
        width: 100%;
        z-index: 5;
    }
    .block-collapsible-nav .content {
        border-bottom: 1px solid #cccccc;
        display: none;
    }
    .block-collapsible-nav .content.active {
        display: block;
    }
    .customer-name,
    .customer-welcome + .authorization-link {
        display: none;
    }
    .nav-sections .switcher-trigger strong:after {
        font-size: 1.6rem;
        right: -25px;
        top: -2px;
    }
    .page-footer .footer.content .switcher-store {
        background: #f5f5f5;
        font-size: 1.6rem;
        margin: 0 -15px;
    }
    .page-footer .footer.content .switcher-store .switcher-options {
        display: block;
    }
    .page-footer .footer.content .switcher-store .switcher-options ul.dropdown {
        background: none;
        border: 0;
        box-shadow: none;
        margin: 0;
        position: relative;
    }
    .page-footer .footer.content .switcher-store .switcher-options ul.dropdown:before,
    .page-footer .footer.content .switcher-store .switcher-options ul.dropdown:after {
        display: none;
    }
    .page-footer .footer.content .switcher-store .switcher-options .switcher-trigger,
    .page-footer .footer.content .switcher-store .switcher-options .switcher-option {
        border-top: 1px solid #cecece;
        display: block;
        padding: 10px 20px;
    }
    .page-footer .footer.content .switcher-store .switcher-options .switcher-trigger strong {
        padding: 10px 0;
    }
    .page-footer .footer.content .switcher-store .switcher-options .switcher-option a {
        padding: 0;
    }
}
@media only screen and (max-width: 639px) {
    .abs-visually-hidden-mobile,
    .table-giftregistry-items .col.product:before,
    .table-downloadable-products .col.title:before {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px;
    }
    .abs-add-clearfix-mobile:before,
    .abs-add-clearfix-mobile:after,
    .abs-checkout-order-review tbody > tr:before,
    .abs-checkout-order-review tbody > tr:after,
    .table-wrapper .table:not(.totals):not(.table-comparison) tfoot tr:before,
    .table-wrapper .table:not(.totals):not(.table-comparison) tfoot tr:after,
    .table-giftregistry-items .col.product:before,
    .table-giftregistry-items .col.product:after,
    .multicheckout.order-review .data.table tbody > tr:before,
    .multicheckout.order-review .data.table tbody > tr:after {
        content: '';
        display: table;
    }
    .abs-add-clearfix-mobile:after,
    .abs-checkout-order-review tbody > tr:after,
    .table-wrapper .table:not(.totals):not(.table-comparison) tfoot tr:after,
    .table-giftregistry-items .col.product:after,
    .multicheckout.order-review .data.table tbody > tr:after {
        clear: both;
    }
    .multicheckout.order-review .data.table tbody > tr:not(:last-child) {
        border-bottom: 1px solid #cccccc;
    }
    .multicheckout.order-review .data.table tbody > tr > td.col.item:before {
        display: none;
    }
    .multicheckout.order-review .data.table tbody > tr > td.col.qty,
    .multicheckout.order-review .data.table tbody > tr > td.col.price,
    .multicheckout.order-review .data.table tbody > tr > td.col.subtotal {
        box-sizing: border-box;
        float: left;
        text-align: center;
        white-space: nowrap;
        width: 33%;
    }
    .multicheckout.order-review .data.table tbody > tr > td.col.qty:before,
    .multicheckout.order-review .data.table tbody > tr > td.col.price:before,
    .multicheckout.order-review .data.table tbody > tr > td.col.subtotal:before {
        content: attr(data-th) ':';
        display: block;
        font-weight: 700;
        padding-bottom: 10px;
    }
    .multicheckout.order-review .data.table tbody > tr > td.col:last-child {
        border: 0;
    }
    .abs-checkout-order-review .product-item-name,
    .multicheckout.order-review .data.table .product-item-name {
        margin: 0;
    }
    .abs-product-items-summary tbody .col {
        padding: 10px 0 0;
    }
    .abs-product-items-summary tfoot .mark {
        padding-bottom: 5px;
        text-align: left;
    }
    .abs-product-items-summary tfoot .amount {
        padding-top: 0;
        text-align: left;
    }
    .abs-product-items-summary tfoot .grand.totals {
        font-size: 1.6rem;
    }
    .abs-product-items-summary tfoot .grand.totals .mark {
        padding-bottom: 0;
        padding-top: 5px;
    }
    .abs-product-items-summary tfoot .grand.totals .amount {
        padding-bottom: 10px;
        padding-top: 0;
    }
    .abs-product-items-summary tfoot .grand.totals .amount strong {
        border-top: none;
    }
    .abs-account-table-margin-mobile,
    .table-wrapper.orders-recent {
        margin-top: -20px;
    }
    .abs-sidebar-totals-mobile th:before,
    .abs-sidebar-totals-mobile td:before,
    .table-wrapper.comparison .table-comparison > tbody > tr > th:before,
    .table-wrapper.comparison .table-comparison > tbody > tr > td:before,
    .cart .table.items .col.item:before,
    .cart .table.items .item-actions td:before,
    .table.grouped > tbody > tr > td:before,
    .account .table-order-items .col.actions:before,
    .account .table-order-items .col.options:before,
    .cart-totals .totals th:before,
    .cart-totals .totals td:before {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px;
    }
    .abs-no-display-s,
    .table-wrapper .table:not(.totals):not(.cart):not(.table-comparison).additional-attributes tbody th,
    .order-details-items .table-order-items .options-label,
    .opc-wrapper .shipping-address-item.selected-item .action-select-shipping-item {
        display: none;
    }
    .abs-title-orders-mobile .page-main .page-title-wrapper .page-title,
    .account .page-main .page-title-wrapper .page-title,
    [class^='sales-guest-'] .page-main .page-title-wrapper .page-title,
    .sales-guest-view .page-main .page-title-wrapper .page-title {
        display: block;
    }
    .abs-title-orders-mobile .page-main .page-title-wrapper .order-status,
    .account .page-main .page-title-wrapper .order-status,
    [class^='sales-guest-'] .page-main .page-title-wrapper .order-status,
    .sales-guest-view .page-main .page-title-wrapper .order-status {
        margin: -10px 0 10px;
    }
    .abs-table-striped-mobile > tbody > tr > td:last-child,
    .account .data.table > tbody > tr > td:last-child {
        border: 0;
    }
    .abs-no-border-top,
    .product.info.detailed .additional-attributes-wrapper,
    .table-wrapper.orders-recent,
    .order-details-items .table-wrapper {
        border-top: 0;
    }
    .abs-no-border-bottom {
        border-bottom: 0;
    }
    .abs-sidebar-totals-mobile .amount,
    .cart-totals .totals .amount {
        text-align: right;
    }
    .table-wrapper {
        border-top: 1px solid #cccccc;
        overflow-x: auto;
        overflow-y: hidden;
        width: 100%;
        -ms-overflow-style: -ms-autohiding-scrollbar;
        -webkit-overflow-scrolling: touch;
        position: relative;
    }
    .table-wrapper .table:not(.totals):not(.cart):not(.table-comparison) {
        border: none;
        display: block;
    }
    .table-wrapper .table:not(.totals):not(.cart):not(.table-comparison) > thead > tr > th {
        display: none;
    }
    .table-wrapper .table:not(.totals):not(.cart):not(.table-comparison) > tbody {
        display: block;
    }
    .table-wrapper .table:not(.totals):not(.cart):not(.table-comparison) > tbody > tr {
        display: block;
    }
    .table-wrapper .table:not(.totals):not(.cart):not(.table-comparison) > tbody > tr td,
    .table-wrapper .table:not(.totals):not(.cart):not(.table-comparison) > tbody > tr th {
        border-bottom: none;
        display: block;
        padding: 5px 10px;
    }
    .table-wrapper .table:not(.totals):not(.cart):not(.table-comparison) > tbody > tr td[data-th]:before,
    .table-wrapper .table:not(.totals):not(.cart):not(.table-comparison) > tbody > tr th[data-th]:before {
        padding-right: 10px;
        content: attr(data-th) ': ';
        display: inline-block;
        font-weight: 600;
    }
    .order-details-items .table-order-items tbody td { text-align: left !important; }
    .table-wrapper .table:not(.totals):not(.cart):not(.table-comparison) > tbody > tr > th {
        background-color: #f6f6f6;
    }
    .table-wrapper .table:not(.totals):not(.cart):not(.table-comparison) tbody > tr > td:first-child {
        padding-top: 15px;
    }
    .table-wrapper .table:not(.totals):not(.cart):not(.table-comparison) tbody > tr > td:last-child {
        border-bottom: 1px solid #cccccc;
        padding-bottom: 15px;
    }
    .table-wrapper .table:not(.totals):not(.cart):not(.table-comparison) tbody > tr > td.col.qty {
        text-align: left;
    }
    .table-wrapper .table:not(.totals):not(.cart):not(.table-comparison).additional-attributes tbody td:last-child {
        border: none;
        padding: 0 0 5px;
    }
    .table-wrapper .table:not(.totals):not(.table-comparison) tfoot {
        display: block;
    }
    .table-wrapper .table:not(.totals):not(.table-comparison) tfoot tr {
        display: block;
    }
    .table-wrapper .table:not(.totals):not(.table-comparison) tfoot tr:first-child th,
    .table-wrapper .table:not(.totals):not(.table-comparison) tfoot tr:first-child td {
        padding-top: 20px;
    }
    .table-wrapper .table:not(.totals):not(.table-comparison) tfoot .mark {
        box-sizing: border-box;
        float: left;
        text-align: left;
        width: 70%;
    }
    .table-wrapper .table:not(.totals):not(.table-comparison) tfoot .amount {
        box-sizing: border-box;
        float: left;
        text-align: right;
        width: 30%;
    }
    .table-wrapper .table:not(.totals):not(.table-comparison) tfoot .grand.totals {
        font-size: 1.6rem;
    }
    .table-wrapper .table:not(.totals):not(.table-comparison) tfoot .grand.totals .amount {
        padding-bottom: 10px;
    }
    .data-table-definition-list thead {
        display: none;
    }
    .data-table-definition-list tbody th {
        padding-bottom: 0;
    }
    .data-table-definition-list tbody th,
    .data-table-definition-list tbody td {
        display: block;
        padding-left: 0;
        padding-right: 0;
    }
    .pages .pages-items {
        display: block;
        padding: 2px 40px 0;
        position: relative;
        white-space: normal;
        z-index: 1;
    }
    .pages-item-previous {
        left: 0;
        position: absolute;
        top: 0;
        z-index: 1;
    }
    .pages-item-next {
        position: absolute;
        right: 0;
        top: 0;
        z-index: 1;
    }
    .checkout-payment-method .amazon-sandbox-simulator {
        border-top: 1px solid #cccccc;
    }
    .column .block-addbysku .sku {
        margin-right: -85px;
        padding-right: 90px;
    }
    .column .block-addbysku .action.remove {
        display: inline-block;
        background-image: none;
        background: none;
        -moz-box-sizing: content-box;
        border: 0;
        box-shadow: none;
        line-height: inherit;
        margin: 0;
        padding: 0;
        text-decoration: none;
        text-shadow: none;
        font-weight: 400;
    }
    .column .block-addbysku .action.remove > span {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px;
    }
    .column .block-addbysku .action.remove:before {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 28px;
        line-height: inherit;
        color: inherit;
        content: '\e616';
        font-family: 'luma-icons';
        margin: 1px 0 0;
        vertical-align: top;
        display: inline-block;
        font-weight: normal;
        overflow: hidden;
        speak: none;
        text-align: center;
    }
    .column .block-addbysku .action.remove:hover:before {
        color: inherit;
    }
    .column .block-addbysku .action.remove:active:before {
        color: inherit;
    }
    .column .block-addbysku .action.remove:focus,
    .column .block-addbysku .action.remove:active {
        background: none;
        border: none;
    }
    .column .block-addbysku .action.remove:hover {
        background: none;
        border: none;
    }
    .column .block-addbysku .action.remove.disabled,
    .column .block-addbysku .action.remove[disabled],
    fieldset[disabled] .column .block-addbysku .action.remove {
        pointer-events: none;
        opacity: 0.5;
    }
    .cart.table-wrapper .gift-content {
        border-bottom: 1px solid #cccccc;
        margin-bottom: 20px;
    }
    .table-giftregistry-items .field.choice,
    .table-giftregistry-items .field.qty {
        display: inline-block;
    }
    .form-giftregistry-search .fieldset {
        margin-bottom: 15px;
    }
    .multicheckout .data.table .address:before {
        margin-bottom: 5px;
    }
    .multicheckout .product-item-name,
    .multicheckout .price-including-tax,
    .multicheckout .price-excluding-tax {
        display: inline-block;
    }
    .multicheckout .block-content .box:not(:last-child) {
        margin-bottom: 40px;
    }
    .multicheckout .block-content .box:last-child {
        margin-bottom: 0;
    }
    .multicheckout.order-review .box-items .data.table thead {
        display: block;
    }
    .multicheckout.order-review .box-items .data.table thead tr {
        display: block;
    }
    .multicheckout.order-review .box-items .data.table thead .col.item {
        display: block;
        padding: 0;
    }
    .multicheckout .actions-toolbar .action {
        margin-bottom: 25px;
    }
    .multicheckout .actions-toolbar > .primary {
        margin-bottom: 25px;
        margin-right: 0;
    }
    .products-grid.wishlist .product-item {
        border-bottom: 1px solid #e8e8e8;
    }
    .products-grid.wishlist .product-item:first-child {
        border-top: 1px solid #e8e8e8;
    }
    .products-list .product-item {
        table-layout: fixed;
    }
    .products-list .product-item-photo {
        padding: 0 10px 10px 0;
        width: 30%;
    }
    .toolbar-products {
        margin-bottom: 0;
    }
    .product-social-links .action.tocompare {
        display: none;
    }

    .table-wrapper.comparison .table-comparison > tbody > tr > th,
    .table-wrapper.comparison .table-comparison > tbody > tr > td {
        display: table-cell;
    }
    .block-search {
        margin-top: 10px;
    }
    .cart .table.items .col.qty {
        text-align: center;
    }
    .cart.table-wrapper .actions-toolbar > .action,
    .action-gift {
        padding: 5px 0;
        width: 100%;
        box-sizing: border-box;
    }
    .minicart-wrapper {
        margin-top: 10px;
    }
    .opc-wrapper .step-title {
        font-size: 22px;
    }
    .opc-wrapper .form-login {
        margin: 0 0 15px;
        padding: 0 0 15px;
    }
    .opc-wrapper .shipping-address-item {
        border-bottom: 1px solid #cccccc;
        margin: 0 0 15px;
        padding: 0 0 15px;
        width: 100%;
    }
    .opc-wrapper .shipping-address-item.selected-item {
        padding: 15px 35px 15px 18px;
        border-bottom-width: 2px;
    }
    .opc-wrapper .shipping-address-item.selected-item .edit-address-link {
        right: 39px;
    }
    .opc-wrapper .form-login,
    .opc-wrapper .form-shipping-address {
        margin: 0; padding: 0;
    }
    .opc-wrapper .action-select-shipping-item {
        float: none;
        margin-top: 10px;
        width: 100%;
    }
    .opc-wrapper .action-show-popup {
        width: 100%;
    }
    .opc-wrapper .edit-address-link {
        display: inline-block;
        text-decoration: none;
        margin: 0;
        position: absolute;
        right: 0;
        top: 1px;
    }
    .opc-wrapper .edit-address-link > span {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px;
    }
    .opc-wrapper .edit-address-link:before {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 20px;
        line-height: 20px;
        color: #ffffff;
        content: '\e606';
        font-family: 'luma-icons';
        vertical-align: middle;
        display: inline-block;
        font-weight: normal;
        overflow: hidden;
        speak: none;
        text-align: center;
    }
    .opc-wrapper .edit-address-link:hover:before {
        color: #333333;
    }
    .opc-wrapper .edit-address-link:active:before {
        color: #ffffff;
    }
    .shipping-policy-block.field-tooltip {
        margin-bottom: 20px;
        position: relative;
        right: auto;
        top: auto;
    }
    .shipping-policy-block.field-tooltip .field-tooltip-content {
        width: 300px;
        right: auto;
    }
    .shipping-policy-block.field-tooltip .field-tooltip-content:before,
    .shipping-policy-block.field-tooltip .field-tooltip-content:after {
        right: auto;
    }
    .account .column.main,
    .account .sidebar-additional {
        margin: 0;
    }
    .page-product-downloadable .product-options-wrapper {
        margin-bottom: 25px;
    }
    .table-downloadable-products .product-name {
        display: block;
        margin-bottom: 5px;
    }
    .page-product-downloadable .product-info-price ~ *:last-child:after {
        border-bottom: 1px solid #f5f5f5;
        content: '';
        display: block;
        height: 0;
        margin: 25px -10px;
        overflow: hidden;
        width: auto;
    }
    .table.grouped > thead > tr > th {
        border-bottom: 1px solid #cccccc;
        display: table-cell;
    }
    .table.grouped > tbody {
        border-bottom: 1px solid #cccccc;
    }
    .table.grouped > tbody > tr > td {
        padding: 11px 10px;
        border: 0;
        display: table-cell;
    }
    .table-wrapper .table.grouped:not(.totals):not(.cart):not(.table-comparison) > tbody > tr > td:last-child {
        border-bottom: 0;
    }
    .page-product-grouped .minimal-price {
        margin: 0;
    }
    .page-product-grouped .product-info-price {
        border: 0;
        margin-bottom: 0;
    }
    .page-product-grouped .product-info-price .price-box {
        padding-bottom: 0;
    }
    .order-details-items thead {
        display: block;
    }
    .order-details-items thead tr {
        display: block;
    }
    .order-pager-wrapper {
        display: block;
    }
    .account .table-order-items tbody tr {
        display: block;
    }
    .account .table-order-items .product-item-name {
        display: inline-block;
        margin: 0;
    }
    .account .table-order-items .action.show {
        margin-top: 0;
    }
    .order-details-items .order-title strong {
        display: block;
    }
    .order-details-items .action {
        margin-top: 10px;
    }
    .order-details-items .items-qty {
        display: inline-block;
        vertical-align: top;
    }
    .order-details-items .col.price .price-including-tax,
    .order-details-items .col.subtotal .price-including-tax,
    .order-details-items .col.price .price-excluding-tax,
    .order-details-items .col.subtotal .price-excluding-tax {
        display: inline-block;
    }
    .order-details-items .data.table .col.options {
        padding: 0 10px 15px;
    }
    .order-details-items .data.table .col.options:before {
        display: none;
    }
    .order-details-items .options-label + .item-options-container:before,
    .order-details-items .item-options-container + .item-options-container:before {
        content: attr(data-th) ':';
        display: block;
        font-weight: 700;
        padding-left: 10px;
    }
    .order-details-items .options-label + .item-options-container .col,
    .order-details-items .item-options-container + .item-options-container .col {
        font-size: 1.2rem;
        padding: 0 10px;
    }
    .order-details-items .options-label + .item-options-container .col:first-child,
    .order-details-items .item-options-container + .item-options-container .col:first-child {
        padding-top: 3px;
    }
    .order-details-items .options-label + .item-options-container .col:last-child,
    .order-details-items .item-options-container + .item-options-container .col:last-child {
        padding-bottom: 20px;
    }
    .logo {
        margin-bottom: 13px;
        margin-top: 4px;
    }
}
@media only screen and (max-width: 479px) {
    .tooltip.wrapper .tooltip.content {
        min-width: 10rem;
    }
    .minicart-wrapper .block-minicart {
        width: 290px;
    }
    .modal-popup .field-tooltip .field-tooltip-content {
        width: 200px;
    }
}
@media all and (min-width: 640px) {
    .abs-blocks-2columns-s {
        width: 48%;
    }
    .abs-reset-left-margin-desktop-s,
    .column:not(.sidebar-main) .multicheckout .actions-toolbar,
    .multicheckout .block-shipping .box-shipping-method .fieldset .legend {
        margin-left: 0;
    }
    .abs-visually-hidden-desktop-s,
    .products-grid .product-item-info:not(:hover):not(.active) .product-item-inner {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px;
    }
    .abs-add-clearfix-desktop-s:before,
    .abs-add-clearfix-desktop-s:after,
    .multicheckout .block-billing:before,
    .multicheckout .block-billing:after {
        content: '';
        display: table;
    }
    .abs-add-clearfix-desktop-s:after,
    .multicheckout .block-billing:after {
        clear: both;
    }
    .abs-add-box-sizing-desktop-s,
    .multicheckout .block-shipping .box,
    .multicheckout .block-billing .box-billing-address,
    .multicheckout .block-billing .box-billing-method {
        box-sizing: border-box;
    }
    .abs-form-field-column-2-s .fieldset .field {
        padding: 0 12px 0 0;
        box-sizing: border-box;
        display: inline-block;
        width: 50%;
        vertical-align: top;
    }
    .abs-form-field-column-2-s .fieldset .field + .fieldset {
        clear: both;
    }
    .abs-form-field-column-2-s .fieldset .field .field {
        padding: 0;
        width: 100%;
    }
    .abs-form-field-revert-column-1-s {
        width: 100%;
    }
    .abs-action-print-s {
        display: inline-block;
        text-decoration: none;
    }
    .abs-action-print-s:before {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 16px;
        line-height: 16px;
        color: inherit;
        content: '\e624';
        font-family: 'luma-icons';
        margin: 0 4px 0 0;
        vertical-align: middle;
        display: inline-block;
        font-weight: normal;
        overflow: hidden;
        speak: none;
        text-align: center;
    }
    .table > tbody > tr > th,
    .table > tbody > tr > td {
        border-top: 1px solid #cccccc;
    }
    .table > tbody + tbody {
        border-top: 1px solid #cccccc !important;
    }
    .ui-dialog.popup {
        width: 420px;
    }
    .column .block-addbysku .action.remove {
        background-image: none;
        -moz-box-sizing: content-box;
        box-shadow: none;
        line-height: inherit;
        text-shadow: none;
        font-weight: 400;
        line-height: 1.42857143;
        margin: 0;
        padding: 0;
        color: #1979c3;
        text-decoration: none;
        background: none;
        border: 0;
        display: inline;
        margin-top: 2px;
    }
    .column .block-addbysku .action.remove:focus,
    .column .block-addbysku .action.remove:active {
        background: none;
        border: none;
    }
    .column .block-addbysku .action.remove:hover {
        background: none;
        border: none;
    }
    .column .block-addbysku .action.remove.disabled,
    .column .block-addbysku .action.remove[disabled],
    fieldset[disabled] .column .block-addbysku .action.remove {
        pointer-events: none;
        opacity: 0.5;
    }
    .column .block-addbysku .action.remove:visited {
        color: #1979c3;
        text-decoration: none;
    }
    .column .block-addbysku .action.remove:hover {
        color: #006bb4;
        text-decoration: underline;
    }
    .column .block-addbysku .action.remove:active {
        color: #677752;
        text-decoration: underline;
    }
    .column .block-addbysku .action.remove:hover {
        color: #006bb4;
    }
    .column .block-addbysku .action.remove:hover,
    .column .block-addbysku .action.remove:active,
    .column .block-addbysku .action.remove:focus {
        background: none;
        border: 0;
    }
    .column .block-addbysku .action.remove.disabled,
    .column .block-addbysku .action.remove[disabled],
    fieldset[disabled] .column .block-addbysku .action.remove {
        color: #1979c3;
        opacity: 0.5;
        cursor: default;
        pointer-events: none;
        text-decoration: underline;
    }
    .multicheckout .actions-toolbar .secondary {
        float: none;
        margin-top: 11px;
        text-align: right;
    }
    .multicheckout .actions-toolbar .secondary .action {
        margin-left: 10px;
    }
    .multicheckout .actions-toolbar .secondary .action.back {
        display: block;
        float: left;
    }
    .multicheckout .item-options {
        margin: 20px 0 0;
    }
    .multicheckout .block-content .box {
        margin-bottom: 0;
    }
    .multicheckout .block-shipping .box {
        float: left;
        width: 25%;
    }
    .multicheckout .block-shipping .box-shipping-method {
        padding-left: 25px;
        padding-right: 25px;
        width: 50%;
    }
    .multicheckout .block-shipping .box-shipping-method .fieldset .field:before {
        display: none;
    }
    .multicheckout .block-billing .box-billing-address {
        float: left;
        width: 25%;
    }
    .multicheckout .block-billing .box-billing-method {
        float: left;
        padding-left: 25px;
        width: 50%;
    }
    .multicheckout.form.address .table-wrapper .applicable {
        margin: 7px 0 0;
    }
    .multicheckout.order-review .box-items {
        clear: left;
        float: none;
        padding-top: 40px;
        width: auto;
    }
    .multicheckout.order-review .col.item {
        width: 75%;
    }
    .multicheckout .methods-payment .item-content > .fieldset {
        width: auto;
    }
    .multicheckout .methods-payment .item-content > .fieldset .field.cvv {
        display: inline-block;
        width: auto;
    }
    .multicheckout .methods-payment .fieldset > .field:not(.choice) > .label {
        float: none;
        margin-bottom: 8px;
        text-align: left;
        width: auto;
    }
    .multicheckout .methods-payment .fieldset > .field:not(.choice):not(.cvv) .control {
        width: 100%;
    }
    .review-form {
        max-width: 500px;
    }
    .review-ratings {
        float: left;
        margin-bottom: 0;
        max-width: 240px;
    }
    .review-ratings ~ .review-content,
    .review-ratings ~ .review-details {
        margin-left: 280px;
    }
    .review-toolbar {
        margin: 0 0 30px;
    }
    .review-toolbar .pages {
        padding: 30px 0;
    }
    .fieldset .review-legend.legend {
        margin-bottom: 30px;
    }
    .review-item {
        padding: 30px 0;
    }
    .review-title {
        margin: 0 0 30px;
    }
    .wishlist-index-index .products-grid .product-item {
        margin-bottom: 20px;
    }
    .wishlist-index-index .products-grid .product-item-actions {
        margin: 0;
    }
    .catalogsearch-advanced-result .message.error {
        margin-top: 0;
    }
    .product-item-actions {
        display: block;
    }

    .product-item-actions .actions-primary + .actions-secondary > .action {
        margin-right: 10px;
    }
    .product-item-actions .actions-primary + .actions-secondary > .action:last-child {
        margin-right: 0;
    }
    .product-item-actions .actions-primary {
        display: table-cell;
    }
    .page-products .products-grid .product-item-inner:before {
        background: #ffffff;
        content: '';
        height: 3px;
        left: 0;
        position: absolute;
        top: -2px;
        width: 100%;
        z-index: 1;
    }
    .page-products .product-item-actions {
        position: relative;
        z-index: 1;
    }
    .products-grid .product-item {
        margin-bottom: 20px;
        width: 33.33333333%;
    }
    .page-products .products-grid .product-item,
    .page-layout-1column .products-grid .product-item,
    .page-layout-3columns .products-grid .product-item,
    .page-products.page-layout-1column .products-grid .product-item,
    .page-products.page-layout-3columns .products-grid .product-item {
        width: 33.33333333%;
    }
    .order-details-items .order-title .action {
        margin: 12px 0 0 30px;
    }
    .order-details-items .order-title .action.track {
        float: right;
    }
    .block.widget .products-grid .product-item,
    .page-layout-1column .block.widget .products-grid .product-item,
    .page-layout-3columns .block.widget .products-grid .product-item {
        width: 33.33333333%;
    }
}
/**
 * Owl Carousel v2.1.0
 * Copyright 2013-2016 David Deutsch
 * Licensed under MIT (https://github.com/OwlCarousel2/OwlCarousel2/blob/master/LICENSE)
 */
.owl-carousel,
.owl-carousel .owl-item {
    -webkit-tap-highlight-color: transparent;
    position: relative;
}
.owl-carousel {
    display: none;
    width: 100%;
    z-index: 1;
}
.owl-carousel .owl-stage {
    position: relative;
    -ms-touch-action: pan-Y;
}
.owl-carousel .owl-stage:after {
    content: ".";
    display: block;
    clear: both;
    visibility: hidden;
    line-height: 0;
    height: 0;
}
.owl-carousel .owl-stage-outer {
    position: relative;
    overflow: hidden;
    -webkit-transform: translate3d(0, 0, 0);
}
.owl-carousel .owl-item {
    min-height: 1px;
    float: left;
    -webkit-backface-visibility: hidden;
    -webkit-touch-callout: none;
}
.owl-carousel .owl-item img {
    display: block;
    width: 100%;
    -webkit-transform-style: preserve-3d;
}
.owl-carousel .owl-dots.disabled,
.owl-carousel .owl-nav.disabled {
    display: none;
}
.owl-carousel .owl-dot,
.owl-carousel .owl-nav .owl-next,
.owl-carousel .owl-nav .owl-prev {
    cursor: pointer;
    cursor: hand;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.owl-carousel.owl-loaded {
    display: block;
}
.owl-carousel.owl-loading {
    opacity: 0;
    display: block;
}
.owl-carousel.owl-hidden {
    opacity: 0;
}
.owl-carousel.owl-refresh .owl-item {
    display: none;
}
.owl-carousel.owl-drag .owl-item {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.owl-carousel.owl-grab {
    cursor: move;
    cursor: grab;
}
.owl-carousel.owl-rtl {
    direction: rtl;
}
.owl-carousel.owl-rtl .owl-item {
    float: right;
}
.no-js .owl-carousel {
    display: block;
}
.owl-carousel .animated {
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
}
.owl-carousel .owl-animated-in {
    z-index: 0;
}
.owl-carousel .owl-animated-out {
    z-index: 1;
}
.owl-carousel .fadeOut {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut;
}
@-webkit-keyframes fadeOut {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}
@keyframes fadeOut {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}
.owl-height {
    transition: height 0.5s ease-in-out;
}
.owl-carousel .owl-item .owl-lazy {
    opacity: 0;
    transition: opacity 0.4s ease;
}
.owl-carousel .owl-item img.owl-lazy {
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
}
.owl-carousel .owl-video-wrapper {
    position: relative;
    height: 100%;
    background: #000;
}
.owl-carousel .owl-video-play-icon {
    position: absolute;
    height: 80px;
    width: 80px;
    left: 50%;
    top: 50%;
    margin-left: -40px;
    margin-top: -40px;
    background: url(../../../../../frontend/Gfp/international/default/images/owl.video.play.png) no-repeat;
    cursor: pointer;
    z-index: 1;
    -webkit-backface-visibility: hidden;
    transition: -webkit-transform 0.1s ease;
    transition: transform 0.1s ease;
}
.owl-carousel .owl-video-play-icon:hover {
    -webkit-transform: scale(1.3, 1.3);
    -ms-transform: scale(1.3, 1.3);
    transform: scale(1.3, 1.3);
}
.owl-carousel .owl-video-playing .owl-video-play-icon,
.owl-carousel .owl-video-playing .owl-video-tn {
    display: none;
}
.owl-carousel .owl-video-tn {
    opacity: 0;
    height: 100%;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: contain;
    transition: opacity 0.4s ease;
}
.owl-carousel .owl-video-frame {
    position: relative;
    z-index: 1;
    height: 100%;
    width: 100%;
}
.owl-carousel {
    position: relative;
    padding: 0 36px;
}
.owl-carousel .owl-nav .owl-prev,
.owl-carousel .owl-nav .owl-next {
    background: transparent;
    border: none;
    position: absolute;
    top: 0;
    height: 100%;
    color: #2f3943;
    padding: 0;
    margin: 0;
    width: 50px;
}
.owl-carousel .owl-nav .owl-prev span,
.owl-carousel .owl-nav .owl-next span {
    display: none;
}
.owl-carousel .owl-nav .owl-prev:hover,
.owl-carousel .owl-nav .owl-next:hover {
    background: transparent;
    border: none;
}
.owl-carousel .owl-nav .owl-prev:before,
.owl-carousel .owl-nav .owl-next:before {
    font-family: 'luma-icons';
    border: none;
    display: inline-block;
    margin: 0;
    cursor: pointer;
    text-align: center;
    line-height: 2rem;
    color: inherit;
    font-weight: 400;
    font-size: 2rem;
    transform: rotate(0deg);
}
.owl-carousel .owl-nav .owl-prev::after,
.owl-carousel .owl-nav .owl-next::after {
    content: "";
    margin-left: 0;
}
.owl-carousel .owl-nav .owl-prev {
    left: 0;
}
.owl-carousel .owl-nav .owl-prev:before {
    right: 10px;
    content: "\e617";
}
.owl-carousel .owl-nav .owl-next {
    right: 0;
}
.owl-carousel .owl-nav .owl-next:before {
    left: 10px;
    content: "\e608";
}
.fotorama__thumb-border {
    border: 1px solid #bae588;
}
.fotorama__caption {
    display: none;
}
.xgifts-container {
    text-align: center;
}
.xgifts-container .xgifts-products-list .product-items {
    flex-direction: column;
    width: 100%;
}
.xgifts-container .xgifts-products-list .product-items .product-item-info {
    width: 100%;
    border-top: 1px solid #efefef;
    padding: 20px 0;
}
.xgifts-container .xgifts-products-list .product-items .product-item-details {
    display: flex;
    text-align: left;
}
.xgifts-container .xgifts-products-list .product-items .product-item-details .product-item-image {
    max-width: 100%;
}
.xgifts-container .xgifts-products-list .product-items .actions {
    text-align: center;
    width: 100%;
}
.xgifts-container .xgifts-products-list .product-items .product-item {
    width: 100%;
}
.xgifts-container .xgifts-products-list .product-items .product-item:last-child .product-item-info {
    border-bottom: 1px solid #efefef;
}
.page-header {
    z-index: 100;
    max-width: 100%;
    background: white;
    width: 100%;
    height: auto;
    box-sizing: border-box;
    top: 0;
    transition: 0.4s ease all;
}
.page-header .panel.wrapper {
    background-color: white;
    border-bottom: none;
}
.page-header .header.content {
    padding: 30px 0 0 0;
}
.page-header.pageScrolled {
    box-shadow: 0 0 7px #808080;
}
.page-header.pageScrolled .header.content {
    max-width: 1570px;
    position: static;
    padding-top: 0;
}
.page-header.pageScrolled .header.content .block-search,
.page-header.pageScrolled .header.content #switcher-language,
.page-header.pageScrolled .header.content .logo-name {
    display: none;
}
.page-header.pageScrolled .header.content .logo {
    position: absolute;
    left: 25px;
    top: 10px;
    width: 100px;
    margin: 0;
}
.page-header.pageScrolled .header.content .minicart-wrapper {
    right: 20px;
    z-index: 10001;
    top: 20px;
}
.page-header.pageScrolled .header.content .minicart-wrapper .block-minicart {
    top: 57px;
}
.page-header.pageScrolled .nav-sections .nav-sections-items {
    margin: 17px auto;
    padding-left: 150px;
    padding-right: 75px;
    padding-bottom: 0;
    max-width: 1457px;
    transition: 0.4s ease all;
}
.checkout-index-index .page-header {
    position: static;
    box-shadow: none;
    padding: unset;
}
.checkout-index-index .page-header .header.content {
    position: static;
    padding-top: 0;
    margin: 0 auto;
    max-width: unset;
}
.checkout-index-index .page-header .header.content .block-search,
.checkout-index-index .page-header .header.content #switcher-language,
.checkout-index-index .page-header .header.content .logo-name,
.checkout-index-index .page-header .header.content .minicart-wrapper {
}
.checkout-index-index .page-header .header.content .logo {
    margin-bottom: unset;
    max-width: 250px;
    margin-top: 10px;
}
.page-header .switcher .options.active ul.dropdown {
    z-index: 100001;
}
.page-header .switcher .options .action.toggle::after {
    vertical-align: middle;
}
span.logo-name {
    margin-top: -70px;
    float: left;
    margin-left: 127px;
    font-weight: bold;
}
a.logo::before {
    border: none;
}
#search_mini_form .hidden {
    display: none;
}
header .block-search .action.search.disabled,
header .block-search .block-search .action.search[disabled],
header .block-search fieldset[disabled] .block-search .action.search {
    cursor: pointer;
    pointer-events: inherit;
}
header .block-search label::before {
    display: none !important;
}
header .block-search .control {
    margin-top: -10px;
}
header .block-search .action.search::before {
    color: #2D3843;
    font-size: 2rem;
    line-height: 2.7rem;
    opacity: 1;
    vertical-align: top;
    top: 0;
    right: 0;
}
header .block-search .action.search::after {
    display: none;
}
header .block-search .action.search:hover::before {
    color: #2D3843;
}
header .block-search button::after {
    display: none;
}
header .block-search .toggleSearchInput {
    display: inline-block;
    background-image: none;
    background: none;
    -moz-box-sizing: content-box;
    border: 0;
    box-shadow: none;
    line-height: inherit;
    margin: 0;
    padding: 0;
    text-decoration: none;
    text-shadow: none;
    font-weight: 400;
    position: absolute;
    right: 10px;
    top: 0;
    z-index: 1;
    cursor: pointer;
}
header .block-search .toggleSearchInput::before {
    color: #2D3843;
    font-size: 2rem;
    line-height: 2.8rem;
    opacity: 1;
    -webkit-font-smoothing: antialiased;
    content: '\e615';
    font-family: 'luma-icons';
    margin: 0;
    vertical-align: top;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
    top: 0;
}
header .block-search .toggleSearchInput:hover {
    color: #2D3843;
}
/**
Minicart
 */
.page-header .header.content .minicart-wrapper {
    position: absolute;
    right: 0;
    top: 109px;
    z-index: 10001;
}
.minicart-wrapper .counterHolder {
    width: 40px;
    height: 40px;
    background: black;
    border-radius: 20px;
    display: block;
}
.minicart-wrapper .counterHolder.hasItem {
    background: none;
}
.minicart-wrapper .action.showcart,
.minicart-wrapper .action.showcart.active {
    position: relative;
}
.minicart-wrapper .action.showcart::before,
.minicart-wrapper .action.showcart.active::before {
    content: '\e908';
    color: white;
    top: 6px;
    left: 2px;
    position: absolute;
    font-size: 2.3rem;
}
.minicart-wrapper .action.showcart:hover::before,
.minicart-wrapper .action.showcart.active:hover::before {
    color: white;
}
.minicart-wrapper .action.showcart .counter.qty,
.minicart-wrapper .action.showcart.active .counter.qty {
    border-radius: 12px;
    position: absolute;
    top: -13px;
    right: -9px;
    background: black;
    min-width: 24px;
    box-sizing: border-box;
    height: 24px;
    display: block;
}
.minicart-wrapper a::before {
    border-bottom: none;
}
#switcher-language {
    float: right;
}
#switcher-language .switcher-label {
    display: none;
}
#switcher-language ul li a::before {
    border-bottom: 0;
}
/* Green bar detail page */
.product-view-navigation {
    min-height: 80px;
    background: #bae588;
    display: none;
    box-shadow: 0 3px 4px #808080;
}
.pageScrolled .product-view-navigation {
    display: block;
}
.product-view-navigation .productViewNavigationWrapper {
    max-width: 1570px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    height: 100%;
    min-height: 80px;
}
.product-view-navigation .productViewNavigationWrapper .navigation-title span {
    font-size: 2.5rem;
    font-weight: bold;
}
.product-view-navigation .productViewNavigationWrapper .navigation-title .price-box span {
    margin-left: 5px;
    font-size: 2rem;
    font-weight: normal;
}
.product-view-navigation .productViewNavigationWrapper .navigation-title .price-box .old-price span {
    font-size: 1.7rem;
    margin-left: 10px;
}
.product-view-navigation .productViewNavigationWrapper .navigation-title .price-box .save-info-block .save-info span {
    font-size: 2rem;
}
.product-view-navigation .navigation-title {
    flex: 1 0 auto;
    max-width: calc(100% - 542px);
}
.product-view-navigation .buttonWrapper {
    flex: 0 0 auto;
}
.product-view-navigation .price-box {
    display: inline-block;
}
.checkout-success {
    max-width: 1570px;
    margin: 0 auto;
}
@media (min-width: 768px) {
    .minicart-wrapper .action.showcart .text {
        right: calc(100% + 10px);
        line-height: 4.1rem;
        clip: unset;
        height: auto;
        width: auto;
        font-weight: bold;
        display: block;
    }
    .opc-sidebar, .checkout-index-index .opc-wrapper{
        width: 100%;
    }


}
@media (min-width: 767px) and (max-width: 1200px) {
    html .sections.nav-sections {
    }
    .logo img { width: 70%; }
}
.footer-wrapper {
    display: flex;
    padding: 70px 120px 60px 120px;
    justify-content: space-between;
    position: relative;
}
.footer-wrapper .footer-column {
}
.footer-wrapper .footer-column.links {
}

.footer-wrapper p + p {
    margin: 0;
}
.footer-wrapper .addthis_inline_share_toolbox {
    display: block;
    position: absolute;
    top: -16px;
    text-align: center;
    margin: 0 auto;
    width: 100%;
    left: 0;
}
.footer-wrapper .addthis_inline_share_toolbox a::before {
    border-bottom: 0;
}
.footer.content {
    padding: 0;
}
.footer.content .links strong,
.footer.content .links a {
    color: inherit;
}
.footer.content .links a {
    font-size: 16px;
    margin: 0;
    padding: 0;
}
.footer.content .links a:hover {
    color: #677752;
    text-decoration: none;
}
.footer.content .links li {
    margin: 0;
}
.footer-headline {
    font-size: 20px;
    display: inline-block;
    font-weight: 600;
}
.copyright {
    background: none;
}
.footer.bottom {
    text-align: center;
    padding: 18px 0;
}
.footer.bottom ul {
    display: inline;
    padding: 0;
}
.footer.bottom ul::before {
    content: "-";
    display: inline;
}
.footer.bottom ul li {
    display: inline;
    padding-left: 10px;
}
.footer.bottom ul li a {
}
.footer.bottom ul li a:hover {
    color: #677752;
}
.footer.bottom span {
}
@media (min-width: 768px) {
    .nav-sections {
        background: white;
    }
    .sections .navigation {
        background: white;
        font-size: 2rem;
        font-weight: 400;
    }
    .sections .navigation a::before {
        border-bottom: none;
    }
    .sections .navigation a.ui-state-active::before {
    }
    .sections .navigation a.ui-state-focus::before {
    }
    .sections .navigation a .ui-menu-icon {
        display: none;
    }
    .sections .navigation > ul {
        position: static;
        padding: 0;
    }
    .sections .navigation > ul > li {
        position: static;
    }
    .sections .navigation > ul > li.level-top {
        line-height: 2.6rem;
        display: inline;
    }
    .sections .navigation > ul > li + li {
        padding-left: 10px;
    }
    .sections .navigation > ul > li a {
        text-decoration: none;
    }
    .sections .navigation > ul > li:first-child {

    }
    .sections .navigation li.level0 {
        position: static;
        padding: 0 12px;
    }
    .sections .navigation li.level0 a {
        padding: 0;
    }
    .sections .navigation li.level0 > a.level-top {
        padding: 0;
    }
    .sections .navigation li.level0 > a.level-top .ui-menu-icon {
        display: none;
    }
    .sections .navigation li.level0.active > .level-top,
    .sections .navigation li.level0.has-active > .level-top {
        border: none;
        display: inline;
        text-decoration: none;
    }
    .sections .navigation li.level0.active > .level-top::before,
    .sections .navigation li.level0.has-active > .level-top::before {
        border-bottom: 1px solid #2f3943;
    }
    .sections .navigation li.level0.active > .level-top:hover::before,
    .sections .navigation li.level0.has-active > .level-top:hover::before {
        border-bottom: 1px solid #677752;
    }
    .sections .navigation li.level0 ul.level0.submenu {
        color: inherit;
        border: none;
        border-top: 1px solid #e2e2e2;
        box-shadow: 0 7px 7px -7px #808080;
        /*box-shadow: @gfp-box-shadow;*/
        list-style: none;
        position: absolute;
        width: 100%;
        left: 0 !important;
        top: 47px !important;
        flex-wrap: wrap;
        font-weight: 400;
    }
    .sections .navigation li.level0 ul.level0.submenu .active > a {
        border: none;
    }
    .sections .navigation li.level0 ul.level0.submenu a.ui-state-active::before {
        border-bottom: none;
    }
    .sections .navigation li.level0 ul.level0.submenu a.ui-state-focus::before {
        border-bottom: none;
    }
    .sections .navigation li.level0 ul.level0.submenu a span:not(.ui-icon) {
        position: relative;
    }
    .sections .navigation li.level0 ul.level0.submenu a span:not(.ui-icon)::before {
        display: block;
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 93%;
    }
    .sections .navigation li.level0 ul.level0.submenu a span:not(.ui-icon):hover::before {
    }
    .sections .navigation li.level0 ul.level0.submenu li.level1 {
        max-width: 25%;
        flex: 1 0 25%;
        padding: 0;
        position: relative;
    }
    .sections .navigation li.level0 ul.level0.submenu li.level1::before {
        border-right: 1px solid #e2e2e2;
        display: block;
        content: "";
        position: absolute;
        top: 20px;
        bottom: 0;
        right: 0;
        width: 1px;
        z-index: 10;
    }
    .sections .navigation li.level0 ul.level0.submenu li.level1:nth-child(4)::before {
        border-right: none;
    }
    .sections .navigation li.level0 ul.level0.submenu li.level1 img {
        margin-top: 10px;
        width: 100%;
    }
    .sections .navigation li.level0 ul.level0.submenu li.level1 a.ui-state-active {
    }
    .sections .navigation li.level0 ul.level0.submenu li.level1:hover {
    }
    .sections .navigation li.level0 ul.level0.submenu li.level1:hover ul.submenu {
        background: transparent;
    }
    .sections .navigation li.level0 ul.level0.submenu li.level1 a {
        padding: 20px 10px 10px 10px;
    }
    .sections .navigation li.level0 ul.level0.submenu ul.level1.submenu {
        font-weight: 400;
        left: 0 !important;
        top: 100%;
        position: relative;
        border: none;
        box-shadow: none;
        list-style: none;
        margin: 0;
        line-height: 3rem;
    }
    .sections .navigation li.level0 ul.level0.submenu ul.level1.submenu li {
        margin: 0;
        padding: 0;
        width: 100%;
    }
    .sections .navigation li.level0 ul.level0.submenu ul.level1.submenu li a {
        padding: 0 10px;
    }
    .sections .navigation li.level0 ul.level0.submenu ul.level1.submenu ul.level2.submenu,
    .sections .navigation li.level0 ul.level0.submenu ul.level1.submenu ul.level2.submenu.submenu-reverse {
        top: 0 !important;
        left: 0 !important;
        padding-left: 20px;
        display: block !important;
        position: relative;
        border: none;
        box-shadow: none;
    }
    .sections .navigation li.level0 ul.level0.submenu .submenuWrapper {
        display: flex !important;
        top: auto !important;
        flex-wrap: wrap;
        padding: 0;
        width: 100%;
    }
    .sections .navigation li.level0 ul.level0.submenu > li {
        max-width: 1570px;
        margin: 0 auto;
        width: 100%;
    }
    .sections .navigation a span:hover {
    }
    .nav-sections {
        position: relative;
        margin-bottom: 0;
    }
    .nav-sections .nav-sections-items {
        max-width: 1570px;
        margin: 0 auto;
        padding-bottom: 10px;
    }
    .logo {
        margin: -8px auto 5px 0;
    }
    .nav-sections-item-content > .navigation {
        display: flex;
        position: static;
    }
    /**
  Page service tabs
   */
    .nav-sections-item-content .page-service-tabs {
        position: fixed;
        right: -359px;
        transform: rotateZ(-90deg);
        width: 650px;
        display: flex;
        top: 300px;
        z-index: 999;
    }
    .nav-sections-item-content .page-service-tabs .tabs-container {
        transform: translateY(-60px);
    }
    .nav-sections-item-content .page-service-tabs ul {
        margin-bottom: 0;
    }
    .nav-sections-item-content .page-service-tabs ul li {
        display: inline;
    }
    .nav-sections-item-content .page-service-tabs ul li .servicepanel-button {
        width: auto;
        display: inline-block;
        cursor: pointer;
        text-align: center;
        border: 2px solid #2D3843;
        padding: 13px 20px 13px 20px;
        margin: 5px 3px 5px 0;
        line-height: 2rem;
        text-decoration: none;
        color: white;
        background-color: #2D3843;
        font-weight: 400;
        font-size: 1.8rem;
        transition: all 0.4s;
    }
    .nav-sections-item-content .page-service-tabs ul li .servicepanel-button::after {
        font-family: 'luma-icons';
        content: "\e907";
        border: none;
        display: inline-block;
        margin-left: 5px;
    }
    .nav-sections-item-content .page-service-tabs ul li .servicepanel-button:hover,
    .nav-sections-item-content .page-service-tabs ul li .servicepanel-button:focus {
        background: #677752;
        border: 2px solid #677752;
        color: #FFFFFF;
        text-decoration: none;
    }
    .nav-sections-item-content .page-service-tabs ul li .servicepanel-button::before {
        border: none;
    }
    .nav-sections-item-content .page-service-tabs ul li .servicepanel-button.blue {
        color: white;
        background-color: #318cc5;
        border-color: #318cc5;
    }
    .nav-sections-item-content .page-service-tabs ul li .servicepanel-button.blue::after {
        content: "\e623";
    }
    .nav-sections-item-content .page-service-tabs ul li .servicepanel-button.blue:hover,
    .nav-sections-item-content .page-service-tabs ul li .servicepanel-button.blue:focus {
        opacity: 0.6;
        filter: alpha(opacity=60);
        /* For IE8 and earlier */
    }
}
.xcategory {
    margin: 0 auto;
    background: #f5f5f5;
}
.xcategory .xtags-container-wrapper {
    background: transparent;
    padding: 0;
}
.xcategory .xcategoryWrapper {
    padding: 20px;
    max-width: 1600px;
    margin: 0 auto;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}
.xcategory .xcategoryWrapper .containerLeft,
.xcategory .xcategoryWrapper .containerRight,
.xcategory .xcategoryWrapper .containerBelow {
    display: flex;
}
.xcategory .xcategoryWrapper img {
    display: block;
}
.xcategory .xcategoryWrapper .backgroundHolder {
    background: linear-gradient(90deg, rgba(0, 0, 0, 0.5), transparent);
    display: block;
    height: 100%;
    width: 60%;
    position: absolute;
}
.xcategory .xcategoryWrapper .containerLeft {
    flex: 0 0 65.5%;
}
.xcategory .xcategoryWrapper .containerLeft .category-item {
    flex: 0 0 100%;
}
.xcategory .xcategoryWrapper .containerLeft .category-item .xtags-container {
    position: absolute;
    bottom: 0;
    right: 20px;
}
.xcategory .xcategoryWrapper .containerLeft .category-item .xtags-container .icon {
    z-index: 10;
}
.xcategory .xcategoryWrapper .containerRight .xtags-container,
.xcategory .xcategoryWrapper .containerBelow .xtags-container {
    position: absolute;
    top: 0;
    left: 20px;
    right: auto;
}
.xcategory .xcategoryWrapper .containerRight .xtags-container .icon,
.xcategory .xcategoryWrapper .containerBelow .xtags-container .icon {
    z-index: 10;
}
.xcategory .xcategoryWrapper .containerRight {
    flex: 0 0 33%;
    flex-direction: column;
    justify-content: space-between;
}
.xcategory .xcategoryWrapper .containerRight .category-item {
    overflow: hidden;
    flex: 0 0 auto;
    max-height: 50%;
}
.xcategory .xcategoryWrapper .containerBelow {
    margin-top: 13px;
    flex: 1 0 100%;
    justify-content: space-between;
    flex-wrap: wrap;
}
.xcategory .xcategoryWrapper .containerBelow .category-item {
    flex: 0 0 32%;
}
.xcategory .xcategoryWrapper .containerBelow .category-item:nth-child(3) {
    flex: 0 0 33%;
}
.xcategory .xcategoryWrapper .category-item {
    position: relative;
}
.xcategory .xcategoryWrapper .category-item .textWrapper {
    position: absolute;
    bottom: 20px;
    left: 20px;
    word-break: break-all;
}
.xcategory .xcategoryWrapper .category-item .textWrapper h4,
.xcategory .xcategoryWrapper .category-item .textWrapper h3 {
    color: white;
}
.xcategory .xcategoryWrapper .category-item .textWrapper .sub-cat-title {
    font-size: 1.8rem;
    margin: 0;
}
.xcategory .xcategoryWrapper .category-item .textWrapper .cat-title {
    margin: 0 0 30px 0;
    font-size: 4rem;
}
.xcategory .xcategoryWrapper .category-item .categoryWrapper {
    display: inline-block;
    max-height: 100%;
    overflow: hidden;
    position: relative;
}
.xcategory .xcategoryWrapper .category-item .categoryWrapper::before {
    border-bottom: none;
}
.xcategory .xcategoryWrapper .category-item.bigImage {
    grid-column-start: 1;
    grid-column-end: 3;
    grid-row-start: 1;
    grid-row-end: 3;
}
.xcategory .xcategoryWrapper .category-item .xtags-container {
    position: absolute;
}
.header.content,
.page.messages {
    max-width: 1570px;
    margin: 0 auto;
}
.page-wrapper {
    display: block;
}
.page-main {
    padding: 0;
}
.modal-fullsize .modal-inner-wrap {
    width: 100%;
    height: 100%;
    margin: 0;
}
.panel.header {
    display: none;
    margin: 0;
}
.block-static-block.widget,
.block-cms-link.widget {
    margin: 0;
}
.block-static-block.widget .block-container,
.block-cms-link.widget .block-container {
    max-width: 1570px;
    margin: 0 auto;
}
.block-static-block.wide .block-container {
    margin: auto;
    max-width: none;
}
.block-static-block.highlight {
    padding: 50px 0;
    background: #bae588;
}
/**
  Breadcrumb
 */
.breadcrumbs {
    background: #f5f5f5;
    margin: 0;
}
.breadcrumbs ul.items {
    max-width: 1570px;
    margin: 0 auto;
    font-size: 1.4rem;
    padding: 8px 0;
}
.breadcrumbs a:hover {
    color: #677752;
    text-decoration: none;
}
div[data-shopname='gfp-international.com'] a::before {
    border-bottom: none;
}
.columns {
    display: block;
}
.columns .column.main {
    padding-bottom: 0;
}
body:not(.page-layout-custom-full-width) #maincontent .category-view-cms {
    max-width: 1570px;
    margin: 0 auto;
}
#maincontent .category-view-cms .page-title-wrapper {
    position: static;
    margin-top: 10px;
    padding: 10px 20px;
}
#maincontent .category-view-cms .category-description {
    background: transparent;
    padding: 20px;
}
@media (min-width: 992px) {
    .col-md-6 {
        width: 50%;
        float: left;
        position: relative;
        min-height: 1px;
        padding-right: 15px;
        padding-left: 15px;
    }
}
#notice-cookie-block {
    box-shadow: 0 5px 5px 5px rgba(0,0,0,0.1);
}
#notice-cookie-block .content {
    max-width: 1570px;
    margin: 0 auto;
}
#callback-form .labelProt {
    font-size: 1.3rem;
    line-height: 13px;
    padding-left: 35px;
}
div.fourcolumns {
    position: relative;
    overflow: hidden;
    padding: 1%;
}
div.fourcolumns .column-first,
div.fourcolumns .column-second,
div.fourcolumns .column-third,
div.fourcolumns .column-fourth {
    float: left;
    padding: 1%;
}
div.fourcolumns .column-first,
div.fourcolumns .column-third {
    width: 23%;
}
div.fourcolumns .column-second,
div.fourcolumns .column-fourth {
    width: 23%;
}
.top-quality-banner {
    max-width: 1570px;
    margin: 0 auto;
    display: flex;
    position: relative;
}
.top-quality-banner .portrait-col {
    position: relative;
}
.top-quality-banner .portrait-col blockquote {
    position: absolute;
    top: 62%;
    color: white;
    text-align: center;
    left: 82px;
    width: 200px;
    font-style: normal;
}
.top-quality-banner .portrait-col img {
    display: block;
}
.top-quality-banner .advantages-col {
    z-index: 1;
}
.top-quality-banner .advantages-col .textWrapper {
    padding-top: 128px;
}
.top-quality-banner .advantages-col .textWrapper h3 {
    margin: 0;
    text-align: center;
}
.top-quality-banner .advantages-col .textWrapper .small-headline {
    text-align: center;
}
.top-quality-banner .xtags-container-wrapper {
    position: absolute;
    left: 501px;
    top: 192px;
}
.top-quality-banner .xtags-container {
    position: relative;
}
.top-quality-banner .xtags-container h3 {
    margin: 0;
    text-align: center;
}
.top-quality-banner .xtags-container .xtagsItemWrapper {
    display: flex;
    text-align: center;
    justify-content: center;
    height: 150px;
    align-items: center;
}
.top-quality-banner .xtags-container .xtagsItemWrapper .xtags-tag {
    flex: 0 0 300px;
}
.top-quality-banner .xtags-container .xtagsItemWrapper .icon {
    display: inline-block;
    text-align: center;
    width: 100%;
}
.top-quality-banner .xtags-container .xtagsItemWrapper .icon::before {
    border-right: none;
    border-left: 1px solid #efefef;
    left: 0;
    top: -20px;
    height: 143px;
}
.top-quality-banner .xtags-container .xtagsItemWrapper .icon img {
    display: block;
    margin: 0 auto 10px auto;
}
.banner-container .banner-bg-image {
    background-size: cover !important;
}
.banner-inner-container {
    max-width: 1570px;
    margin: 0 auto;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.banner-inner-container .banner-tags {
    margin-right: 30px;
}
.banner-inner-container .banner-content h3 {
    font-size: 2rem;
    margin: 0;
}
.banner-inner-container .banner-content h2 {
    margin-top: 0;
}
.banner-inner-container .xtags-container-wrapper {
    background: transparent;
    padding: 0;
}
.xtags-container-wrapper {
    padding: 20px 0;
}
.xtags-container {
    max-width: 1570px;
    margin: 0 auto;
}
.xtags-container h3 {
    text-align: center;
}
.xtags-container .xtagsItemWrapper {
    display: flex;
    height: 175px;
    justify-content: center;
    align-items: center;
}
.xtags-container .xtagsItemWrapper .xtags-tag {
    flex: 1 0 auto;
    max-width: 200px;
}
.xtags-container .xtagsItemWrapper .xtags-tag:last-child .icon::before {
    border-right: none;
}
.xtags-container .xtagsItemWrapper .icon {
    position: relative;
    text-align: center;
}
.xtags-container .xtagsItemWrapper .icon::before {
    content: "";
    display: block;
    border-right: 1px solid #efefef;
    height: 175px;
    position: absolute;
    top: -33px;
    padding: 0;
    right: 0;
}
.xtags-container .xtagsItemWrapper img {
    margin: 0 auto 10px auto;
    display: block;
}
/**
Category list
 */
.category-view {
    /**
    Subcategories
   */
    /* Description */
    /* category cms */
}
.category-view .category-view-image-container {
    position: relative;
}
.category-view .category-view-image-container .category-image {
}
.category-view .category-view-image-container .page-title-wrapper {

}
.category-view .category-view-image-container .page-title-wrapper h1,
.category-view .category-view-image-container .page-title-wrapper h2 {
    margin: 0;
}
.category-view .category-view-image-container .page-title-wrapper #page-title-heading {
    order: 2;
}
.category-view .category-view-image-container .page-title-wrapper .category-sub-name {
    order: 1;
}
.category-view .xtags-container {
    position: absolute;
    left: 30px;
    top: 30px;
}
.category-view .page-title-wrapper {
    z-index: 999;
}
.category-view .category-image .image {
    width: 100%;
}
.category-view .category-description-container {
}
.category-view .category-description {
    margin-top: 20px;
}
.category-view .categories.wrapper {
}
.category-view .category-description {
    max-width: 1570px;
    margin: 0 auto;
    padding: 30px 0;
}
.category-view .category-description table h3 {
    margin-bottom: 0;
}
.category-view .category-cms {

}
.category-view .category-cms .xtags-container {
    position: static;
}
/* Subcategories List */
/**
  Subcategories
 */
.categories.wrapper {
    margin: 0 auto 0 auto;
    z-index: 1;
    position: relative;
    max-width: 1570px;
}
.categories.wrapper .category-overview-info {
    margin-bottom: 30px;
}
.categories.wrapper .category-overview-info .category-overview-label {
    font-size: 2.6rem;
    font-weight: bold;
    margin-right: 15px;
}
.categories.wrapper ol.categories {
    display: flex;
    flex-wrap: wrap;
    margin-left: -10px;
    margin-right: -10px;
}
.categories.wrapper ol.categories .category-item {
    flex: 0 0 auto;
    margin: 0 10px 20px;
    width: calc(25% - 20px);
    padding-bottom: 20px;
    transition: transform 0.1s;
}
.categories.wrapper ol.categories .category-item:hover {
    box-shadow: 0 0 7px #808080;
    transform: scale(1.05);
}
.categories.wrapper ol.categories .category-item .category-image {
    position: relative;
}
.categories.wrapper ol.categories .category-item.noPicture {
    padding-bottom: 0;
}
.categories.wrapper ol.categories .category-item.noPicture .category-image {
    min-height: 200px;
    background: grey;
    margin-bottom: 0;
}
.categories.wrapper ol.categories .category-item.noPicture .category-item-description {
    padding: 0;
}
.categories.wrapper ol.categories .category-item .category-item-description {
    padding: 10px;
}
.categories.wrapper ol.categories .product-item-info-wrapper {
    position: relative;
}
.categories.wrapper ol.categories .product-item-info-wrapper .category-name-actions {

}
.categories.wrapper ol.categories .product-item-info-wrapper .category-name-actions .category-item-link {
}
.categories.wrapper ol.categories .product-item-info-wrapper .category-name-actions .category-item-link::before {
    border-bottom: none;
}
.categories.wrapper ol.categories .priceDiv {
}
/* image subcontainer */
.catalog-product-view .product-item-info,
.page-products .product-item-info {
    width: 100%;
}
/* overwrite button */
#maincontent .category-view a.button {
    padding: 13px 25px 13px 31px;
    margin: 0;
}
.category-image {
    position: relative;
}
.category-image .backgroundHolder {
    background: linear-gradient(90deg, rgba(0, 0, 0, 0.5), transparent);
    display: block;
    height: 100%;
    width: 60%;
    position: absolute;
}
/** Product list **/
.category-select-container {
    max-width: 1570px;
    margin: 0 auto 20px auto;
}
.category-select-container select {
    display: inline-block;
    border: none;
    padding-left: 15px;
}
.category-select-container h3 {
    margin: 0 20px 0 0;
    display: inline-block;
    font-size: 28px;
    font-weight: 400;
    vertical-align: middle;
}
.page-products.page-layout-1column .products-grid,
.block.related .products-grid,
.column.main .products-grid,
.block.upsell .products-grid {
    max-width: 1570px;
    margin: 0 auto;
}
.page-products.page-layout-1column .products-grid .product-items,
.block.related .products-grid .product-items,
.column.main .products-grid .product-items,
.block.upsell .products-grid .product-items {
    margin-left: -10px;
    margin-right: -10px;
    display: flex;
    flex-wrap: wrap;
}
.page-products.page-layout-1column .products-grid .product-items .product-item,
.block.related .products-grid .product-items .product-item,
.column.main .products-grid .product-items .product-item,
.block.upsell .products-grid .product-items .product-item {
    position: relative;
    padding: 0;
    flex: 0 0 auto;
    margin: 0 10px 20px;
    width: calc(25% - 20px);
    background: #FFF;
}
.page-products.page-layout-1column .products-grid .product-items .product-item:hover,
.block.related .products-grid .product-items .product-item:hover,
.column.main .products-grid .product-items .product-item:hover,
.block.upsell .products-grid .product-items .product-item:hover {
    box-shadow: 0 0 7px #808080;
    transform: scale(1.05);
    transition: all 0.4s;
}
.page-products.page-layout-1column .products-grid .product-items .product-item a::before,
.block.related .products-grid .product-items .product-item a::before,
.column.main .products-grid .product-items .product-item a::before,
.block.upsell .products-grid .product-items .product-item a::before {
    border: none;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .product-item-info,
.block.related .products-grid .product-items .product-item .product-item-info,
.column.main .products-grid .product-items .product-item .product-item-info,
.block.upsell .products-grid .product-items .product-item .product-item-info {
    width: auto;
    height: 100%;
    display: flex;
    flex-direction: column;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .product-item-info:hover,
.block.related .products-grid .product-items .product-item .product-item-info:hover,
.column.main .products-grid .product-items .product-item .product-item-info:hover,
.block.upsell .products-grid .product-items .product-item .product-item-info:hover {
    box-shadow: none;
    margin: 0;
    border: none;
    padding: 0;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .product-item-info .product-item-image,
.block.related .products-grid .product-items .product-item .product-item-info .product-item-image,
.column.main .products-grid .product-items .product-item .product-item-info .product-item-image,
.block.upsell .products-grid .product-items .product-item .product-item-info .product-item-image,
.page-products.page-layout-1column .products-grid .product-items .product-item .product-item-info .product-item-details,
.block.related .products-grid .product-items .product-item .product-item-info .product-item-details,
.column.main .products-grid .product-items .product-item .product-item-info .product-item-details,
.block.upsell .products-grid .product-items .product-item .product-item-info .product-item-details {
    flex: 0 0 auto;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .product-item-info .product-item-price,
.block.related .products-grid .product-items .product-item .product-item-info .product-item-price,
.column.main .products-grid .product-items .product-item .product-item-info .product-item-price,
.block.upsell .products-grid .product-items .product-item .product-item-info .product-item-price {
    flex: 1 0 auto;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper,
.block.related .products-grid .product-items .product-item .imageWrapper,
.column.main .products-grid .product-items .product-item .imageWrapper,
.block.upsell .products-grid .product-items .product-item .imageWrapper {
    position: relative;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-image-container,
.block.related .products-grid .product-items .product-item .imageWrapper .product-image-container,
.column.main .products-grid .product-items .product-item .imageWrapper .product-image-container,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-image-container {
    display: inline;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .backgroundHolder,
.block.related .products-grid .product-items .product-item .imageWrapper .backgroundHolder,
.column.main .products-grid .product-items .product-item .imageWrapper .backgroundHolder,
.block.upsell .products-grid .product-items .product-item .imageWrapper .backgroundHolder {
    background: linear-gradient(90deg, rgba(0, 0, 0, 0.5), transparent);
    display: block;
    height: 100%;
    width: 90%;
    position: absolute;
    z-index: 9;
    pointer-events: none;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name {
    position: absolute;
    bottom: 0;
    z-index: 10;
    padding: 10px 20px;
    font-size: 2.4rem;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .product-item-link,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .product-item-link,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .product-item-link,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .product-item-link {
    margin: 0;
    color: white;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .product-item-link .save-info-block,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .product-item-link .save-info-block,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .product-item-link .save-info-block,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .product-item-link .save-info-block {
    display: none !important;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box {
    color: #FFF;
    margin: 0;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .save-info-block,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .save-info-block,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .save-info-block,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .save-info-block,
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .minimal-price-link,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .minimal-price-link,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .minimal-price-link,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .minimal-price-link {
    display: none !important;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .price,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .price,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .price,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .price {
    font-size: 2.4rem;
    font-weight: 400;
    color: #FFF;
    display: inline;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .price-label,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .price-label,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .price-label,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .price-label {
    color: #FFF;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .normal-price .price-label,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .normal-price .price-label,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .normal-price .price-label,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .normal-price .price-label,
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .special-price .price-label,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .special-price .price-label,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .special-price .price-label,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .special-price .price-label {
    display: none;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .normal-price,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .normal-price,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .normal-price,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .normal-price,
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .special-price,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .special-price,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .special-price,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .special-price,
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price {
    color: #FFF;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .normal-price .price,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .normal-price .price,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .normal-price .price,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .normal-price .price,
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .special-price .price,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .special-price .price,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .special-price .price,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .special-price .price,
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price {
    display: inline;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price-final_price::before,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price-final_price::before,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price-final_price::before,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price-final_price::before {
    content: "/";
    display: inline-block;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price-label,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price-label,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price-label,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price-label {
    display: none;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price {
    font-size: 1.8rem;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .product-item-button,
.block.related .products-grid .product-items .product-item .product-item-button,
.column.main .products-grid .product-items .product-item .product-item-button,
.block.upsell .products-grid .product-items .product-item .product-item-button {
    text-align: center;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .product-item-price,
.block.related .products-grid .product-items .product-item .product-item-price,
.column.main .products-grid .product-items .product-item .product-item-price,
.block.upsell .products-grid .product-items .product-item .product-item-price {
    margin: 10px 10px 10px 0;
    text-align: center;
    padding: 10px;
    border-top: 1px solid #f5f5f5;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .stock-status,
.block.related .products-grid .product-items .product-item .stock-status,
.column.main .products-grid .product-items .product-item .stock-status,
.block.upsell .products-grid .product-items .product-item .stock-status {
    margin: 10px 20px;
    text-align: center;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .product-item-description,
.block.related .products-grid .product-items .product-item .product-item-description,
.column.main .products-grid .product-items .product-item .product-item-description,
.block.upsell .products-grid .product-items .product-item .product-item-description {
    margin: 20px 0 0 0;
    padding: 10px;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .xtags-container-wrapper,
.block.related .products-grid .product-items .product-item .xtags-container-wrapper,
.column.main .products-grid .product-items .product-item .xtags-container-wrapper,
.block.upsell .products-grid .product-items .product-item .xtags-container-wrapper {
    padding: 0;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .xtags-container,
.block.related .products-grid .product-items .product-item .xtags-container,
.column.main .products-grid .product-items .product-item .xtags-container,
.block.upsell .products-grid .product-items .product-item .xtags-container {
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 10;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .xtags-container .xtagsItemWrapper,
.block.related .products-grid .product-items .product-item .xtags-container .xtagsItemWrapper,
.column.main .products-grid .product-items .product-item .xtags-container .xtagsItemWrapper,
.block.upsell .products-grid .product-items .product-item .xtags-container .xtagsItemWrapper {
    height: auto;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .product-item-actions,
.block.related .products-grid .product-items .product-item .product-item-actions,
.column.main .products-grid .product-items .product-item .product-item-actions,
.block.upsell .products-grid .product-items .product-item .product-item-actions {
    margin: 0;
}
.page-products.page-layout-1column .products-grid .swatch-attribute,
.block.related .products-grid .swatch-attribute,
.column.main .products-grid .swatch-attribute,
.block.upsell .products-grid .swatch-attribute {
    padding: 0 15px;
}
.page-products.page-layout-1column .price-box,
.block.related .price-box,
.column.main .price-box,
.block.upsell .price-box {
    margin: 0;
}
.page-products.page-layout-1column .price-box .price,
.block.related .price-box .price,
.column.main .price-box .price,
.block.upsell .price-box .price {
    font-weight: 400;
    font-size: 1.8rem;
}
.page-products.page-layout-1column .price-box .normal-price .price-label,
.block.related .price-box .normal-price .price-label,
.column.main .price-box .normal-price .price-label,
.block.upsell .price-box .normal-price .price-label,
.page-products.page-layout-1column .price-box .special-price .price-label,
.block.related .price-box .special-price .price-label,
.column.main .price-box .special-price .price-label,
.block.upsell .price-box .special-price .price-label {
    display: none;
}
.page-products.page-layout-1column .price-box .normal-price,
.block.related .price-box .normal-price,
.column.main .price-box .normal-price,
.block.upsell .price-box .normal-price,
.page-products.page-layout-1column .price-box .special-price,
.block.related .price-box .special-price,
.column.main .price-box .special-price,
.block.upsell .price-box .special-price,
.page-products.page-layout-1column .price-box .old-price,
.block.related .price-box .old-price,
.column.main .price-box .old-price,
.block.upsell .price-box .old-price {
    display: block;
    width: 100%;
    margin: 0;
}
.page-products.page-layout-1column .price-box .special-price .price,
.block.related .price-box .special-price .price,
.column.main .price-box .special-price .price,
.block.upsell .price-box .special-price .price,
.page-products.page-layout-1column .price-box .normal-price .price,
.block.related .price-box .normal-price .price,
.column.main .price-box .normal-price .price,
.block.upsell .price-box .normal-price .price {
    font-weight: 400;
    font-size: 3.4rem;
}
.page-products.page-layout-1column .products-list .product-items,
.block.related .products-list .product-items,
.column.main .products-list .product-items,
.block.upsell .products-list .product-items {
    margin: 30px 0;
}
.page-products.page-layout-1column .products-list .product-item,
.block.related .products-list .product-item,
.column.main .products-list .product-item,
.block.upsell .products-list .product-item {
    margin: 10px 0;
    padding: 10px 0;
    border-bottom: 1px solid #f5f5f5;
}
.page-products.page-layout-1column .products-list .product-item-info,
.block.related .products-list .product-item-info,
.column.main .products-list .product-item-info,
.block.upsell .products-list .product-item-info {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-image,
.block.related .products-list .product-item-info .product-item-image,
.column.main .products-list .product-item-info .product-item-image,
.block.upsell .products-list .product-item-info .product-item-image,
.page-products.page-layout-1column .products-list .product-item-info .product-item-details,
.block.related .products-list .product-item-info .product-item-details,
.column.main .products-list .product-item-info .product-item-details,
.block.upsell .products-list .product-item-info .product-item-details,
.page-products.page-layout-1column .products-list .product-item-info .product-item-action-container,
.block.related .products-list .product-item-info .product-item-action-container,
.column.main .products-list .product-item-info .product-item-action-container,
.block.upsell .products-list .product-item-info .product-item-action-container,
.page-products.page-layout-1column .products-list .product-item-info .product-item-choice,
.block.related .products-list .product-item-info .product-item-choice,
.column.main .products-list .product-item-info .product-item-choice,
.block.upsell .products-list .product-item-info .product-item-choice {
    flex: 0 1 auto;
    text-align: left;
    box-sizing: border-box;
    width: auto;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-image,
.block.related .products-list .product-item-info .product-item-image,
.column.main .products-list .product-item-info .product-item-image,
.block.upsell .products-list .product-item-info .product-item-image {
    text-align: center;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-details,
.block.related .products-list .product-item-info .product-item-details,
.column.main .products-list .product-item-info .product-item-details,
.block.upsell .products-list .product-item-info .product-item-details {
    flex-grow: 1;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-details .product-item-name,
.block.related .products-list .product-item-info .product-item-details .product-item-name,
.column.main .products-list .product-item-info .product-item-details .product-item-name,
.block.upsell .products-list .product-item-info .product-item-details .product-item-name {
    margin: 0 0 10px 0;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-details .product-list-description-content,
.block.related .products-list .product-item-info .product-item-details .product-list-description-content,
.column.main .products-list .product-item-info .product-item-details .product-list-description-content,
.block.upsell .products-list .product-item-info .product-item-details .product-list-description-content {
    margin: 15px 0;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-action-container,
.block.related .products-list .product-item-info .product-item-action-container,
.column.main .products-list .product-item-info .product-item-action-container,
.block.upsell .products-list .product-item-info .product-item-action-container {
    text-align: right;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-action-container .product-item-actions,
.block.related .products-list .product-item-info .product-item-action-container .product-item-actions,
.column.main .products-list .product-item-info .product-item-action-container .product-item-actions,
.block.upsell .products-list .product-item-info .product-item-action-container .product-item-actions {
    display: block;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary,
.block.related .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary,
.column.main .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary,
.block.upsell .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary {
    display: block;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart,
.block.related .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart,
.column.main .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart,
.block.upsell .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart {
    margin: -20px 0 0 0;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .fieldset,
.block.related .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .fieldset,
.column.main .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .fieldset,
.block.upsell .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .fieldset {
    display: table;
    width: 100%;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .field,
.block.related .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .field,
.column.main .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .field,
.block.upsell .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .field,
.page-products.page-layout-1column .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .actions,
.block.related .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .actions,
.column.main .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .actions,
.block.upsell .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .actions {
    display: table-cell;
    vertical-align: bottom;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .field,
.block.related .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .field,
.column.main .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .field,
.block.upsell .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .field {
    width: 25%;
    padding: 0 5% 0 0;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .actions,
.block.related .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .actions,
.column.main .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .actions,
.block.upsell .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .actions {
    width: 70%;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .actions .action.tocart,
.block.related .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .actions .action.tocart,
.column.main .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .actions .action.tocart,
.block.upsell .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .actions .action.tocart {
    margin: 0;
    width: 100%;
    height: 50px;
    padding: 12px 17px;
}
#maincontent .product-item-description a.button {
    margin-left: 17px;
}
#maincontent .catalog-related-product-container .action.primary.tocart {
    margin: 0;
    width: 100%;
}
.message.info {
    margin: 0 auto;
    max-width: 1570px;
}
.page-products #maincontent {
    background: #f5f5f5;
}
.page-products.page-layout-1column .products-grid {
    margin-bottom: 50px;
}
.block.related .field.choice .label {
    height: auto;
    width: auto;
}
.block.related .price-box {
    margin: 0;
}
.block.related .title strong {
    font-size: 2rem;
}
.block.related .title strong a {
    font-weight: bold;
}
.block.related .field.choice {
    position: relative;
}
.block.related .field.choice .label {
    padding-left: 35px;
    clip: auto;
}
.block.related .product-item-photo {
    padding: 0;
}
.catalog-related-product-container .block.related a.action.close {
    float: right;
}
.catalog-related-product-container .block.related a.action.close::before {
    border-bottom: none;
}
.catalog-related-product-container .block.related a.action.close::after {
    font-size: 1.4rem;
    line-height: 2rem;
    content: '\e616';
    font-family: 'luma-icons';
    margin: 0;
    vertical-align: top;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
    color: red;
    padding-left: 20px;
}
.catalog-related-product-container .block.related .product-item-info.related-available .product-item-actions .actions-primary {
    display: block;
}
.catalog-related-product-container .block.related .product-item-info.related-available .tocart {
    white-space: normal;
    margin: 0;
}
.cart-container .block.crosssell .products-grid .product-items .product-item {
    width: calc(33.33333333% - 20px);
}
@media (min-width: 767px) {
    .page-products.page-layout-1column .products-list .product-item-info,
    .block.related .products-list .product-item-info,
    .column.main .products-list .product-item-info,
    .block.upsell .products-list .product-item-info {
        flex-direction: row;
    }
    .page-products.page-layout-1column .products-list .product-item-info .product-item-image,
    .block.related .products-list .product-item-info .product-item-image,
    .column.main .products-list .product-item-info .product-item-image,
    .block.upsell .products-list .product-item-info .product-item-image {
        text-align: left;
        flex-basis: 25%;
    }
    .page-products.page-layout-1column .products-list .product-item-info .product-item-details,
    .block.related .products-list .product-item-info .product-item-details,
    .column.main .products-list .product-item-info .product-item-details,
    .block.upsell .products-list .product-item-info .product-item-details {
        flex-basis: 40%;
        padding: 0 20px;
    }
    .page-products.page-layout-1column .products-list .product-item-info .product-item-action-container,
    .block.related .products-list .product-item-info .product-item-action-container,
    .column.main .products-list .product-item-info .product-item-action-container,
    .block.upsell .products-list .product-item-info .product-item-action-container {
        align-self: baseline;
        flex-basis: 25%;
    }
}
.swatch-option {
    border: 1px solid #fff;
}
.swatch-option.image:not(.disabled):hover,
.swatch-option.color:not(.disabled):hover {
    outline: 2px solid #677752;
    border: 1px solid #fff;
}
.swatch-option.selected {
    outline: 2px solid #2d3843;
}
.product-info-container {
    background: #f5f5f5;
}
.product-info-wrapper {
    max-width: 1570px;
    margin: 0 auto;
}
.product-tabs-wrapper {
    padding-bottom: 70px;
}
.product-category-list-container {
    padding: 100px 0;
    background: #f5f5f5;
}
.product-reviews-container {
    padding: 100px 0;
}
.product-reviews-container .product-reviews-slider-container {
    max-width: 772px;
    margin: 0 auto;
    text-align: center;
}
.page-layout-1column {
    /* Description */
}
.page-layout-1column .product.media {
    width: calc(50% - 40px);
    position: relative;
}
.page-layout-1column .product-info-main {
    width: 50%;
    margin-left: 40px;
}
.page-layout-1column .product-info-main .attribute.subname {
    font-weight: 500;
    margin-bottom: 15px;
    font-size: 16px;
}
.page-layout-1column .product-info-main .product-info-stock-sku {
    text-align: left;
    display: block;
    padding: 0;
}
.page-layout-1column .product-info-main .product-info-overview-container,
.page-layout-1column .product-info-main .product-info-cmsblock-container {

}
.page-layout-1column .product-info-main .product-info-overview-container ul,
.page-layout-1column .product-info-main .product-info-cmsblock-container ul {
    padding-left: 22px;
}
.page-layout-1column .product-info-main .product-info-price-top .old-price .price-final_price::before {
    content: "/";
    display: inline-block;
}
.page-layout-1column .product-info-main .product-info-price-container {
    margin-top: 20px;
}
.page-layout-1column .product-info-main .product-info-price-container .product-info-price {
    margin: 0;
    border-bottom: 0;
}
.page-layout-1column .product-info-main .product-info-price-container .product-info-price .price-box {
    padding-bottom: 0;
}
.page-layout-1column .product-info-main .product-info-price-container .product-info-price .normal-price,
.page-layout-1column .product-info-main .product-info-price-container .product-info-price .special-price {
    display: block;
}
.page-layout-1column .product-info-main .product-info-price-container .product-info-price .normal-price .now-only-label,
.page-layout-1column .product-info-main .product-info-price-container .product-info-price .special-price .now-only-label {
    font-size: 3.4rem;
}
.page-layout-1column .product-info-main .product-info-price-container .product-info-price .normal-price .price,
.page-layout-1column .product-info-main .product-info-price-container .product-info-price .special-price .price {
    color: #677752;
}
.page-layout-1column .product-info-main .product-info-price-container .product-info-price .normal-price .price-label,
.page-layout-1column .product-info-main .product-info-price-container .product-info-price .special-price .price-label {
    display: none;
}
.page-layout-1column .product-info-main .product-info-price-container .product-info-price .old-price .price-container {
    font-size: 1.8rem;
}
.page-layout-1column .product-info-main .product-info-price-container .product-info-price .old-price .price-container .price {
    font-weight: 400;
    font-size: 1.8rem;
}
.page-layout-1column .product-info-main .product-info-price-container .product-info-price .save-info-block,
.page-layout-1column .product-info-main .product-info-price-container .product-info-price .save-info-block .price,
.page-layout-1column .product-related-price.product-item-price .save-info-block,
.page-layout-1column .product-related-price.product-item-price .save-info-block .price
{
    color: #85b84b;
    font-size: 17px;
    font-weight: 600;
}

.page-layout-1column .product-info-main .product-info-price-container .price-box .price-container .price {
    font-weight: 400;
    font-size: 3.4rem;
}
/* Fotorama custom changes */
.fotorama__stage__frame .fotorama__img {
    width: 100%;
}
.fotorama--fullscreen {
    z-index: 10001 !important;
}
.fotorama__arr {
    background-color: transparent;
}
.fotorama__arr:hover {
    background-color: transparent;
}
#sb-nav,
#sb-info,
#sb-info-inner {
    height: 50px;
}
#sb-wrapper-inner {
    border: 2px solid #f5f5f5;
}
#sb-nav-next {
    background-image: none;
}
#sb-nav-next::before {
    border-right: #ffffff 2px solid;
    border-bottom: #ffffff 2px solid;
    -webkit-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    transform: rotate(-45deg);
    content: none;
    width: 22px;
    height: 22px;
    display: block;
    position: absolute;
    top: 10px;
}
#sb-nav-previous {
    background-image: none;
}
#sb-nav-previous::before {
    border-left: #ffffff 2px solid;
    border-bottom: #ffffff 2px solid;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
    content: none;
    width: 22px;
    height: 22px;
    display: block;
    position: absolute;
    top: 10px;
}
#sb-nav-close {
    background-image: none;
}
#sb-nav-close::before {
    transform: rotate(45deg);
    border-bottom: none;
}
#sb-nav-close::after {
    transform: rotate(-45deg);
}
#sb-nav-close::before,
#sb-nav-close::after {
    content: none;
    position: absolute;
    height: 2px;
    width: 100%;
    top: 23px;
    left: 0;
    margin-top: -1px;
    background: white;
}
#sb-nav a {
    width: 55px;
    height: 65px;
}
.gallery-placeholder {
    margin-bottom: 20px;
}
#maincontent .product.media .button.action,
#maincontent .product.media button.action {
    display: block;
    padding: 13px 31px 13px 25px;
    border-radius: 0;
    transition: all 0.4s;
}
#maincontent .product.media .button.action.to-faq::before,
#maincontent .product.media button.action.to-faq::before {
    content: "\e623";
}
#maincontent .product.media .button.action.to-online-catalogue::before,
#maincontent .product.media button.action.to-online-catalogue::before {
    content: "\e907";
}
#maincontent .product.media .button.action.to-assembly-hint::before,
#maincontent .product.media button.action.to-assembly-hint::before {
    content: "\e90c";
}
#maincontent .product.media .button.action.to-overview::before,
#maincontent .product.media button.action.to-overview::before {
    content: "\e90f";
}
#maincontent .product.media .button.action.to-datasheet::before,
#maincontent .product.media button.action.to-datasheet::before {
    content: "\e907";
}
#maincontent .product.media .button.action {
    display: inline-block;
}
#maincontent .product-service-container {
    margin-top: -35px;
    clear: both;
}
#maincontent .product-service-container .service-container {
    display: flex;
    max-width: 1570px;
    margin: 0 auto;
    position: relative;
}
#maincontent .product-service-container .service-container .info-col {
    position: relative;
}
#maincontent .product-service-container .service-container .info-col blockquote {
    position: absolute;
    font-style: normal;
    top: 62%;
    color: white;
    text-align: center;
    left: 115px;
    width: 200px;
    margin: 0;
}
#maincontent .product-service-container .service-container .info-col img {
    display: block;
}
#maincontent .product-service-container .service-container .serviceFormWrapper {
    position: absolute;
    left: 400px;
    top: 140px;
    display: flex;
    align-items: center;
    z-index: 10;
}
#maincontent .product-service-container .service-container .serviceFormWrapper .col {
    position: relative;
    padding: 20px;
}
#maincontent .product-service-container .service-container .serviceFormWrapper .col::before {
    border-left: 1px solid #efefef;
    left: -20px;
    top: -83px;
    height: 400px;
    display: block;
    content: "";
    position: absolute;
}
#maincontent .product-service-container .service-container .serviceFormWrapper .form-datasheet-col {
    margin-right: 20px;
}
#maincontent .product-service-container .service-container .serviceFormWrapper .form-catalog-col {
    margin-left: 20px;
}
#maincontent .product-service-container .service-container form fieldset {
    margin-bottom: 10px;
}
#maincontent .product-service-container .service-container form legend h3 {
    margin: 0;
    position: relative;
    font-size: 2.4rem;
}
#maincontent .product-service-container .service-container form legend h3::before {
    border: none;
    display: block;
    margin-right: 10px;
    text-align: center;
    line-height: 2rem;
    color: black;
    font-weight: 400;
    font-size: 2rem;
    transform: rotate(0deg);
    background-image: url('../images/datasheet-form.svg');
    background-repeat: no-repeat;
    background-size: 70px 70px;
    content: "";
    width: 70px;
    height: 70px;
    position: absolute;
    top: -80px;
    left: 0;
}
#maincontent .product-service-container .service-container form#catalog-form legend h3::before {
    background-image: url('../images/catalog-form.svg');
}
#maincontent .product-service-container .callback-container {
    border-top: 1px solid #f5f5f5;
    background: none;
}
#maincontent .product-service-container .callback-container .callback-form-container {
    width: 680px;
    margin: 0 auto;
    padding-left: 180px;
}
#maincontent .product-service-container .callback-container .callback-form-container fieldset {
    position: relative;
    margin-bottom: 0;
    padding-bottom: 35px;
}
#maincontent .product-service-container .callback-container .callback-form-container fieldset::before {
    border: none;
    display: block;
    margin-right: 10px;
    text-align: center;
    line-height: 2rem;
    color: black;
    font-weight: 400;
    font-size: 2rem;
    transform: rotate(0deg);
    background-image: url('../images/rueckrufservice.png');
    background-repeat: no-repeat;
    background-size: 150px 150px;
    content: "";
    width: 150px;
    height: 150px;
    position: absolute;
    top: 61px;
    left: -180px;
}
#maincontent .product-service-container .callback-container .callback-form-container fieldset h3 {
    margin-bottom: 0;
    font-size: 2.4rem;
}
#maincontent .product-service-container .callback-container .callback-form-container fieldset legend {
    margin: 0;
    border-bottom: 0;
}
#maincontent .product-service-container .callback-container .callback-form-container fieldset .control #phone {
    width: 280px;
    display: inline-block;
}
#maincontent .product-service-container .callback-container .callback-form-container fieldset .phoneFieldButtonWrapper {
    display: flex;
}
#maincontent .product-service-container .callback-container .callback-form-container fieldset .phoneFieldButtonWrapper button {
    margin: 0 0 0 10px;
}
#maincontent .product-service-container .callback-container .callback-form-container fieldset div.field {
    margin: 0 0 10px 0;
}
#maincontent .product-add-form {
    padding-top: 30px;
}
#maincontent .product-add-form .box-tocart .fieldset {
    display: inline-block;
}
#maincontent .product-add-form .box-tocart .fieldset .field.qty,
#maincontent .product-add-form .box-tocart .fieldset .actions {
    float: left;
    display: inline-block;
}
#maincontent .product-add-form .box-tocart .fieldset button.tocart {
    margin: 0 0 0 40px;
    width: 100%;
}
.productOrderButtonWrapper {
    max-width: 1570px;
    margin: 20px auto 20px auto;
    text-align: center;
}

.product.data.items .section-header {
    font-weight: 400;
    color: #fff;
    padding: 5px 15px;
    background: #6f7d81;
    z-index: 3;
}

.product.info.detailed {
    max-width: 1570px;
    margin: -50px auto 0 auto;
}
.product.info.detailed .product.data.items .item.title > .switch {
    border: none;
    text-align: center;
}
.product.info.detailed .product.data.items .item.title > .switch::before {
    border: none;
}
.product.info.detailed .product.data.items .item.title.active > .switch {
    background-color: white;
}
.product.info.detailed .product.data.items .item.title + .item.content:not(.product-info-details-tab-details) {
    display: none;
}
.product.info.detailed .product.data.items .item.title.active + .item.content {
    display: block;
}
.product.info.detailed .product.data.items .item.content {
    border: 0;
}
.product.info.detailed .col1,
.product.info.detailed .col2 {
    width: 50%;
    float: left;
}
.product.info.detailed .product-info-details-tab-details-description {
    clear: both;
}

.product.info.detailed iframe {
    max-width: 100%;
}
.old-price span.price {
    text-decoration: line-through;
}
.product-info-accordion {
    display: none;
}
#maincontent .block.upsell {
    background: none;
    margin-bottom: 0;
}
#maincontent .block.upsell .products {
    margin-top: 0;
    margin-bottom: 0;
}
#maincontent .block.upsell .products .product-item-info {
    background: #f5f5f5;
}
#maincontent .block.upsell .block-title,
.review-list .block-title {
    padding-bottom: 5px;
    margin-bottom: 20px;
    width: 100%;
    border-bottom: 1px solid #f5f5f5;
}
#maincontent .block.upsell .block-title strong,
.review-list .block-title strong {
    font-weight: 700;
    font-size: 2.5rem;
    line-height: 3rem;
}
.review-list .review-items .review-item {
    border-bottom: 2px solid #FFF;
    background: #f5f5f5;
    padding: 15px;
}
.review-list .review-items .review-item .review-title {
    margin-bottom: 15px;
}
.review-list .review-toolbar {
    border-top: 1px solid #f5f5f5;
}
.catalog-related-product-container {
    padding: 20px 0;
}
#category-overview-modal .product-category-list-container {
    padding: 0;
    background: none;
}
#category-overview-modal .product-category-list-container .categories.wrapper ol.categories .category-item {
    width: calc(50% - 20px);
    padding-bottom: 0;
}
#category-overview-modal .product-category-list-container .categories.wrapper ol.categories .category-item .category-image {
    margin-bottom: 0;
}
#category-overview-modal .product-category-list-container .categories.wrapper ol.categories .category-item .category-image img {
    width: 100%;
}
#category-overview-modal .product-category-list-container .categories.wrapper ol.categories .category-item .category-item-description {
    display: none;
}
.product-options-wrapper select {
    background-image: none;
}
.addToCartSuccessPopupContainer {
    margin-top: 0;
}
.prices-tier {
    margin: 20px 0;
}
.prices-tier .item {
    margin-bottom: 0;
}
.prices-tier .item .price-container .price {
    font-size: inherit;
}
.ui-accordion {
    position: relative;
    background: #F5F5F5;
}
.ui-accordion .ui-accordion-header {
    max-width: 1570px;
    margin: 0 auto;
    cursor: pointer;
    font-size: 2rem;
    line-height: 2.4rem;
    font-weight: 700;
    position: relative;
    border: none;
    padding: 23px 15px 23px 51px;
    text-decoration: none;
    display: flex;
}
.ui-accordion .ui-accordion-header .ui-accordion-header-icon {
    order: 2;
}
.ui-accordion .ui-accordion-header .ui-accordion-header-icon::after {
    content: "";
    display: inline-block;
    /* By using an em scale, the arrows will size with the font */
    width: 0.4em;
    height: 0.4em;
    border-right: 0.1em solid black;
    border-top: 0.1em solid black;
    right: 18px;
    transform: rotate(135deg);
    -ms-transform: rotate(135deg);
    /* IE 9 */
    -webkit-transform: rotate(135deg);
    /* Chrome, Safari, Opera */
    top: 14pt;
    margin-left: 10px;
}
.ui-accordion .ui-accordion-header.ui-state-active {
    height: auto;
    margin-bottom: 0;
}
.ui-accordion .ui-accordion-header.ui-state-active .ui-accordion-header-icon::after {
    top: 20pt;
    transform: rotate(-45deg);
}
.ui-accordion .ui-accordion-header:focus {
    outline: 0;
}
.ui-accordion .ui-accordion-header:last-child {
    margin-bottom: 0;
}
.ui-accordion .ui-accordion-header ~ .ui-accordion-header {
    border-top: 1px solid #E2E2E2;
}
.ui-accordion .ui-accordion-header::before {
    font-family: 'luma-icons';
    border: none;
    display: inline-block;
    margin-right: 10px;
    cursor: pointer;
    text-align: center;
    color: black;
    font-weight: 400;
    font-size: 2rem;
    line-height: 2rem;
    transform: rotate(0deg);
}
.ui-accordion .ui-accordion-header.product-gallery-title::before {
    content: "\e90b";
}
.ui-accordion .ui-accordion-header.product-faq-title::before {
    content: "\e623";
}
.ui-accordion .accordion-content {
    max-width: 1570px;
    margin: 0 auto;
    padding-bottom: 40px;
}
.ui-accordion .accordion-content a::before {
    border-bottom: none;
}
.ui-accordion .accordion-content .tab-content {
    border: none;
}
.owl-carousel .owl-item {
    padding-right: 10px;
}
.opc-progress-bar {
    padding-top: 0;
    padding-bottom: 0;
}
.opc-progress-bar .opc-progress-bar-item {

}
.opc-progress-bar .opc-progress-bar-item::before {
}
.opc-progress-bar .opc-progress-bar-item span {
    padding-top: 0;
}
.opc-progress-bar .opc-progress-bar-item span::before,
.opc-progress-bar .opc-progress-bar-item span::after {
    display: none;
}
.opc-progress-bar .opc-progress-bar-item._active span {
    color: #677752;
}
.opc-progress-bar .opc-progress-bar-item._complete span {

}
.product-item-photo::before {
    border-bottom: none;
}
.product-item-photo:hover::before {
    border-bottom: none;
}
.cart.table-wrapper .col.price .price-excluding-tax,
.cart.table-wrapper .col.subtotal .price-excluding-tax {
    color: #2f3943;
    font-weight: 400;
}
.cart.table-wrapper .col.price .price-excluding-tax .price {
    font-weight: 400;
}
.cart.table-wrapper .col.subtotal .price-excluding-tax .price {
    font-weight: 400;
}
.product-item-name a {
    font-weight: 700;
}
.product-item-name a:hover {
    text-decoration: none;
    color: #85b84b;
}
.product-item-name a::before {
    border-bottom: none;
}
.checkout-cart-index .page-main {
    padding: 0;
}
.checkout-cart-index .block.crosssell {
    float: none;
    width: 100%;
    max-width: 1570px;
    margin: 0 auto;
    padding: 40px 0;
}
.checkout-cart-index .block.crosssell .title strong {
    font-size: 2rem;
    line-height: 2rem;
}
#maincontent .cart-container-wrapper .cart-container {
    margin: 0 auto;
    max-width: 1570px;
}
#maincontent .cart-container-wrapper .product-image-container {
    max-width: 100px;
}
#maincontent .cart-container-wrapper #empty_cart_button {
    display: none;
}
#maincontent .cart-container-wrapper .cart-summary {
    background: white;
}

#maincontent .cart-container-wrapper .cart-summary .summary.title:after {
    content: "";
}
#maincontent .cart-container-wrapper .cart-summary .block.discount {
    margin-top: 10px;
    padding-top: 10px;
}
#maincontent .cart-container-wrapper .cart-summary .block.discount .label {
    display: none;
}
#maincontent .cart-container-wrapper .cart-summary .block.discount .actions-toolbar .primary {
    float: none;
    width: 100%;
}
#maincontent .cart-container-wrapper .cart-summary .block.discount .actions-toolbar .primary .action.primary {
    margin: 0;
    width: 100%;
}
#maincontent .cart-container-wrapper .cart-summary .cart-summary-info-container h4 {
    margin: 12px 0;
    font-size: 2rem;
}
#maincontent .cart-container-wrapper .cart-discount {
    float: none;
}
#maincontent .cart-container-wrapper .cart-discount #block-discount {
    float: none;
}
#maincontent .cart-container-wrapper .cart-discount .block > .title strong {
    font-size: 1.8rem;
    color: #2f3943;
}
#maincontent .cart-container-wrapper .cart-discount button {
    margin: 0;
}
#maincontent .cart-container-wrapper .cart-discount #coupon_code {
    width: auto;
}
#maincontent .cart-container-wrapper .product-item-details {
    padding: 10px;
}
.customer-account-create .page-title-wrapper,
.customer-account-index .page-title-wrapper,
.customer-address-form .page-title-wrapper,
.customer-account-forgotpassword .page-title-wrapper,
.customer-account-edit .page-title-wrapper,
.customer-account-create .column.main,
.customer-account-index .column.main,
.customer-address-form .column.main,
.customer-account-forgotpassword .column.main,
.customer-account-edit .column.main {
    margin: 0 auto;
}
.customer-account-index .column.main,
.customer-address-form .column.main,
.checkout-index-index .column.main,
.checkout-cart-index .column.main,
.customer-account-edit .column.main {
    float: none;
    background: #f5f5f5;
}
.color {
    color: inherit;
}
.opc-block-summary {
    background: white;
}
.checkout-index-index #checkout {
    max-width: 1570px;
    margin: 0 auto;
    padding: 0;
}
.checkout-index-index .opc-sidebar button.action {
    min-height: auto;
    background: none;
    border: none;
    padding: 0;
    margin: 0;
    text-align: right;
    position: absolute;
    right: 0;
}
.checkout-index-index .opc-sidebar button.action > span {
    display: inline-block;
    display: none;
}
.checkout-index-index .opc-sidebar button.action:hover {
    background: none;
    border: none;
    color: #85b84b;
}
.checkout-index-index .opc-sidebar button.action:hover:before {
    color: inherit;
    border: none;
}
.checkout-index-index .opc-sidebar button.action:after {
    content: "";
    margin-left: 0;
}
.checkout-index-index .opc-sidebar button.action:before {
    font-family: 'luma-icons';
    display: inline-block;
    cursor: pointer;
    text-align: center;
    color: inherit;
    border: none;
    transition: all 0.4s;
    width: auto;
    position: relative;
    bottom: auto;
    left: 0;
}
.checkout-index-index .opc-sidebar button.action.action-edit:before {
    font-size: 1.8rem;
    line-height: 2rem;
    content: '\e601';
}
.checkout-index-index .opc-sidebar .shipping-information-title,
.checkout-index-index .opc-sidebar .delivery-information-title {
    margin: 0 0 5px;
}
.checkout-index-index .opc-sidebar .delivery-information-title > span {
    font-size: 18px;
    font-weight: 500;
}
.checkout-index-index .opc-sidebar .opc-block-shipping-information .ship-to,
.checkout-index-index .opc-sidebar .opc-block-shipping-information .ship-via,
.checkout-index-index .opc-sidebar .opc-block-delivery-information,
.checkout-index-index .opc-sidebar .opc-block-info {
    background: #FFF;
    margin: 0;
    padding: 22px 30px;
}
.checkout-index-index .opc-sidebar .opc-block-shipping-information .ship-to p + p,
.checkout-index-index .opc-sidebar .opc-block-shipping-information .ship-via p + p,
.checkout-index-index .opc-sidebar .opc-block-delivery-information p + p,
.checkout-index-index .opc-sidebar .opc-block-info p + p {
    margin: 0;
}
.checkout-index-index .billing-address-same-as-shipping-block {
    margin: 10px;
}
.checkout-index-index .checkout-shipping-method .step-title,
.checkout-index-index .checkout-shipping-method .table-checkout-shipping-method {
    display: none;
}
.checkout-payment-method .payment-option-title .action-toggle {
    color: #2f3943;
}
.checkout-payment-method .payment-option-content .actions-toolbar .primary {
    float: none;
}
#maincontent #checkout .authentication-wrapper button {
    display: none;
}
.cart-empty {
    padding-bottom: 20px;
}
.billing-info .billing-info-cms-block {
    font-size: 11px;
    font-style: italic;
    line-height: 14px;
    margin-bottom: 10px;
}
#payment #cardexpiremonth iframe,
#payment #cardexpireyear iframe {
    height: 30px;
}
.customer-account-createpassword #maincontent,
.catalogsearch-result-index #maincontent,
.cms-page-view #maincontent,
body[class*=' customer-account-'] #maincontent,
.customer-account-create #maincontent,
.htmlsitemap-index-index #maincontent,
.checkout-onepage-success #maincontent,
.customer-account-login #maincontent,
.contact-index-index #maincontent,
.cms-no-route #maincontent,
.customer-account-forgotpassword #maincontent,
.catalog-product-compare-index #maincontent
{
    max-width: 1570px;
    margin: 0 auto;
}
.customer-account-login .actions-toolbar > .secondary {
    margin-left: 20px;
}
#payolution_instalment_dob_day,
#payolution_elv_dob_day {
    padding-right: 10px;
}
form fieldset#customer-email-fieldset .field .note {
    display: none !important;
}
.checkout-agreements-block {
    margin: 20px 0;
}
.checkout-agreements-block .checkout-agreement button {
    color: #000;
}
.checkout-agreements-block .checkout-agreement button:hover {
    text-decoration: none;
    color: #000;
}
.checkout-agreements-block .checkout-agreement button::after {
    display: none;
}
.checkout-info-block,
.cart-summary-info-cms-block {
    margin: 0 0 20px;
}
.checkout-info-block .image-container,
.cart-summary-info-cms-block .image-container {
    text-align: left;
}
.checkout-info-block .image-container.image-andreas-berger,
.cart-summary-info-cms-block .image-container.image-andreas-berger {
    margin-top: -30px;
    margin-right: -20px;
    margin-bottom: -20px;
}
.checkout-info-block p + p,
.cart-summary-info-cms-block p + p {
    margin: 0;
}
.checkout-info-block h4,
.cart-summary-info-cms-block h4 {
    margin: 0 0 20px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #ccc;
    font-size: 18px;
    font-weight: 500;
}
.message.global.cookie {
    background: white;
    font-size: 15px;
    z-index: 100000;
}
.message.global.cookie a {
    color: #2f3943;
}
.message.global.cookie a:hover {
    color: #85b84b;
}
.xfolder-items,
.modal-popup .xfolder-items {
    list-style: none;
    padding: 0;
}
@media (max-width: 767px) {
    .logo {
        display: table;
        margin: auto;
        float: none;
    }
    span.logo-name {
        font-size: 1.6rem;
        font-weight: 400;
        margin: 5px 0 0 -10px;
    }
    .page-header .header.content {
        padding-top: 0;
    }
    .page-header .header.content .service-wrapper-container {
        position: relative;
        z-index: 150;
    }
    .page-header .header.content .service-wrapper-container #switcher-language {
        padding: 10px;
    }
    .page-header .header.content .service-wrapper-container .block-search {
        margin-top: 0;
        background: #f5f5f5;
        width: 100%;
        padding: 0;
    }
    .page-header .header.content .service-wrapper-container .block-search form .field.search input {
        padding: 10px;
    }
    .page-header .header.content #switcher-language {
        position: relative;
        float: left;
    }
    .page-header .header.content .block.search {
        float: right;
        margin-top: 0;
    }
    .page-header .header.content .minicart-wrapper {
        right: 10px;
        top: -5px;
        z-index: inherit;
    }
    .page-header .header.content .minicart-wrapper .counterHolder {
        background: transparent;
    }
    .page-header .header.content .minicart-wrapper .action.showcart::before {
        color: #2f3943;
    }
    .page-header.pageScrolled .header.content .block-search,
    .page-header.pageScrolled .header.content #switcher-language,
    .page-header.pageScrolled .header.content .logo-name {
        display: inline-block;
    }
    .page-header.pageScrolled .header.content .logo {
        position: static;
    }
    .page-header.pageScrolled .header.content .minicart-wrapper {
        right: 10px;
        top: -5px;
        z-index: inherit;
    }
    header .block-search .toggleSearchInput {
        right: 50px;
        top: 10px;
    }
    .nav-toggle {
        right: 17px;
        left: auto;
        top: 63px;
    }
    .block-search .action.search::before {
        font-size: 1.6rem;
        line-height: 3.2rem;
        color: #8f8f8f;
        content: '\e615';
        font-family: 'luma-icons';
        margin: 0;
        vertical-align: top;
        display: inline-block;
        font-weight: normal;
        overflow: hidden;
        text-align: center;
    }
    header .block-search .action.search::before {
        color: #2D3843;
        font-size: 2rem;
        line-height: 2.7rem;
        opacity: 1;
        top: 11px;
        right: 40px;
        position: absolute;
    }
    .action.search[disabled] {
        cursor: not-allowed;
        pointer-events: none;
        opacity: 0.5;
    }
    .block-search .action.search {
        display: inline-block;
        background: none;
        -moz-box-sizing: content-box;
        border: 0;
        box-shadow: none;
        line-height: inherit;
        margin: 0;
        padding: 0;
        text-decoration: none;
        text-shadow: none;
        font-weight: 400;
        position: absolute;
        right: 10px;
        top: 0;
        z-index: 1;
    }
    .block-search .action.search > span {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px;
    }
    .block-search .control {
        border-top: none;
    }
}
@media (max-width: 767px) {
    .block-static-block.widget .block-container {
        padding: 0 20px;
    }
}
@media (max-width: 767px) {
    .navigation {
        background: white;
    }
    .navigation a::before {
        border-bottom: none;
    }
    .navigation ul li.all-category {
        display: none;
    }
    .navigation .submenuWrapper {
        display: block !important;
        padding: 0;
    }
    .navigation a {
        padding: 10px;
    }
    .navigation .submenu:not(:first-child) ul {
        padding-left: 0;
    }
    .navigation .submenu:not(:first-child) .active > a {
        border-color: #85b84b;
    }
    .navigation .level0.active > a:not(.ui-state-active) {
        border-color: #85b84b;
    }
    .navigation .level0.has-active > a:not(.ui-state-active) {
        border-color: #85b84b;
    }
    .nav-sections-item-title {
        display: none;
    }
    .nav-sections-item-content {
        margin-top: 0;
        margin-left: 0;
        padding: 0;
    }
    .nav-sections-item-content#store\.quicklinks {
        display: block !important;
    }
    .nav-sections-item-content#store\.quicklinks .page-service-tabs ul {
        list-style: none;
        padding-left: 0;
    }
    .nav-sections-item-content#store\.quicklinks .page-service-tabs ul li {
        padding: 8px 15px;
        margin-bottom: 0;
    }
    .nav-sections-item-content#store\.quicklinks .page-service-tabs ul li.servicePanelFolder {
        background: #318cc5;
    }
    .nav-sections-item-content#store\.quicklinks .page-service-tabs ul li.servicePanelProduct {
        background: #2D3843;
    }
    .nav-sections-item-content#store\.quicklinks .page-service-tabs ul li a {
        color: white;
        display: block;
    }
    .nav-sections-item-content#store\.quicklinks .page-service-tabs ul li a::before {
        border-bottom: none;
    }
    .nav-open .page-wrapper {
        left: auto;
    }
    .nav-toggle {
        position: absolute;
    }
    .nav-open .nav-sections {
        width: 100%;
        background: transparent;
        box-shadow: none;
    }
    .nav-open .nav-toggle::after {
        display: none;
    }
    .nav-sections-items {
        padding-bottom: 20px;
    }
    .nav-open .page-header.pageScrolled .nav-sections .nav-sections-items {
        padding: 0;
    }
}
@media (max-width: 767px) {
    .xcategory .xcategoryWrapper {
        padding: 0;
        display: block;
    }
    .xcategory .xcategoryWrapper .containerLeft,
    .xcategory .xcategoryWrapper .containerRight,
    .xcategory .xcategoryWrapper .containerBelow {
        display: block;
        flex: 0 0 100%;
        width: 100%;
        max-height: 100%;
    }
    .xcategory .xcategoryWrapper .containerLeft .category-item,
    .xcategory .xcategoryWrapper .containerRight .category-item,
    .xcategory .xcategoryWrapper .containerBelow .category-item {
        width: 100%;
        max-height: 100%;
    }
    .xcategory .xcategoryWrapper .containerLeft .category-item .categoryWrapper img,
    .xcategory .xcategoryWrapper .containerRight .category-item .categoryWrapper img,
    .xcategory .xcategoryWrapper .containerBelow .category-item .categoryWrapper img {
        width: 100%;
    }
    .xcategory .xcategoryWrapper .containerRight .category-item .xtags-container,
    .xcategory .xcategoryWrapper .containerBelow .category-item .xtags-container {
        left: auto;
        right: 20px;
        top: 20px;
    }
    .xcategory .xcategoryWrapper .category-item .categoryWrapper {
        display: block;
    }
    .xcategory .xcategoryWrapper .category-item .xtags-container .xtags-tag {
        padding: 0;
        max-width: 80px;
    }
    .xcategory .xcategoryWrapper .containerBelow {
        margin-top: 0;
    }
    .xcategory .xcategoryWrapper .category-item {
        max-height: none;
    }
}
@media (max-width: 767px) {
    .top-quality-banner {
        flex-direction: column;
    }
    .top-quality-banner .portrait-col {
        flex: 1 0 100%;
        order: 2;
    }
    .top-quality-banner .portrait-col blockquote {
        left: 0;
        margin: 0;
        padding: 0 20%;
        top: 210px;
        width: 100%;
        box-sizing: border-box;
    }
    .top-quality-banner .advantages-col {
        order: 1;
    }
    .top-quality-banner .advantages-col .textWrapper {
        padding: 50px 20px 0 20px;
    }
    .top-quality-banner .advantages-col .textWrapper h3 {
        margin-bottom: 20px;
    }
    .top-quality-banner .xtags-container-wrapper {
        order: 3;
        position: relative;
        left: auto;
        top: auto;
    }
    .top-quality-banner .xtags-container h3 {
        margin-top: 10px;
    }
    .top-quality-banner .xtags-container .xtagsItemWrapper {
        flex-direction: column;
        height: auto;
    }
    .top-quality-banner .xtags-container .xtagsItemWrapper .xtags-tag {
        max-width: none;
        width: 100%;
        padding: 20px 0;
        flex: 0 0 auto;
    }
    .top-quality-banner .xtags-container .xtagsItemWrapper .xtags-tag + div {
        border-top: 1px solid #e4e4e4;
    }
    .top-quality-banner .xtags-container .xtagsItemWrapper .icon::before {
        display: none;
    }
}
@media (max-width: 767px) {
    .banner-inner-container {
        flex-direction: column;
        height: auto;
    }
    .banner-inner-container .banner-tags {
        margin-top: 50px;
        margin-right: 0;
        margin-bottom: 35px;
    }
    .banner-inner-container .banner-content {
        padding: 20px;
        text-align: center;
        margin-bottom: 50px;
    }
}
@media (max-width: 767px) {
    .xtags-container-wrapper {
        padding: 20px 0;
    }
    .xtags-container h3 {
        padding: 0 20px;
    }
    .xtags-container .xtagsItemWrapper {
        flex-direction: column;
        height: auto;
    }
    .xtags-container .xtagsItemWrapper .xtags-tag {
        max-width: none;
        width: 100%;
        padding: 20px 0;
    }
    .xtags-container .xtagsItemWrapper .xtags-tag + div {
        border-top: 1px solid #e4e4e4;
    }
    .xtags-container .xtagsItemWrapper .icon::before {
        display: none;
    }
}
@media (max-width: 767px) {
    .page-footer { background: #fff; padding-bottom: 30px; }
    .page-footer .footer.content {
        border-top: none;
    }
    .page-footer .footer-wrapper {
        flex-direction: column;
        padding: 0;
        background: #fff;
    }
    .page-footer .footer-wrapper .footer-column {
        padding: 30px 20px;
        text-align: center;
    }
    .page-footer .footer-wrapper .footer-column.links::after {
        display: none;
    }
    .page-footer .footer-wrapper .footer-column ul li a {
        display: inline-block;
    }
}
@media (max-width: 767px) {
    /**
    Category list
   */
    .category-view {
        /* category cms */
    }
    .category-view h2 b {
        word-break: break-word;
    }
    .category-view .category-view-image-container {
        padding: 0;
        display: block;
    }
    .category-view .category-view-image-container .category-image {
        max-width: 100%;
    }
    .category-view .category-view-image-container .page-title-wrapper {
        padding: 20px;
        text-align: center;
    }
    .category-view .category-view-image-container .xtags-container-wrapper {
        padding: 0;
    }
    .category-view .category-view-image-container .xtags-container-wrapper .xtags-container {
        position: absolute;
        top: auto;
        bottom: 0;
        left: auto;
        right: 20px;
    }
    .category-view .category-view-image-container .xtags-container-wrapper .xtags-container .xtagsItemWrapper .xtags-tag {
        max-width: 80px;
    }
    .category-view .category-view-image-container .xtags-container-wrapper .xtags-container .icon {
        z-index: 10;
    }
    .category-view .category-description-container .category-description {
        padding: 20px;
        box-sizing: border-box;
        width: 100%;
    }
    .category-view .category-description table,
    .category-view .category-description tbody,
    .category-view .category-description table tr,
    .category-view .category-description table td {
        display: block;
    }
    .category-view .category-cms {

    }
    .categories.wrapper {
        margin: 0;
    }
    .categories.wrapper ol.categories {
        flex-direction: column;
        margin: 0;
    }
    .categories.wrapper ol.categories .category-item {
        width: 100%;
        margin: 10px 0;
    }
    .categories.wrapper ol.categories .category-item .category-image .image {
        width: 100%;
    }
    .categories.wrapper ol.categories .category-item .product-item-info-wrapper .category-name-actions {
        width: 100%;
        text-align: center;
    }
    .categories.wrapper ol.categories .category-item:hover {
        box-shadow: none;
        transform: none;
    }
}
@media (max-width: 767px) {
    /**
  product list
   */
    .page-products.page-layout-1column .products-grid .product-items {
        margin: 0;
        flex-direction: column;
    }
    .page-products.page-layout-1column .products-grid .product-items .product-item {
        width: 100%;
        margin: 0;
    }
    .page-products.page-layout-1column .products-grid .product-items .product-item img {
        width: 100%;
    }
    .page-products.page-layout-1column .products-grid .product-items .product-item:hover {
        transition: none;
        box-shadow: none;
        transform: none;
    }
    .page-products .columns {
        padding-top: 0;
    }
    .category-select-container {
        padding: 0 20px;
    }
    .products-grid {
        margin-bottom: 0;
    }
    .products-grid .category-image {
        max-width: 100%;
    }
    .products-grid .product-item .xtags-container-wrapper {
        padding: 0;
    }
    .products-grid .product-item .xtags-container-wrapper .xtags-container {
        position: absolute;
        top: 0;
        left: auto;
        right: 20px;
    }
    .products-grid .product-item .xtags-container-wrapper .xtags-container .xtagsItemWrapper .xtags-tag {
        padding: 0;
        max-width: 80px;
    }
    .products-grid .product-item .xtags-container-wrapper .xtags-container .icon {
        z-index: 10;
    }
    .products-grid .category-description table,
    .products-grid .category-description table tr,
    .products-grid .category-description table td {
        display: block;
    }
    /** Product list **/
    .category-select-container select {
        font-size: 1.6rem;
    }
    #product.info.details.tab.related .block.related .products-list .product-item-info.related-available {
        display: block;
    }
    #product.info.details.tab.related .block.related .products-list .product-item-info.related-available div.product {
        display: block;
    }
    #product.info.details.tab.related .block.related .products-list .product-item-info.related-available div.product-item-image {
        text-align: center;
    }
    #product.info.details.tab.related .block.related .products-list .product-item-info.related-available .product-item-details {
        padding: 0;
    }
    #product.info.details.tab.related .block.related .products-list .product-item-info.related-available .product-item-choice {
        margin-bottom: 50px;
    }
}
@media (max-width: 767px) {
    .product-view-navigation,
    .pageScrolled .product-view-navigation {
        display: none;
    }
    .product-view-navigation .productViewNavigationWrapper,
    .pageScrolled .product-view-navigation .productViewNavigationWrapper {
        display: none;
    }
    .page-layout-1column .product.media {
        width: 100%;
    }
    .page-layout-1column .product.media .fotorama__dot {
        width: 10px;
        height: 10px;
    }
    .page-layout-1column .product.media .fotorama__active .fotorama__dot {
        background-color: #85b84b;
        border-color: #85b84b;
    }
    .page-layout-1column a.action,
    .page-layout-1column button.action {
        width: 100%;
    }
    .page-layout-1column .product-info-main {
        margin: 0;
        padding: 0;
        width: 100%;
    }
    .page-layout-1column .product-info-main .product-info-overview-container,
    .page-layout-1column .product-info-main .product-info-cmsblock-container {
        font-size: 1.8rem;
    }
    .page-layout-1column .product-info-main .product-info-cmsblock-container table {
        display: block;
    }
    .page-layout-1column .product-info-main .product-info-cmsblock-container table tr,
    .page-layout-1column .product-info-main .product-info-cmsblock-container table td {
        display: block;
        width: 100%;
    }
    .page-layout-1column .product-info-main .product-info-price .price-box {
        padding: 0;
    }
    .page-layout-1column .product-info-main .product-info-shipping-container {
        margin-top: 15px;
    }
    .page-layout-1column .product-info-details-tab-details-description .product.attribute.description .column img {
        width: 100%;
    }
    #maincontent .product-options-bottom button.tocart {
        margin: 0;
    }
    #maincontent .product-service-container {
        margin-top: 0;
    }
    #maincontent .product-service-container .info-col .slogan-container blockquote {
        left: 0;
        margin: 0;
        padding: 0 60px;
        top: 210px;
    }
    #maincontent .product-service-container .service-container {
        flex-wrap: wrap;
    }
    #maincontent .product-service-container .service-container .info-col {
        flex: 1 0 100%;
    }
    #maincontent .product-service-container .service-container .serviceFormWrapper {
        left: auto;
        top: auto;
        flex-direction: column;
        position: relative;
    }
    #maincontent .product-service-container .service-container .serviceFormWrapper .form-catalog-col {
        margin-left: 0;
    }
    #maincontent .product-service-container .callback-container .callback-form-container {
        padding: 0 20px;
        width: 100%;
    }
    #maincontent .product-service-container .callback-container .callback-form-container fieldset .phoneFieldButtonWrapper {
        color: inherit;
        flex-wrap: wrap;
    }
    #maincontent .product-service-container .callback-container .callback-form-container fieldset .phoneFieldButtonWrapper button {
        margin: 0;
    }
    .actions-toolbar > .primary {
        text-align: left;
    }
    .product.data.items {
        margin: 0;
        width: auto;
    }
    .product.info.detailed {
        width: 100%;
    }
    .product.info.detailed .product.data.items .item.title.active {
        box-shadow: 0 7px 7px -7px #808080;
    }
    .product.info.detailed .product.data.items .item.title > .switch {
        margin-right: 0;
    }
    .product.info.detailed .col1,
    .product.info.detailed .col2 {
        float: none;
        width: 100%;
    }
    .product.info.detailed .product.data.items .item.content {
        border: 0;
        margin-top: 0;
    }
    .product.info.detailed .product.data.items .item.content table {
        display: block;
    }
    .product.info.detailed .product.data.items .item.content table tr,
    .product.info.detailed .product.data.items .item.content table td {
        display: block;
        width: 100%;
    }
    .ui-accordion .ui-accordion-header {
        padding: 22px 15px 23px 20px;
    }
    .ui-accordion .owl-carousel {
        margin-top: 50px;
    }
    .customNavigation {
        display: none;
    }
    #maincontent .block.upsell {
        margin: 0;
    }
    #maincontent .product-add-form .control .select-wrapper {
        display: block;
    }
    #maincontent .product-add-form .box-tocart {
        margin: 10px 0 0;
    }
    #maincontent .product-add-form .box-tocart .fieldset {
        display: block;
    }
    #maincontent .product-add-form .box-tocart .fieldset button.tocart {
        margin: 0;
    }
    #maincontent .block.related,
    #maincontent .block.upsell {
        padding: 0;
    }
    #maincontent .block.related .products-grid,
    #maincontent .block.upsell .products-grid {
        margin: 20px 0;
    }
    #maincontent .block.related .products-grid .product-items,
    #maincontent .block.upsell .products-grid .product-items {
        margin: 0;
    }
    #maincontent .block.related .products-grid .product-items .product-item,
    #maincontent .block.upsell .products-grid .product-items .product-item {
        margin: 0;
        width: 100%;
    }
    #maincontent .block.related .products-grid .product-items .product-item:hover,
    #maincontent .block.upsell .products-grid .product-items .product-item:hover {
        box-shadow: none;
    }
    .product-category-list-container {
        padding: 20px 0;
    }
    .product-category-list-container .category-overview-info {
        padding: 0 10px;
    }
}
@media (max-width: 767px) {
    .xgifts-container {
        text-align: center;
    }
    .xgifts-container .xgifts-products-list .product-items .product-item-details {
        text-align: center;
        flex-direction: row;
        flex-wrap: wrap;
    }
    .xgifts-container .xgifts-products-list .product-items .product-item-details .product-item-text-wrapper {
        margin-top: 20px;
        margin-bottom: 20px;
    }
}
@media (max-width: 767px) {
    .opc-progress-bar {
        padding-left: 10px;
        padding-right: 10px;
    }
    .payolution.instalment-plan {
        padding: 0 20px;
    }
    #maincontent .cart-container-wrapper .product-image-container {
        max-width: 100%;
    }
    #maincontent .cart-summary .cart-summary-info-container .cart-summary-info-cms-block {
        padding-left: 15px;
        padding-right: 15px;
    }
    #maincontent .cart-summary .cart-summary-info-container .cart-summary-info-cms-block .image-container {
        text-align: center;
    }
    #maincontent .cart-summary .cart-summary-info-container .cart-summary-info-cms-block .image-container.image-andreas-berger {
        margin: 0;
    }
    .cart.table-wrapper thead .col.actions {
        display: none;
    }
    .block.gift {
        padding: 0 20px;
    }
    .col.item-actions {
        padding-left: 20px;
    }
    .cart.table-wrapper .col.item-actions {
        box-sizing: border-box;
        display: block;
        float: left;
        width: 100%;
    }
    .checkout-index-index .opc-wrapper {
        width: 100%;
        box-sizing: border-box;
    }
    .checkout-index-index .opc-wrapper > li {
        box-sizing: border-box;
    }
    .checkout-index-index .opc-sidebar {
        width: 100%;
    }
    .checkout-index-index .page-header .header.content .service-wrapper-container {
        height: auto;
        padding: 0;
    }
    .checkout-index-index .minicart-wrapper .action.showcart .counterHolder.hasItem .counter.qty {
        background: rgb(99, 109, 112);
    }
    .checkout-index-index .page-header {
        margin-bottom: 0;
        border-bottom: none;
    }
    .opc-estimated-wrapper {
        margin: 0;
    }
}
.customer-account-createpassword #maincontent,
.catalogsearch-result-index #maincontent,
.cms-page-view #maincontent,
body[class*=' customer-account-'] #maincontent,
.customer-account-create #maincontent,
.htmlsitemap-index-index #maincontent,
.checkout-onepage-success #maincontent,
.customer-account-login #maincontent,
.contact-index-index #maincontent,
.customer-account-forgotpassword #maincontent {
    padding: 0 20px;
}
.customer-account-createpassword #maincontent button.action,
.catalogsearch-result-index #maincontent button.action,
.cms-page-view #maincontent button.action,
body[class*=' customer-account-'] #maincontent button.action,
.customer-account-create #maincontent button.action,
.htmlsitemap-index-index #maincontent button.action,
.checkout-onepage-success #maincontent button.action,
.customer-account-login #maincontent button.action,
.contact-index-index #maincontent button.action,
.customer-account-forgotpassword #maincontent button.action {
    margin-right: 0;
}
@media (min-width: 768px) and (max-width: 1200px) {
    .sections.nav-sections {
        padding: 0 20px;
    }
    .cart-empty,
    .opc-progress-bar {
        padding: 20px;
    }
    .page-header .header.content {
        padding: 30px 10px 0 10px;
    }
    .xcategory .xcategoryWrapper .category-item .textWrapper .cat-title {
        margin: 0;
    }
    .top-quality-banner .advantages-col .textWrapper {
        padding-top: 40px;
    }
    .top-quality-banner .xtags-container .xtagsItemWrapper {
        flex-direction: row;
    }
    .top-quality-banner .xtags-container .xtagsItemWrapper .xtags-tag {
        flex: 1 0 145px;
    }
    .top-quality-banner .xtags-container-wrapper {
        width: auto;
        background: transparent;
        position: absolute;
        right: 126px;
        top: 192px;
        left: auto;
    }
    .xcategory .xtags-container .xtagsItemWrapper .xtags-tag {
        max-width: 50px;
    }
    .xcategory .xcategoryWrapper .containerRight .xtags-container,
    .xcategory .xcategoryWrapper .containerBelow .xtags-container {
        left: auto;
        right: 20px;
    }
    .page-layout-1column .product-info-main .product-options-bottom .box-tocart .fieldset .actions {
    }
    #maincontent .product-service-container .info-col .slogan-container blockquote {
        left: 50px;
        top: 289px;
    }
    .serviceFormWrapper .col {
        margin: 75px 0;
    }
    #maincontent .product-service-container .service-container .serviceFormWrapper .col::before {
        display: none;
    }
}
