<!--
/**
 * Copyright © 2013-2017 Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->

<div id="opc-sidebar"
     data-bind="afterRender:setModalElement, mageInit: {
    'Magento_Ui/js/modal/modal':{
        'type': 'custom',
        'modalClass': 'opc-sidebar opc-summary-wrapper',
        'wrapperClass': 'checkout-container',
        'parentModalClass': '_has-modal-custom',
        'responsive': true,
        'responsiveClass': 'custom-slide',
        'overlayClass': 'modal-custom-overlay',
        'buttons': []
    }}">
    <div class="opc-background-wrapper modal-content">
        <!-- ko foreach: getRegion('summary') -->
        <!-- ko template: getTemplate() --><!-- /ko -->
        <!--/ko-->
    </div>
    <!-- ko if: (window.checkoutConfig.sidebar.infoBlockZero) -->
    <div class="opc-background-wrapper modal-content block-zero">
        <div class="info-block-zero" data-bind="html: window.checkoutConfig.sidebar.infoBlockZero"></div>
    </div>
    <!-- /ko -->

    <!-- ko foreach: getRegion('info') -->
    <!-- ko template: getTemplate() --><!-- /ko -->
    <!--/ko-->
    <!-- ko if: (window.checkoutConfig.info_bottom) -->
    <div class="opc-info-bottom visible-xs">
        <div class="opc-block-info">
            <div class="checkout-info-block">
                <div class="info-cms-block" data-bind="html: window.checkoutConfig.info_bottom"></div>
            </div>
        </div>
    </div>
    <!-- /ko -->
</div>
