<?xml version="1.0"?>
<!--
/**
 * Copyright © 2013-2017 Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page layout="1column" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="report.bugs" remove="true"/>
        <referenceBlock name="top.links" remove="true" />
        <referenceBlock name="footer_links" remove="true"/>
        <referenceBlock name="store.links" remove="true"/>
        <referenceBlock name="store.settings" remove="true"/>
        <referenceBlock name="trustedshops_trustedshops.trustbadge" remove="true"/>

        <!--<referenceBlock name="view.addto.compare" remove="true"/>-->
        <referenceBlock name="view.addto.wishlist" remove="true"/>
        <referenceBlock name="form.subscribe" remove="true"/>

        <referenceBlock name="top.search" remove="true" />

        <referenceContainer name="header-wrapper">
            <container name="service.wrapper.container" htmlTag="div" htmlClass="service-wrapper-container" after="logo">
                <container name="service.wrapper.top" htmlTag="div" htmlClass="service-wrapper-top">
                    <block class="Magento\Cms\Block\Block" name="service-top-block">
                        <arguments>
                            <argument name="block_id" xsi:type="string">servicetop</argument>
                        </arguments>
                    </block>
                </container>
                <container name="service.wrapper.inner" htmlTag="div" htmlClass="service-wrapper-inner">
                    <block class="Magento\Framework\View\Element\Template" name="service.wrapper.additional" template="Magento_Theme::html/header/service-wrapper.phtml">
                        <container name="service.promo.inner" htmlTag="div" htmlClass="service-promo-inner">
                            <block class="Magento\Cms\Block\Block" name="header.promotion.bannerbadge">
                                <arguments>
                                    <argument name="block_id" xsi:type="string">promobadge</argument>
                                </arguments>
                            </block>
                        </container>
                    </block>
                    <container name="service.wrapper.before" htmlTag="div" htmlClass="service-wrapper-before">
                        <block class="Magento\Cms\Block\Block" name="lageinfo-block">
                            <arguments>
                                <argument name="block_id" xsi:type="string">lageinfo</argument>
                            </arguments>
                        </block>
                    </container>
                </container>
            </container>
        </referenceContainer>

        <referenceContainer name="page.top">
            <container name="service.wrapper.before.mobile" htmlTag="div" htmlClass="service-wrapper-before-mobile visible-xs">
                <block class="Magento\Cms\Block\Block" name="lageinfo-block-mobile">
                    <arguments>
                        <argument name="block_id" xsi:type="string">lageinfo</argument>
                    </arguments>
                </block>
            </container>
        </referenceContainer>



        <referenceContainer name="columns.top">
            <!--<container name="sidebar.main" as="sidebar_main" label="Sidebar Main"/>
            <container name="sidebar.additional" as="sidebar_additional" label="Sidebar Additional"/>-->
            <container name="page.messages.container" htmlTag="div" htmlClass="page-messages-container" before="page.main.title"/>
        </referenceContainer>

        <referenceBlock name="store.menu">
            <block class="Magento\Theme\Block\Html\Topmenu" ttl="3600" name="catalog.topnav" template="Magento_Theme::html/topmenu.phtml" before="-"/>
        </referenceBlock>


        <referenceContainer name="footer">
            <container name="footer-wrapper-border" htmlTag="div" htmlClass="footer-wrapper-border">
                <container name="footer-wrapper-border-inner" htmlTag="div" htmlClass="footer-wrapper-border-inner" before="-">
                    <block class="Magento\Framework\View\Element\Text">
                        <arguments>
                            <argument name="text" xsi:type="string"><![CDATA[&nbsp;]]></argument>
                        </arguments>
                    </block>
                </container>
                <container name="footer-wrapper" htmlTag="div" htmlClass="footer-wrapper container">
                    <!--<container name="footer-addthis-links" htmlTag="div" htmlClass="addthis_inline_share_toolbox">
                        <block class="Magento\Framework\View\Element\Text" name="comment.placeholder">
                            <arguments>
                                <argument name="text" xsi:type="string"></argument>
                            </arguments>
                        </block>
                    </container>-->
                    <container name="footer-col-links" htmlTag="div" htmlClass="footer-column col-md-3 links">
                        <block class="Magento\Cms\Block\Block" name="footer_block_links">
                            <arguments>
                                <argument name="block_id" xsi:type="string">footer-links</argument>
                            </arguments>
                        </block>
                    </container>
                    <container name="footer-col-payment" htmlTag="div" htmlClass="footer-column col-md-3 links">
                        <block class="Magento\Cms\Block\Block" name="footer_block_payment">
                            <arguments>
                                <argument name="block_id" xsi:type="string">footer-payment</argument>
                            </arguments>
                        </block>
                    </container>
                    <container name="footer-col-contact" htmlTag="div" htmlClass="footer-column col-md-3 links" >
                        <block class="Magento\Cms\Block\Block" name="footer_block_contact">
                            <arguments>
                                <argument name="block_id" xsi:type="string">footer-contact</argument>
                            </arguments>
                        </block>
                    </container>
                    <container name="footer-col-info" htmlTag="div" htmlClass="footer-column col-md-3 links" >
                        <block class="Magento\Cms\Block\Block" name="footer_block_info">
                            <arguments>
                                <argument name="block_id" xsi:type="string">footer-info</argument>
                            </arguments>
                        </block>
                    </container>
                </container>
            </container>
        </referenceContainer>

        <referenceContainer name="page.bottom.container">
            <container name="page.bottom" label="Before Page Footer" htmlTag="div" htmlClass="content">
                <container name="page.bottom.inner" htmlTag="div" htmlClass="page-bottom-inner" after="-">
                    <container name="page.bottom.contact.outer" htmlTag="div" htmlClass="home-contact-outer">
                        <container name="page.bottom.contact.inner" htmlTag="div" htmlClass="home-contact-inner" after="-">
                            <block class="Magento\Cms\Block\Block" name="footer_contact">
                                <arguments>
                                    <argument name="block_id" xsi:type="string">homecontactrow</argument>
                                </arguments>
                            </block>
                            <container name="page.bottom.social.outer" htmlTag="div" htmlClass="contact-social-outer" after="-">
                                <block class="Magento\Cms\Block\Block" name="footer_contact_social">
                                    <arguments>
                                        <argument name="block_id" xsi:type="string">homecontactsocial</argument>
                                    </arguments>
                                </block>
                            </container>
                        </container>
                    </container>
                </container>
            </container>
        </referenceContainer>

        <referenceContainer name="footer-container">
            <container name="footer-bottom" htmlTag="div" htmlClass="footer bottom">
                <container name="footer-bottom-container" htmlTag="div" htmlClass="container">
                    <block class="Magento\Framework\View\Element\Template" name="footer-logo" template="Magento_Theme::html/footer-logo.phtml" before="-">
                        <container name="footer-bottom-promotion" htmlTag="div" htmlClass="promotion-footer-badge">
                            <block class="Magento\Cms\Block\Block" name="footer.promotion.bannerbadge">
                                <arguments>
                                    <argument name="block_id" xsi:type="string">promofooterbadge</argument>
                                </arguments>
                            </block>
                        </container>
                    </block>
                </container>
            </container>
        </referenceContainer>

        <move element="store_language" destination="service.wrapper.top" after="-"/>
        <move element="top.search" destination="service.wrapper.container" after="-"/>
        <move element="minicart" destination="service.wrapper.inner" before="service.wrapper.additional"/>
        <move element="logo" destination="service.wrapper.inner" before="-"/>
        <move element="copyright" destination="footer-bottom-container" before="-"/>
        <move element="navigation.sections" destination="header.container" after="header-wrapper"/>
        <move element="page.messages" destination="page.messages.container" />

        <referenceContainer name="sidebar.additional" remove="true"/>

        <referenceContainer name="before.body.end">
            <block name="go.to.top" template="Magento_Theme::gototop.phtml" />
        </referenceContainer>

    </body>
</page>
