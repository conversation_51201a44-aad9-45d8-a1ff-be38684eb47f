<?php
/**
 * Copyright © 2013-2017 Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile
?>
<?php
/**
 * Category layered navigation
 *
 * @var $block \Magento\LayeredNavigation\Block\Navigation
 */
?>

<?php if ($block->canShowBlock()): ?>
    <div class="filter-current-container container">
        <div class="block-content filter-content">
            <?php echo $block->getChildHtml('state') ?>
        </div>
    </div>
<?php endif; ?>
