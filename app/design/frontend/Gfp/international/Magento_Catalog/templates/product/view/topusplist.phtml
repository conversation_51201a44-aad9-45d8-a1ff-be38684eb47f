<?php
$_product = $block->getProduct();
$_code = $block->getAtCode();
$_size = $block->getSize();
$_count = 0;
?>

<div class="topusp-list product-info-overview-container">
    <ul>
    <?php if($_product->getResource()->getAttribute($_size)->getFrontend()->getValue($_product)) { ?>
        <li><?php echo $_product->getResource()->getAttribute($_size)->getFrontend()->getValue($_product); ?></li>
    <?php } ?>
    <?php for ($i = 1; $i <= 6; $i++) {
        //if ($_product->getResource()->getAttribute($_code."".$i)->getFrontend()->getValue($_product)) {
        if ($_product->getData($_code."".$i)) { $_count++; ?>
            <li><?php echo $_product->getResource()->getAttribute($_code."".$i)->getFrontend()->getValue($_product); ?></li>
    <?php } } ?>
    </ul>
</div>

<?php if($_count < 1) { ?>
    <div class="block-overview-top">
        <?php if($_product->getData('block_id_desc')) { echo "<div class='block-overview-top-inner'>" . $this->getLayout()->createBlock('Magento\Cms\Block\Block')->setBlockId($_product->getData("block_id_desc"))->toHtml() . "</div>"; } ?>
    </div>
<?php } ?>
