<?php
$_product = $block->getProduct();
$setId = $_product->getData('featuredsetid');
$setId2 = $_product->getData('featuredsetid2');

if($setId || $setId2):
    $_children = $_product->getTypeInstance()->getUsedProducts($_product);
    $set1 = "";
    $set2 = "";

    echo "<h3>" . __("Jetzt sparen mit unseren Set-Angeboten") . "</h3>";

    foreach ($_children as $child):
        if($setId && $child->getID() == $setId):
            $set1 = $child->getSet();
            $setname1 = $child->getResource()->getAttribute('set')->getFrontend()->getValue($child);
        endif;
        if($setId2 && $child->getID() == $setId2):
            $set2 = $child->getSet();
            $setname2 = $child->getResource()->getAttribute('set')->getFrontend()->getValue($child);
        endif;
    endforeach; ?>

    <div class="product-setchooser-container">

        <?php if($set1 != ""): ?>
            <div class="product-setchooser-inner">
                <strong><?php echo $setname1; ?></strong>
                <div class="product-setchooser-items row">
                    <?php if($_product->getFeaturedsettextbox()): ?>
                        <div class="product-setchooser-item col-md-7">
                            <?php echo $_product->getFeaturedsettextbox(); ?>
                        </div>
                    <?php endif; ?>

                    <div class="product-setchooser-actions<?php if($_product->getFeaturedsettextbox() != ""): ?> col-md-5<?php endif; ?>" id="product-setchooser-actions-<?php echo $set1; ?>">
                        <?php if($_product->getFeaturedsetpricebox()): ?>
                            <div class="product-setchooser-pricebox">
                                <?php echo $_product->getFeaturedsetpricebox(); ?>
                            </div>
                        <?php endif; ?>

                        <div class="product-setchooser-success">
                            <span><?php echo __("Gute Wahl"); ?>!</span>
                            <span><?php echo __("Sie haben das Set bereits gewählt"); ?></span>
                        </div>

                        <button class="action btn-main product-setchooser-button" type="button" id="product-setchooser-button-<?php echo $set1; ?>">
                            <span><?php echo __("Jetzt Set-Angebot sichern"); ?></span>
                        </button>
                    </div>

                    <?php if($_product->getFeaturedsettextrow()): ?>
                        <div class="product-setchooser-row col-md-12">
                            <?php echo $_product->getFeaturedsettextrow(); ?>
                        </div>
                    <?php endif; ?>
                </div>

                <script type="text/javascript">
                    require(['jquery', 'jquery-ui-modules/effect'], function($) {
                        $('#product-setchooser-button-<?php echo $set1; ?>').on('click', function (e) {
                            let $option = $('.swatch-option[data-option-id=<?php echo $set1; ?>]');
                            if($option.attr('aria-checked') == "true") {
                                e.preventDefault();
                            }
                            else {
                                $option.trigger( "click" );
                                document.getElementById("product_addtocart_form").scrollIntoView({
                                    behavior: 'smooth'
                                });
                            }
                        });
                    });
                </script>
            </div>
        <?php endif; ?>

        <?php if($set2 != ""): ?>
            <div class="product-setchooser-inner">
                <strong><?php echo $setname2; ?></strong>
                <div class="product-setchooser-items row">
                    <?php if($_product->getData('featuredsettextbox2')): ?>
                        <div class="product-setchooser-item col-md-7">
                            <?php echo $_product->getData('featuredsettextbox2'); ?>
                        </div>
                    <?php endif; ?>

                    <div class="product-setchooser-actions<?php if($_product->getData('featuredsettextbox2') != ""): ?> col-md-5<?php endif; ?>" id="product-setchooser-actions-<?php echo $set2; ?>">
                        <?php if($_product->getData('featuredsetpricebox2')): ?>
                            <div class="product-setchooser-pricebox">
                                <?php echo $_product->getData('featuredsetpricebox2'); ?>
                            </div>
                        <?php endif; ?>

                        <div class="product-setchooser-success">
                            <span><?php echo __("Gute Wahl"); ?>!</span>
                            <span><?php echo __("Sie haben das Set bereits gewählt"); ?></span>
                        </div>

                        <button class="action btn-main product-setchooser-button" type="button" id="product-setchooser-button-<?php echo $set2; ?>">
                            <span><?php echo __("Jetzt Set-Angebot sichern"); ?></span>
                        </button>
                    </div>

                    <?php if($_product->getData('featuredsettextrow2')): ?>
                        <div class="product-setchooser-row col-md-12">
                            <?php echo $_product->getData('featuredsettextrow2'); ?>
                        </div>
                    <?php endif; ?>
                </div>

                <script type="text/javascript">
                    require(['jquery', 'jquery-ui-modules/effect'], function($) {
                        $('#product-setchooser-button-<?php echo $set2; ?>').on('click', function (e) {
                            let $option = $('.swatch-option[data-option-id=<?php echo $set2; ?>]');
                            if($option.attr('aria-checked') == "true") {
                                e.preventDefault();
                            }
                            else {
                                $option.trigger( "click" );
                                document.getElementById("product_addtocart_form").scrollIntoView({
                                    behavior: 'smooth'
                                });
                            }
                        });
                    });
                </script>
            </div>
        <?php endif; ?>

    </div>
<?php endif; ?>