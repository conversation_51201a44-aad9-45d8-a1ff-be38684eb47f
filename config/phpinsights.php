<?php

declare(strict_types=1);

return [
    'preset' => 'magento2',
    'ide' => 'phpstorm',
    'exclude' => [
        'config',
        'app/etc/env.php',
        'phpinsights.php',
        'app/code/CtiDigital',
        'app/code/Emipro',
        'app/code/FME',
        'app/code/GFP',
        'app/code/Hyva',
        'app/code/Magefan',
        'app/code/MagePsycho',
        'app/code/Magezon',
        'app/code/Magmodules',
        'app/code/Mstage',
        'app/code/Payolution',
        'app/code/Tigren',
        'app/code/Vsourz',
        'app/code/Xortex',
        'softdy'
    ],
    'add' => [
        //  ExampleMetric::class => [
        //      ExampleInsight::class,
        //  ]
    ],
    'remove' => [
        \NunoMaduro\PhpInsights\Domain\Sniffs\ForbiddenSetterSniff::class,
        \SlevomatCodingStandard\Sniffs\TypeHints\DeclareStrictTypesSniff::class,
    ],
    'config' => [
        \PHP_CodeSniffer\Standards\Generic\Sniffs\Files\LineLengthSniff::class => [
            'lineLimit' => 120,
            'absoluteLineLimit' => 160,
        ]
    ],
    'requirements' => [
        'min-quality' => 56,
        'min-complexity' => 65,
        'min-architecture' => 70,
        'min-style' => 50,
        'disable-security-check' => true
    ],
    'threads' => null,
    'timeout' => 60,
];
