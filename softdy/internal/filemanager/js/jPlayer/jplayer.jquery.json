{"name": "jplayer", "title": "jPlayer : HTML5 Audio & Video", "description": "jPlayer allows you to create a media player with a consistent interface and experience across all browsers.", "keywords": ["audio", "video", "media", "player", "jplayer", "html5", "streaming"], "version": "2.4.0", "author": {"name": "<PERSON>", "url": "http://happyworm.com/"}, "licenses": [{"type": "MIT", "url": "https://github.com/happyworm/jPlayer/blob/master/MIT-LICENSE.txt"}], "dependencies": {"jquery": ">=1.7"}, "homepage": "http://jplayer.org/", "demo": "http://jplayer.org/latest/demos/", "docs": "http://jplayer.org/latest/developer-guide/", "download": "http://jplayer.org/download/", "bugs": "https://github.com/happyworm/jPlayer/issues"}