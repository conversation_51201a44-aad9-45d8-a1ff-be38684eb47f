module.exports = [
  {
    name: 'common',
    modules: {
      jquery: 'jquery',
      'Magento_PageCache/js/form-key-provider': 'Magento_PageCache/js/form-key-provider',
      text: 'mage/requirejs/text',
      'text!js-translation.json': 'js-translation.json',
      'Magento_Translation/js/mage-translation-dictionary': 'Magento_Translation/js/mage-translation-dictionary',
      underscore: 'underscore',
      'mage/template': 'mage/template',
      'text!ui/template/modal/modal-popup.html': 'Magento_Ui/templates/modal/modal-popup.html',
      'text!ui/template/modal/modal-slide.html': 'Magento_Ui/templates/modal/modal-slide.html',
      'text!ui/template/modal/modal-custom.html': 'Magento_Ui/templates/modal/modal-custom.html',
      'Magento_Ui/js/lib/key-codes': 'Magento_Ui/js/lib/key-codes',
      domReady: 'requirejs/domReady',
      'mage/ie-class-fixer': 'mage/ie-class-fixer',
      'knockoutjs/knockout': 'knockoutjs/knockout',
      'knockoutjs/knockout-es5': 'knockoutjs/knockout-es5',
      'knockoutjs/knockout-repeat': 'knockoutjs/knockout-repeat',
      'knockoutjs/knockout-fast-foreach': 'knockoutjs/knockout-fast-foreach',
      'Magento_Ui/js/lib/knockout/extender/observable_array': 'Magento_Ui/js/lib/knockout/extender/observable_array',
      'mage/utils/wrapper': 'mage/utils/wrapper',
      'Magento_Ui/js/lib/core/events': 'Magento_Ui/js/lib/core/events',
      'Magento_Ui/js/lib/knockout/extender/bound-nodes': 'Magento_Ui/js/lib/knockout/extender/bound-nodes',
      'jquery/patches/jquery': 'jquery/patches/jquery',
      'jquery/jquery.mobile.custom': 'jquery/jquery.mobile.custom',
      'jquery-ui-modules/widget': 'jquery/ui-modules/widget',
      'jquery-ui-modules/core': 'jquery/ui-modules/core',
      'jquery/jquery-migrate': 'jquery/jquery-migrate',
      'mage/smart-keyboard-handler': 'mage/smart-keyboard-handler',
      'jquery/jquery.cookie': 'jquery/jquery.cookie',
      'mage/apply/scripts': 'mage/apply/scripts',
      'mage/apply/main': 'mage/apply/main',
      'mage/mage': 'mage/mage',
      'mage/cookies': 'mage/cookies',
      'mage/translate': 'mage/translate',
      'Magento_Ui/js/modal/modal': 'Magento_Ui/js/modal/modal',
      'Magento_Ui/js/modal/confirm': 'Magento_Ui/js/modal/confirm',
      'mage/dataPost': 'mage/dataPost',
      'Magento_Ui/js/lib/registry/registry': 'Magento_Ui/js/lib/registry/registry',
      'jquery-ui-modules/mouse': 'jquery/ui-modules/mouse',
      'jquery-ui-modules/resizable': 'jquery/ui-modules/resizable',
      'Magento_Ui/js/lib/knockout/bindings/mage-init': 'Magento_Ui/js/lib/knockout/bindings/mage-init',
      moment: 'moment',
      'jquery-ui-modules/datepicker': 'jquery/ui-modules/datepicker',
      'jquery-ui-modules/slider': 'jquery/ui-modules/slider',
      'jquery-ui-modules/timepicker': 'jquery/ui-modules/timepicker',
      'mage/calendar': 'mage/calendar',
      'Magento_Ui/js/lib/knockout/bindings/fadeVisible': 'Magento_Ui/js/lib/knockout/bindings/fadeVisible',
      'text!ui/template/tooltip/tooltip.html': 'Magento_Ui/templates/tooltip/tooltip.html',
      spectrum: 'jquery/spectrum/spectrum',
      tinycolor: 'jquery/spectrum/tinycolor',
      'mage/utils/strings': 'mage/utils/strings',
      'mage/utils/objects': 'mage/utils/objects',
      'mage/utils/compare': 'mage/utils/compare',
      'mage/utils/misc': 'mage/utils/misc',
      'mage/utils/arrays': 'mage/utils/arrays',
      'mage/utils/template': 'mage/utils/template',
      'mage/utils/main': 'mage/utils/main',
      'Magento_Ui/js/lib/core/class': 'Magento_Ui/js/lib/core/class',
      'Magento_Ui/js/lib/knockout/template/observable_source': 'Magento_Ui/js/lib/knockout/template/observable_source',
      'Magento_Ui/js/lib/core/storage/local': 'Magento_Ui/js/lib/core/storage/local',
      'Magento_Ui/js/lib/knockout/bindings/optgroup': 'Magento_Ui/js/lib/knockout/bindings/optgroup',
      'Mstage_DateTimePicker/js/lib/knockout/bindings/datepicker': 'Mstage_DateTimePicker/js/lib/knockout/bindings/datepicker',
      'Magento_Ui/js/lib/knockout/template/loader': 'Magento_Ui/js/lib/knockout/template/loader',
      'Magento_Ui/js/lib/knockout/template/renderer': 'Magento_Ui/js/lib/knockout/template/renderer',
      'Magento_Ui/js/lib/knockout/bindings/i18n': 'Magento_Ui/js/lib/knockout/bindings/i18n',
      'Magento_Ui/js/lib/knockout/bindings/range': 'Magento_Ui/js/lib/knockout/bindings/range',
      'Magento_Ui/js/lib/knockout/bindings/keyboard': 'Magento_Ui/js/lib/knockout/bindings/keyboard',
      'Magento_Ui/js/lib/knockout/bindings/after-render': 'Magento_Ui/js/lib/knockout/bindings/after-render',
      'Magento_Ui/js/lib/knockout/bindings/autoselect': 'Magento_Ui/js/lib/knockout/bindings/autoselect',
      'Magento_Ui/js/lib/knockout/bindings/outer_click': 'Magento_Ui/js/lib/knockout/bindings/outer_click',
      'Magento_Ui/js/lib/knockout/bindings/collapsible': 'Magento_Ui/js/lib/knockout/bindings/collapsible',
      'Magento_Ui/js/lib/knockout/bindings/staticChecked': 'Magento_Ui/js/lib/knockout/bindings/staticChecked',
      'Magento_Ui/js/lib/knockout/bindings/simple-checked': 'Magento_Ui/js/lib/knockout/bindings/simple-checked',
      'Magento_Ui/js/lib/knockout/bindings/bind-html': 'Magento_Ui/js/lib/knockout/bindings/bind-html',
      'Magento_Ui/js/lib/knockout/bindings/tooltip': 'Magento_Ui/js/lib/knockout/bindings/tooltip',
      'Magento_Ui/js/lib/knockout/bindings/color-picker': 'Magento_Ui/js/lib/knockout/bindings/color-picker',
      'Magento_Ui/js/lib/logger/formatter': 'Magento_Ui/js/lib/logger/formatter',
      'Magento_Ui/js/lib/logger/message-pool': 'Magento_Ui/js/lib/logger/message-pool',
      'Magento_Ui/js/lib/logger/levels-pool': 'Magento_Ui/js/lib/logger/levels-pool',
      'Magento_Ui/js/lib/logger/logger': 'Magento_Ui/js/lib/logger/logger',
      'Magento_Ui/js/lib/logger/console-output-handler': 'Magento_Ui/js/lib/logger/console-output-handler',
      'Magento_Ui/js/lib/logger/logger-utils': 'Magento_Ui/js/lib/logger/logger-utils',
      'Magento_Ui/js/lib/view/utils/bindings': 'Magento_Ui/js/lib/view/utils/bindings',
      'mage/common': 'mage/common',
      'Magento_Theme/js/theme': 'Magento_Theme/js/theme',
      'Magento_Ui/js/lib/view/utils/dom-observer': 'Magento_Ui/js/lib/view/utils/dom-observer',
      'Magento_Ui/js/lib/view/utils/async': 'Magento_Ui/js/lib/view/utils/async',
      'Magento_Ui/js/lib/knockout/bindings/resizable': 'Magento_Ui/js/lib/knockout/bindings/resizable',
      'Magento_Ui/js/lib/logger/entry': 'Magento_Ui/js/lib/logger/entry',
      'Magento_Ui/js/lib/logger/entry-factory': 'Magento_Ui/js/lib/logger/entry-factory',
      'Magento_Ui/js/lib/logger/console-logger': 'Magento_Ui/js/lib/logger/console-logger',
      'Magento_Ui/js/lib/knockout/template/engine': 'Magento_Ui/js/lib/knockout/template/engine',
      'Magento_Ui/js/lib/knockout/bindings/scope': 'Magento_Ui/js/lib/knockout/bindings/scope',
      'Magento_Ui/js/lib/knockout/bindings/bootstrap': 'Magento_Ui/js/lib/knockout/bindings/bootstrap',
      'Magento_Ui/js/lib/knockout/bootstrap': 'Magento_Ui/js/lib/knockout/bootstrap',
      'mage/bootstrap': 'mage/bootstrap',
      'Magezon_Builder/js/vimeo/player': 'Magezon_Builder/js/vimeo/player',
      'Magezon_Builder/js/jarallax/jarallax.min': 'Magezon_Builder/js/jarallax/jarallax.min',
      'Magezon_Builder/js/jarallax/jarallax-video': 'Magezon_Builder/js/jarallax/jarallax-video',
      'Magezon_Builder/js/parallax': 'Magezon_Builder/js/parallax',
      'Magezon_Builder/js/responsive-typography': 'Magezon_Builder/js/responsive-typography',
      'Magezon_Core/js/jquery.magnific-popup.min': 'Magezon_Core/js/jquery.magnific-popup.min',
      'Magezon_Builder/js/waypoints/jquery.waypoints': 'Magezon_Builder/js/waypoints/jquery.waypoints',
      'Magezon_Builder/js/magezon-builder': 'Magezon_Builder/js/magezon-builder',
      'Magezon_PageBuilder/js/common': 'Magezon_PageBuilder/js/common',
      'fotorama/fotorama': 'fotorama/fotorama',
      matchMedia: 'matchMedia',
      'text!mage/gallery/gallery.html': 'mage/gallery/gallery.html',
      'mage/gallery/gallery': 'mage/gallery/gallery',
      'FME_Photogallery/js/shadowbox': 'FME_Photogallery/js/shadowbox',
      'jquery-ui-modules/effect': 'jquery/ui-modules/effect',
      'jquery-ui-modules/effect-drop': 'jquery/ui-modules/effect-drop',
      'jquery-ui-modules/sortable': 'jquery/ui-modules/sortable',
      'jquery-ui-modules/tabs': 'jquery/ui-modules/tabs',
      'mage/loader': 'mage/loader',
      'Magento_PageCache/js/page-cache': 'Magento_PageCache/js/page-cache',
      'Magento_Cookie/js/notices': 'Magento_Cookie/js/notices',
      'jquery/jquery.storageapi.min': 'jquery/jquery.storageapi.min',
      'jquery/jquery-storageapi': 'Magento_Cookie/js/jquery.storageapi.extended',
      'mage/collapsible': 'mage/collapsible',
      'mage/tabs': 'mage/tabs',
      'jquery-ui-modules/position': 'jquery/ui-modules/position',
      'jquery-ui-modules/menu': 'jquery/ui-modules/menu',
      'mage/menu': 'mage/menu',
      'Xortex_Xinfo/js/popup': 'Xortex_Xinfo/js/popup',
      'Min_Base/js/min': 'Min_Base/js/min',
      'Magento_Theme/js/cookie-status': 'Magento_Theme/js/cookie-status',
      'Magento_Ui/js/block-loader': 'Magento_Ui/js/block-loader',
      'Magento_Customer/js/section-config': 'Magento_Customer/js/section-config',
      'Magento_Ui/js/core/renderer/types': 'Magento_Ui/js/core/renderer/types',
      'Magento_Ui/js/core/renderer/layout': 'Magento_Ui/js/core/renderer/layout',
      'Magento_Ui/js/core/app': 'Magento_Ui/js/core/app',
      'jquery-ui-modules/button': 'jquery/ui-modules/button',
      'jquery-ui-modules/draggable': 'jquery/ui-modules/draggable',
      'jquery-ui-modules/dialog': 'jquery/ui-modules/dialog',
      'jquery/patches/jquery-ui': 'jquery/patches/jquery-ui',
      'mage/dropdown': 'mage/dropdown',
      'mage/url': 'mage/url',
      'mage/storage': 'mage/storage',
      'Magento_Customer/js/customer-data': 'Magento_Customer/js/customer-data',
      'Magento_Persistent/js/view/customer-data-mixin': 'Magento_Persistent/js/view/customer-data-mixin',
      'text!ui/template/block-loader.html': 'Magento_Ui/templates/block-loader.html',
      'Magento_Ui/js/lib/core/element/links': 'Magento_Ui/js/lib/core/element/links',
      'Magento_Ui/js/lib/core/element/element': 'Magento_Ui/js/lib/core/element/element',
      'Magento_Customer/js/invalidation-processor': 'Magento_Customer/js/invalidation-processor',
      'Magento_Ui/js/modal/alert': 'Magento_Ui/js/modal/alert',
      'Magento_Ui/js/lib/core/collection': 'Magento_Ui/js/lib/core/collection',
      'Magento_Customer/js/model/authentication-popup': 'Magento_Customer/js/model/authentication-popup',
      'mage/decorate': 'mage/decorate',
      'jquery-ui-modules/effect-fade': 'jquery/ui-modules/effect-fade',
      'Magento_Checkout/js/sidebar': 'Magento_Checkout/js/sidebar',
      'Magento_Checkout/js/view/minicart': 'Magento_Checkout/js/view/minicart',
      'Magento_Tax/js/view/checkout/minicart/subtotal/totals': 'Magento_Tax/js/view/checkout/minicart/subtotal/totals',
      'Magento_Msrp/js/view/checkout/minicart/subtotal/totals': 'Magento_Msrp/js/view/checkout/minicart/subtotal/totals',
      'Magento_Checkout/js/view/cart-item-renderer': 'Magento_Checkout/js/view/cart-item-renderer',
      'Magento_Catalog/js/view/image': 'Magento_Catalog/js/view/image',
      'Magento_Ui/js/lib/spinner': 'Magento_Ui/js/lib/spinner',
      'mage/requirejs/resolver': 'mage/requirejs/resolver',
      'jquery/jquery.metadata': 'jquery/jquery.metadata',
      'jquery/validate': 'jquery/jquery.validate',
      'mage/validation': 'mage/validation',
      'Magento_Captcha/js/action/refresh': 'Magento_Captcha/js/action/refresh',
      'Magento_Captcha/js/model/captcha': 'Magento_Captcha/js/model/captcha',
      'Magento_Captcha/js/model/captchaList': 'Magento_Captcha/js/model/captchaList',
      'Magento_Captcha/js/view/checkout/defaultCaptcha': 'Magento_Captcha/js/view/checkout/defaultCaptcha',
      'Amasty_InvisibleCaptcha/js/model/am-recaptcha': 'Amasty_InvisibleCaptcha/js/model/am-recaptcha',
      'Amasty_InvisibleCaptcha/js/view/am-recaptcha-loader': 'Amasty_InvisibleCaptcha/js/view/am-recaptcha-loader',
      'Amasty_InvisibleCaptcha/js/view/am-recaptcha': 'Amasty_InvisibleCaptcha/js/view/am-recaptcha',
      'Magento_Security/js/escaper': 'Magento_Security/js/escaper',
      'Magento_Theme/js/view/messages': 'Magento_Theme/js/view/messages',
      'Magento_Catalog/js/product/storage/ids-storage': 'Magento_Catalog/js/product/storage/ids-storage',
      'Magento_Catalog/js/product/query-builder': 'Magento_Catalog/js/product/query-builder',
      'Magento_Catalog/js/product/storage/data-storage': 'Magento_Catalog/js/product/storage/data-storage',
      'Magento_Catalog/js/product/storage/ids-storage-compare': 'Magento_Catalog/js/product/storage/ids-storage-compare',
      'Magento_Catalog/js/product/storage/storage-service': 'Magento_Catalog/js/product/storage/storage-service',
      'Magento_Catalog/js/storage-manager': 'Magento_Catalog/js/storage-manager',
      'Magento_Customer/js/invalidation-rules/website-rule': 'Magento_Customer/js/invalidation-rules/website-rule',
      'Magento_Ui/js/form/adapter/buttons': 'Magento_Ui/js/form/adapter/buttons',
      'Magento_Ui/js/form/adapter': 'Magento_Ui/js/form/adapter',
      'Magento_Ui/js/form/form': 'Magento_Ui/js/form/form',
      'Magento_Ui/js/model/messages': 'Magento_Ui/js/model/messages',
      'Magento_Ui/js/model/messageList': 'Magento_Ui/js/model/messageList',
      'Magento_Customer/js/action/login': 'Magento_Customer/js/action/login',
      'Magento_Customer/js/view/authentication-popup': 'Magento_Customer/js/view/authentication-popup',
      'Magento_Captcha/js/view/checkout/loginCaptcha': 'Magento_Captcha/js/view/checkout/loginCaptcha',
      'jquery-ui-modules/effect-blind': 'jquery/ui-modules/effect-blind',
      'Magento_ReCaptchaFrontendUi/js/registry': 'Magento_ReCaptchaFrontendUi/js/registry',
      'Magento_ReCaptchaFrontendUi/js/ui-messages-mixin': 'Magento_ReCaptchaFrontendUi/js/ui-messages-mixin',
      'Magento_Ui/js/view/messages': 'Magento_Ui/js/view/messages',
      'text!Magento_Checkout/template/minicart/content.html': 'Magento_Checkout/template/minicart/content.html',
      'text!Magento_Customer/template/authentication-popup.html': 'Magento_Customer/template/authentication-popup.html',
      'text!ui/template/collection.html': 'Magento_Ui/templates/collection.html',
      'text!Magento_Ui/template/messages.html': 'Magento_Ui/template/messages.html',
      'text!Magento_Captcha/template/checkout/captcha.html': 'Magento_Captcha/template/checkout/captcha.html'
    }
  },
  {
    url: '',
    name: 'category',
    modules: {
      'Magento_Cookie/js/require-cookie': 'Magento_Cookie/js/require-cookie',
      'Magezon_PageBuilder/js/collapse': 'Magezon_PageBuilder/js/collapse'
    }
  },
  {
    url: '',
    name: 'cms',
    modules: {}
  },
  {
    url: [],
    name: 'product',
    modules: {
      'jquery-ui-modules/accordion': 'jquery/ui-modules/accordion',
      'FME_Photogallery/js/owlcarousel': 'FME_Photogallery/js/owlcarousel',
      'FME_Photogallery/js/jqueryfunction': 'FME_Photogallery/js/jqueryfunction',
      'Magento_Cookie/js/require-cookie': 'Magento_Cookie/js/require-cookie',
      'magnifier/magnifier': 'magnifier/magnifier',
      'magnifier/magnify': 'magnifier/magnify',
      'Magento_Catalog/js/gallery': 'Magento_Catalog/js/gallery',
      'Magento_ProductVideo/js/load-player': 'Magento_ProductVideo/js/load-player',
      'Magento_ProductVideo/js/fotorama-add-video-events': 'Magento_ProductVideo/js/fotorama-add-video-events',
      'mage/validation/validation': 'mage/validation/validation',
      'Magento_Catalog/product/view/validation': 'Magento_Catalog/product/view/validation',
      'Magento_Paypal/js/in-context/paypal-sdk': 'Magento_Paypal/js/in-context/paypal-sdk',
      'Magento_Paypal/js/in-context/express-checkout-smart-buttons': 'Magento_Paypal/js/in-context/express-checkout-smart-buttons',
      'Magento_Review/js/process-reviews': 'Magento_Review/js/process-reviews',
      'Magento_Theme/js/model/breadcrumb-list': 'Magento_Theme/js/model/breadcrumb-list',
      'text!Magento_Theme/templates/breadcrumbs.html': 'Magento_Theme/templates/breadcrumbs.html',
      'Magento_Theme/js/view/breadcrumbs': 'Magento_Theme/js/view/breadcrumbs',
      'Magento_Theme/js/view/add-home-breadcrumb': 'Magento_Theme/js/view/add-home-breadcrumb',
      'Magento_Catalog/js/product/breadcrumbs': 'Magento_Catalog/js/product/breadcrumbs',
      'Magento_Catalog/js/product/view/product-ids': 'Magento_Catalog/js/product/view/product-ids',
      'Magento_Catalog/js/product/view/product-ids-resolver': 'Magento_Catalog/js/product/view/product-ids-resolver',
      'Magento_Catalog/js/product/view/product-info': 'Magento_Catalog/js/product/view/product-info',
      'Magento_Catalog/js/product/view/product-info-resolver': 'Magento_Catalog/js/product/view/product-info-resolver',
      'Magento_Catalog/js/catalog-add-to-cart': 'Magento_Catalog/js/catalog-add-to-cart',
      'Magento_ConfigurableProduct/js/product/view/product-info-resolver': 'Magento_ConfigurableProduct/js/product/view/product-info-resolver',
      'Magento_ConfigurableProduct/js/catalog-add-to-cart-mixin': 'Magento_ConfigurableProduct/js/catalog-add-to-cart-mixin',
      'Mstage_ExtendConfigurable/js/catalog/catalog-add-to-cart': 'Mstage_ExtendConfigurable/js/catalog/catalog-add-to-cart',
      'Magento_Catalog/js/validate-product': 'Magento_Catalog/js/validate-product',
      'Magento_Paypal/js/in-context/express-checkout-wrapper': 'Magento_Paypal/js/in-context/express-checkout-wrapper',
      'Magento_Paypal/js/in-context/product-express-checkout': 'Magento_Paypal/js/in-context/product-express-checkout',
      'Magento_Catalog/js/product/view/provider': 'Magento_Catalog/js/product/view/provider',
      'Magento_Catalog/js/price-utils': 'Magento_Catalog/js/price-utils',
      'Magento_Catalog/js/price-box': 'Magento_Catalog/js/price-box',
      'Klarna_Onsitemessaging/js/pricebox-widget-mixin': 'Klarna_Onsitemessaging/js/pricebox-widget-mixin',
      'text!Magento_InstantPurchase/template/confirmation.html': 'Magento_InstantPurchase/template/confirmation.html',
      'Magento_InstantPurchase/js/view/instant-purchase': 'Magento_InstantPurchase/js/view/instant-purchase',
      'text!Magento_InstantPurchase/template/instant-purchase.html': 'Magento_InstantPurchase/template/instant-purchase.html'
    }
  },
  {
    url: {},
    name: 'checkout',
    modules: {
      'mage/sticky': 'mage/sticky',
      'Magento_Checkout/js/discount-codes': 'Magento_Checkout/js/discount-codes',
      'Magento_Checkout/js/shopping-cart': 'Magento_Checkout/js/shopping-cart',
      'Magento_Checkout/js/action/update-shopping-cart': 'Magento_Checkout/js/action/update-shopping-cart',
      'Mstage_CWPicker/js/cart/delivery': 'Mstage_CWPicker/js/cart/delivery',
      'Xortex_Xcheckout/js/view/delivery-comment-block': 'Xortex_Xcheckout/js/view/delivery-comment-block',
      'Magento_Checkout/js/model/shipping-rates-validation-rules': 'Magento_Checkout/js/model/shipping-rates-validation-rules',
      'Magento_Checkout/js/model/quote': 'Magento_Checkout/js/model/quote',
      'Magento_Checkout/js/model/totals': 'Magento_Checkout/js/model/totals',
      'text!Xortex_Xcheckout/template/delivery-comment-block.html': 'Xortex_Xcheckout/template/delivery-comment-block.html',
      'Magento_OfflineShipping/js/model/shipping-rates-validation-rules/tablerate': 'Magento_OfflineShipping/js/model/shipping-rates-validation-rules/tablerate',
      'Magento_OfflineShipping/js/model/shipping-rates-validator/tablerate': 'Magento_OfflineShipping/js/model/shipping-rates-validator/tablerate',
      'Magento_OfflineShipping/js/model/shipping-rates-validation-rules/flatrate': 'Magento_OfflineShipping/js/model/shipping-rates-validation-rules/flatrate',
      'Magento_OfflineShipping/js/model/shipping-rates-validator/flatrate': 'Magento_OfflineShipping/js/model/shipping-rates-validator/flatrate',
      'Magento_OfflineShipping/js/model/shipping-rates-validation-rules/freeshipping': 'Magento_OfflineShipping/js/model/shipping-rates-validation-rules/freeshipping',
      'Magento_OfflineShipping/js/model/shipping-rates-validator/freeshipping': 'Magento_OfflineShipping/js/model/shipping-rates-validator/freeshipping',
      'Payone_Core/js/view/checkout/summary/grand-total-mixin': 'Payone_Core/js/view/checkout/summary/grand-total-mixin',
      'Magento_GiftMessage/js/model/gift-message': 'Magento_GiftMessage/js/model/gift-message',
      'Magento_GiftMessage/js/model/gift-options': 'Magento_GiftMessage/js/model/gift-options',
      'Magento_Checkout/js/model/url-builder': 'Magento_Checkout/js/model/url-builder',
      'Magento_Checkout/js/model/error-processor': 'Magento_Checkout/js/model/error-processor',
      'Xortex_Xcheckout/js/model/cart/full-screen-loader': 'Xortex_Xcheckout/js/model/cart/full-screen-loader',
      'Magento_Checkout/js/model/shipping-address/form-popup-state': 'Magento_Checkout/js/model/shipping-address/form-popup-state',
      'Magento_Catalog/js/price-utils': 'Magento_Catalog/js/price-utils',
      'Magento_Checkout/js/action/select-shipping-address': 'Magento_Checkout/js/action/select-shipping-address',
      'Magento_Checkout/js/model/postcode-validator': 'Magento_Checkout/js/model/postcode-validator',
      'Magento_Checkout/js/model/step-navigator': 'Magento_Checkout/js/model/step-navigator',
      'PayPal_Braintree/js/model/step-navigator-mixin': 'PayPal_Braintree/js/model/step-navigator-mixin',
      'Xortex_Xcheckout/js/model/step-navigator-mixin': 'Xortex_Xcheckout/js/model/step-navigator-mixin',
      'Magento_Checkout/js/view/summary/abstract-total': 'Magento_Checkout/js/view/summary/abstract-total',
      'Magento_Tax/js/view/checkout/summary/subtotal': 'Magento_Tax/js/view/checkout/summary/subtotal',
      'Magento_Tax/js/view/checkout/summary/tax': 'Magento_Tax/js/view/checkout/summary/tax',
      'Magento_Tax/js/view/checkout/cart/totals/tax': 'Magento_Tax/js/view/checkout/cart/totals/tax',
      'Magento_Weee/js/view/checkout/summary/weee': 'Magento_Weee/js/view/checkout/summary/weee',
      'Magento_Weee/js/view/cart/totals/weee': 'Magento_Weee/js/view/cart/totals/weee',
      'Magento_SalesRule/js/view/summary/discount': 'Magento_SalesRule/js/view/summary/discount',
      'Magento_SalesRule/js/view/cart/totals/discount': 'Magento_SalesRule/js/view/cart/totals/discount',
      'Magento_Tax/js/view/checkout/summary/grand-total': 'Magento_Tax/js/view/checkout/summary/grand-total',
      'Magento_Checkout/js/view/summary/shipping': 'Magento_Checkout/js/view/summary/shipping',
      'Magento_Tax/js/view/checkout/summary/shipping': 'Magento_Tax/js/view/checkout/summary/shipping',
      'Magento_Tax/js/view/checkout/cart/totals/shipping': 'Magento_Tax/js/view/checkout/cart/totals/shipping',
      'Magento_GiftMessage/js/model/url-builder': 'Magento_GiftMessage/js/model/url-builder',
      'Magento_Checkout/js/model/payment/method-list': 'Magento_Checkout/js/model/payment/method-list',
      'Payone_Core/js/model/error-processor-mixin': 'Payone_Core/js/model/error-processor-mixin',
      'Xortex_Xcheckout/js/model/cart-save-processor': 'Xortex_Xcheckout/js/model/cart-save-processor',
      'Xortex_Xcheckout/js/update-cart': 'Xortex_Xcheckout/js/update-cart',
      'Xortex_Xcheckout/js/view/delivery-date-block': 'Xortex_Xcheckout/js/view/delivery-date-block',
      'Magento_GiftMessage/js/action/gift-options': 'Magento_GiftMessage/js/action/gift-options',
      'Magento_GiftMessage/js/view/gift-message': 'Magento_GiftMessage/js/view/gift-message',
      'Magento_Checkout/js/model/default-validation-rules': 'Magento_Checkout/js/model/default-validation-rules',
      'Magento_Checkout/js/model/default-validator': 'Magento_Checkout/js/model/default-validator',
      'Magento_Checkout/js/checkout-data': 'Magento_Checkout/js/checkout-data',
      'Magento_Checkout/js/action/select-shipping-method': 'Magento_Checkout/js/action/select-shipping-method',
      'text!Magento_GiftMessage/template/gift-message-item-level.html': 'Magento_GiftMessage/template/gift-message-item-level.html',
      'text!Magento_GiftMessage/template/gift-message.html': 'Magento_GiftMessage/template/gift-message.html',
      'text!Xortex_Xcheckout/template/delivery-date-block.html': 'Xortex_Xcheckout/template/delivery-date-block.html',
      'Magento_Checkout/js/action/select-billing-address': 'Magento_Checkout/js/action/select-billing-address',
      'Magento_Checkout/js/action/select-payment-method': 'Magento_Checkout/js/action/select-payment-method',
      'Magento_Checkout/js/model/default-post-code-resolver': 'Magento_Checkout/js/model/default-post-code-resolver',
      'Magento_Checkout/js/model/new-customer-address': 'Magento_Checkout/js/model/new-customer-address',
      'Amazon_Payment/js/model/amazonPaymentConfig': 'Amazon_Payment/js/model/amazonPaymentConfig',
      'text!Magento_Checkout/template/minicart/subtotal.html': 'Magento_Checkout/template/minicart/subtotal.html',
      'Amasty_CustomerAttributes/js/model/custom-attributes-processor': 'Amasty_CustomerAttributes/js/model/custom-attributes-processor',
      'Amasty_CustomerAttributes/js/model/new-customer-address-mixin': 'Amasty_CustomerAttributes/js/model/new-customer-address-mixin',
      'Magento_Checkout/js/model/address-converter': 'Magento_Checkout/js/model/address-converter',
      'Magento_Checkout/js/model/shipping-rates-validator': 'Magento_Checkout/js/model/shipping-rates-validator',
      'Magento_OfflineShipping/js/view/shipping-rates-validation/tablerate': 'Magento_OfflineShipping/js/view/shipping-rates-validation/tablerate',
      'Magento_OfflineShipping/js/view/shipping-rates-validation/flatrate': 'Magento_OfflineShipping/js/view/shipping-rates-validation/flatrate',
      'Magento_OfflineShipping/js/view/shipping-rates-validation/freeshipping': 'Magento_OfflineShipping/js/view/shipping-rates-validation/freeshipping',
      'Magento_Checkout/js/action/create-billing-address': 'Magento_Checkout/js/action/create-billing-address',
      'text!Magento_Checkout/template/minicart/item/default.html': 'Magento_Checkout/template/minicart/item/default.html',
      'Magento_SalesRule/js/model/payment/discount-messages': 'Magento_SalesRule/js/model/payment/discount-messages',
      'Magento_SalesRule/js/model/coupon': 'Magento_SalesRule/js/model/coupon',
      'Amazon_Payment/js/amazon-widgets-loader': 'Amazon_Payment/js/amazon-widgets-loader',
      'Amazon_Payment/js/amazon-core': 'Amazon_Payment/js/amazon-core',
      'Amazon_Payment/js/model/storage': 'Amazon_Payment/js/model/storage',
      'Amazon_Payment/js/view/checkout/summary/grand-total-mixin': 'Amazon_Payment/js/view/checkout/summary/grand-total-mixin',
      'Magento_Tax/js/view/checkout/cart/totals/grand-total': 'Magento_Tax/js/view/checkout/cart/totals/grand-total',
      'Payone_Core/js/action/select-payment-method-mixin': 'Payone_Core/js/action/select-payment-method-mixin',
      'text!Magento_Tax/template/checkout/minicart/subtotal/totals.html': 'Magento_Tax/template/checkout/minicart/subtotal/totals.html',
      'text!Magento_Catalog/template/product/image_with_borders.html': 'Magento_Catalog/template/product/image_with_borders.html',
      'text!Magento_Checkout/template/minicart/item/price.html': 'Magento_Checkout/template/minicart/item/price.html',
      'mage/trim-input': 'mage/trim-input',
      'Magento_Customer/js/model/customer/address': 'Magento_Customer/js/model/customer/address',
      'Magento_Customer/js/model/customer-addresses': 'Magento_Customer/js/model/customer-addresses',
      'Magento_Customer/js/model/address-list': 'Magento_Customer/js/model/address-list',
      'Magento_Checkout/js/action/create-shipping-address': 'Magento_Checkout/js/action/create-shipping-address',
      'Magento_Checkout/js/model/full-screen-loader': 'Magento_Checkout/js/model/full-screen-loader',
      'Magento_Customer/js/model/customer': 'Magento_Customer/js/model/customer',
      'Magento_Checkout/js/model/resource-url-manager': 'Magento_Checkout/js/model/resource-url-manager',
      'Magento_Checkout/js/action/get-totals': 'Magento_Checkout/js/action/get-totals',
      'Magento_Checkout/js/model/payment/place-order-hooks': 'Magento_Checkout/js/model/payment/place-order-hooks',
      'Magento_Checkout/js/action/set-payment-information-extended': 'Magento_Checkout/js/action/set-payment-information-extended',
      'Magento_SalesRule/js/action/select-payment-method-mixin': 'Magento_SalesRule/js/action/select-payment-method-mixin',
      'Magento_Checkout/js/model/payment-service': 'Magento_Checkout/js/model/payment-service',
      'Magento_Checkout/js/model/checkout-data-resolver': 'Magento_Checkout/js/model/checkout-data-resolver',
      'Magento_Checkout/js/model/shipping-service': 'Magento_Checkout/js/model/shipping-service',
      'Magento_Checkout/js/view/cart/totals': 'Magento_Checkout/js/view/cart/totals',
      'text!Magento_Checkout/template/cart/totals.html': 'Magento_Checkout/template/cart/totals.html',
      'text!Magento_Weee/template/checkout/summary/weee.html': 'Magento_Weee/template/checkout/summary/weee.html',
      'text!Magento_Tax/template/checkout/summary/subtotal.html': 'Magento_Tax/template/checkout/summary/subtotal.html',
      'text!Magento_SalesRule/template/cart/totals/discount.html': 'Magento_SalesRule/template/cart/totals/discount.html',
      'text!Magento_Tax/template/checkout/cart/totals/tax.html': 'Magento_Tax/template/checkout/cart/totals/tax.html',
      'text!Magento_Tax/template/checkout/cart/totals/shipping.html': 'Magento_Tax/template/checkout/cart/totals/shipping.html',
      'text!Magento_Tax/template/checkout/cart/totals/grand-total.html': 'Magento_Tax/template/checkout/cart/totals/grand-total.html',
      'Magento_Checkout/js/checkout-loader': 'Magento_Checkout/js/checkout-loader',
      'text!Magento_Checkout/template/onepage.html': 'Magento_Checkout/template/onepage.html',
      'Magento_Ui/js/form/components/group': 'Magento_Ui/js/form/components/group',
      'Mstage_CWPicker/js/view/summary/item/details': 'Mstage_CWPicker/js/view/summary/item/details',
      'Magento_Checkout/js/view/summary/item/details/thumbnail': 'Magento_Checkout/js/view/summary/item/details/thumbnail',
      'Magento_Checkout/js/view/summary/item/details/message': 'Magento_Checkout/js/view/summary/item/details/message',
      'Payolution_Payments/js/view/payolution/fraud-protection': 'Payolution_Payments/js/view/payolution/fraud-protection',
      'Magento_Checkout/js/view/shipping-information/list': 'Magento_Checkout/js/view/shipping-information/list',
      'Magento_PaypalCaptcha/js/view/payment/list-mixin': 'Magento_PaypalCaptcha/js/view/payment/list-mixin',
      'Magento_Checkout/js/model/payment/renderer-list': 'Magento_Checkout/js/model/payment/renderer-list',
      'Magento_Payment/js/view/payment/payments': 'Magento_Payment/js/view/payment/payments',
      'Magento_Paypal/js/view/payment/paypal-payments': 'Magento_Paypal/js/view/payment/paypal-payments',
      'Magento_OfflinePayments/js/view/payment/offline-payments': 'Magento_OfflinePayments/js/view/payment/offline-payments',
      'Payolution_Payments/js/view/payment/payolution-payments': 'Payolution_Payments/js/view/payment/payolution-payments',
      'Payone_Core/js/view/payment/payone-payments': 'Payone_Core/js/view/payment/payone-payments',
      'Amasty_Orderattr/js/view/order-attributes-information': 'Amasty_Orderattr/js/view/order-attributes-information',
      'Klarna_Kp/js/model/config': 'Klarna_Kp/js/model/config',
      'Klarna_Kp/js/view/payments': 'Klarna_Kp/js/view/payments',
      'Magento_Checkout/js/model/payment/method-converter': 'Magento_Checkout/js/model/payment/method-converter',
      'Klarna_Kp/js/action/override': 'Klarna_Kp/js/action/override',
      'Magento_Checkout/js/view/checkout/placeOrderCaptcha': 'Magento_Checkout/js/view/checkout/placeOrderCaptcha',
      'Magento_Checkout/js/model/payment/additional-validators': 'Magento_Checkout/js/model/payment/additional-validators',
      'Magento_Tax/js/view/checkout/shipping_method/price': 'Magento_Tax/js/view/checkout/shipping_method/price',
      'Magento_CheckoutAgreements/js/model/agreement-validator': 'Magento_CheckoutAgreements/js/model/agreement-validator',
      'Magento_CheckoutAgreements/js/view/agreement-validation': 'Magento_CheckoutAgreements/js/view/agreement-validation',
      'Magento_Checkout/js/view/summary': 'Magento_Checkout/js/view/summary',
      'Magento_Checkout/js/view/summary/cart-items': 'Magento_Checkout/js/view/summary/cart-items',
      'Magento_Checkout/js/view/summary/totals': 'Magento_Checkout/js/view/summary/totals',
      'Magento_CheckoutAgreements/js/model/agreements-modal': 'Magento_CheckoutAgreements/js/model/agreements-modal',
      'Magento_CheckoutAgreements/js/view/checkout-agreements': 'Magento_CheckoutAgreements/js/view/checkout-agreements',
      'Magento_Shipping/js/model/config': 'Magento_Shipping/js/model/config',
      'Magento_Shipping/js/view/checkout/shipping/shipping-policy': 'Magento_Shipping/js/view/checkout/shipping/shipping-policy',
      'Magento_Checkout/js/model/authentication-messages': 'Magento_Checkout/js/model/authentication-messages',
      'Magento_Checkout/js/view/authentication-messages': 'Magento_Checkout/js/view/authentication-messages',
      'Magento_Checkout/js/view/summary/item/details/subtotal': 'Magento_Checkout/js/view/summary/item/details/subtotal',
      'Magento_Tax/js/view/checkout/summary/item/details/subtotal': 'Magento_Tax/js/view/checkout/summary/item/details/subtotal',
      'Magento_Weee/js/view/checkout/summary/item/price/weee': 'Magento_Weee/js/view/checkout/summary/item/price/weee',
      'Magento_Weee/js/view/checkout/summary/item/price/row_incl_tax': 'Magento_Weee/js/view/checkout/summary/item/price/row_incl_tax',
      'Magento_Weee/js/view/checkout/summary/item/price/row_excl_tax': 'Magento_Weee/js/view/checkout/summary/item/price/row_excl_tax',
      'Tigren_AdvancedCheckout/js/model/billing-address/form-popup-state': 'Tigren_AdvancedCheckout/js/model/billing-address/form-popup-state',
      'Magento_Customer/js/action/check-email-availability': 'Magento_Customer/js/action/check-email-availability',
      'Tigren_AdvancedCheckout/js/view/billing-address/list': 'Tigren_AdvancedCheckout/js/view/billing-address/list',
      'Magento_Checkout/js/view/form/element/email': 'Magento_Checkout/js/view/form/element/email',
      'Amazon_Payment/js/view/form/element/email': 'Amazon_Payment/js/view/form/element/email',
      'Magento_Checkout/js/view/authentication': 'Magento_Checkout/js/view/authentication',
      'Magento_Checkout/js/model/customer-email-validator': 'Magento_Checkout/js/model/customer-email-validator',
      'Magento_Checkout/js/view/payment/email-validator': 'Magento_Checkout/js/view/payment/email-validator',
      'Magento_Checkout/js/view/shipping-address/list': 'Magento_Checkout/js/view/shipping-address/list',
      'Tigren_AdvancedCheckout/js/action/edit-billing-address': 'Tigren_AdvancedCheckout/js/action/edit-billing-address',
      'Magento_Checkout/js/model/sidebar': 'Magento_Checkout/js/model/sidebar',
      'Magento_Checkout/js/view/sidebar': 'Magento_Checkout/js/view/sidebar',
      'Amasty_Orderattr/js/model/attribute-sets/payment-attributes': 'Amasty_Orderattr/js/model/attribute-sets/payment-attributes',
      'Amasty_Orderattr/js/action/amasty-validate-form': 'Amasty_Orderattr/js/action/amasty-validate-form',
      'Amasty_Orderattr/js/model/attributes-validator': 'Amasty_Orderattr/js/model/attributes-validator',
      'Amasty_Orderattr/js/view/attributes-validation': 'Amasty_Orderattr/js/view/attributes-validation',
      'Magento_Checkout/js/model/shipping-rate-registry': 'Magento_Checkout/js/model/shipping-rate-registry',
      'Payone_Core/js/action/addresscheck': 'Payone_Core/js/action/addresscheck',
      'text!Magento_Checkout/template/authentication.html': 'Magento_Checkout/template/authentication.html',
      'text!Xortex_Xcheckout/template/sidebar.html': 'Xortex_Xcheckout/template/sidebar.html',
      'Amazon_Payment/js/view/shipping-address/list': 'Amazon_Payment/js/view/shipping-address/list',
      'Amazon_Payment/js/view/shipping-address/inline-form': 'Amazon_Payment/js/view/shipping-address/inline-form',
      'Amazon_Payment/js/messages': 'Amazon_Payment/js/messages',
      'Payone_Core/js/action/edit-address': 'Payone_Core/js/action/edit-address',
      'Payone_Core/js/view/shipping-mixin': 'Payone_Core/js/view/shipping-mixin',
      'Amasty_CustomerAttributes/js/action/set-shipping-information-mixin': 'Amasty_CustomerAttributes/js/action/set-shipping-information-mixin',
      'Magento_Ui/js/lib/validation/utils': 'Magento_Ui/js/lib/validation/utils',
      'Magento_Ui/js/lib/validation/rules': 'Magento_Ui/js/lib/validation/rules',
      'Magento_Ui/js/lib/validation/validator': 'Magento_Ui/js/lib/validation/validator',
      'Magento_Ui/js/form/element/abstract': 'Magento_Ui/js/form/element/abstract',
      'Magento_Ui/js/form/element/select': 'Magento_Ui/js/form/element/select',
      'Magento_Ui/js/form/element/region': 'Magento_Ui/js/form/element/region',
      'Magento_Ui/js/form/element/post-code': 'Magento_Ui/js/form/element/post-code',
      'text!Magento_Checkout/template/summary.html': 'Magento_Checkout/template/summary.html',
      'Magento_Checkout/js/action/get-payment-information': 'Magento_Checkout/js/action/get-payment-information',
      'Magento_Checkout/js/action/set-billing-address': 'Magento_Checkout/js/action/set-billing-address',
      'Magento_Checkout/js/view/payment': 'Magento_Checkout/js/view/payment',
      'Tigren_AdvancedCheckout/js/view/billing': 'Tigren_AdvancedCheckout/js/view/billing',
      'Magento_Checkout/js/view/payment/list': 'Magento_Checkout/js/view/payment/list',
      'Magento_Checkout/js/model/shipping-rate-processor/new-address': 'Magento_Checkout/js/model/shipping-rate-processor/new-address',
      'Magento_Checkout/js/view/estimation': 'Magento_Checkout/js/view/estimation',
      'Magento_Checkout/js/model/shipping-rate-processor/customer-address': 'Magento_Checkout/js/model/shipping-rate-processor/customer-address',
      'Magento_Checkout/js/model/shipping-rate-service': 'Magento_Checkout/js/model/shipping-rate-service',
      'Magento_SalesRule/js/model/shipping-save-processor-mixin': 'Magento_SalesRule/js/model/shipping-save-processor-mixin',
      'Amasty_Orderattr/js/model/attribute-sets/shipping-attributes': 'Amasty_Orderattr/js/model/attribute-sets/shipping-attributes',
      'text!Magento_Checkout/template/summary/totals.html': 'Magento_Checkout/template/summary/totals.html',
      'text!Amasty_Orderattr/template/order-attributes-information.html': 'Amasty_Orderattr/template/order-attributes-information.html',
      'text!Magento_Checkout/template/summary/cart-items.html': 'Magento_Checkout/template/summary/cart-items.html',
      'Magento_Checkout/js/model/payment/method-group': 'Magento_Checkout/js/model/payment/method-group',
      'Amasty_Orderattr/js/action/amasty-prepare-attributes-for-api': 'Amasty_Orderattr/js/action/amasty-prepare-attributes-for-api',
      'Amasty_Orderattr/js/model/validate-and-save': 'Amasty_Orderattr/js/model/validate-and-save',
      'Amasty_Orderattr/js/action/set-shipping-information-mixin': 'Amasty_Orderattr/js/action/set-shipping-information-mixin',
      'text!Magento_Checkout/template/payment.html': 'Magento_Checkout/template/payment.html',
      'text!Magento_Checkout/template/estimation.html': 'Magento_Checkout/template/estimation.html',
      'text!Magento_SalesRule/template/summary/discount.html': 'Magento_SalesRule/template/summary/discount.html',
      'text!Magento_Tax/template/checkout/summary/shipping.html': 'Magento_Tax/template/checkout/summary/shipping.html',
      'text!Magento_Tax/template/checkout/summary/tax.html': 'Magento_Tax/template/checkout/summary/tax.html',
      'text!Payolution_Payments/template/checkout/summary/grand-total.html': 'Payolution_Payments/template/checkout/summary/grand-total.html',
      'CleverReach_CleverReachIntegration/js/model/shipping-save-processor/payload-extender-override': 'CleverReach_CleverReachIntegration/js/model/shipping-save-processor/payload-extender-override',
      'Xortex_Xcheckout/js/model/shipping-save-processor/default': 'Xortex_Xcheckout/js/model/shipping-save-processor/default',
      'Magento_Checkout/js/model/shipping-save-processor': 'Magento_Checkout/js/model/shipping-save-processor',
      'Magento_Checkout/js/action/set-shipping-information': 'Magento_Checkout/js/action/set-shipping-information',
      'Magento_Checkout/js/view/shipping': 'Magento_Checkout/js/view/shipping',
      'Tigren_AdvancedCheckout/js/view/shipping': 'Tigren_AdvancedCheckout/js/view/shipping',
      'Amazon_Payment/js/view/shipping': 'Amazon_Payment/js/view/shipping',
      'text!Mstage_CWPicker/template/summary/item/details.html': 'Mstage_CWPicker/template/summary/item/details.html',
      'text!Magento_Checkout/template/payment-methods/list.html': 'Magento_Checkout/template/payment-methods/list.html',
      'Magento_Checkout/js/view/progress-bar': 'Magento_Checkout/js/view/progress-bar',
      'text!Mstage_CWPicker/template/summary/item/details/thumbnail.html': 'Magento_Checkout/template/summary/item/details/thumbnail.html',
      'text!Magento_Tax/template/checkout/summary/item/details/subtotal.html': 'Magento_Tax/template/checkout/summary/item/details/subtotal.html',
      'Magento_Checkout/js/view/shipping-information/address-renderer/default': 'Magento_Checkout/js/view/shipping-information/address-renderer/default',
      'Magento_Checkout/js/view/shipping-information': 'Magento_Checkout/js/view/shipping-information',
      'text!Tigren_AdvancedCheckout/template/shipping.html': 'Tigren_AdvancedCheckout/template/shipping.html',
      'text!Magento_Checkout/template/progress-bar.html': 'Magento_Checkout/template/progress-bar.html',
      'text!Magento_Weee/template/checkout/summary/item/price/row_incl_tax.html': 'Magento_Weee/template/checkout/summary/item/price/row_incl_tax.html',
      'Xortex_Xcheckout/js/view/delivery-information': 'Xortex_Xcheckout/js/view/delivery-information',
      'Xortex_Xcheckout/js/view/info': 'Xortex_Xcheckout/js/view/info',
      'text!Magento_Checkout/template/shipping-address/form.html': 'Magento_Checkout/template/shipping-address/form.html',
      'text!CleverReach_CleverReachIntegration/template/form/element/email.html': 'CleverReach_CleverReachIntegration/template/form/element/email.html',
      'text!Magento_Checkout/template/shipping-address/list.html': 'Magento_Checkout/template/shipping-address/list.html',
      'text!Magento_Shipping/template/checkout/shipping/shipping-policy.html': 'Magento_Shipping/template/checkout/shipping/shipping-policy.html',
      'text!Tigren_AdvancedCheckout/template/billing.html': 'Tigren_AdvancedCheckout/template/billing.html',
      'text!Xortex_Xcheckout/template/delivery-information.html': 'Xortex_Xcheckout/template/delivery-information.html',
      'text!Magento_Checkout/template/shipping-information.html': 'Magento_Checkout/template/shipping-information.html',
      'text!Xortex_Xcheckout/template/info.html': 'Xortex_Xcheckout/template/info.html',
      'text!Amazon_Payment/template/shipping-address/inline-form.html': 'Amazon_Payment/template/shipping-address/inline-form.html',
      'text!ui/template/form/field.html': 'Magento_Ui/templates/form/field.html',
      'text!ui/template/form/element/helper/tooltip.html': 'Magento_Ui/templates/form/element/helper/tooltip.html',
      'text!ui/template/group/group.html': 'Magento_Ui/templates/group/group.html',
      'text!Tigren_AdvancedCheckout/template/billing-address/custom-list.html': 'Tigren_AdvancedCheckout/template/billing-address/custom-list.html',
      'text!CleverReach_CleverReachIntegration/template/form/element/newsletter-subscribe.html': 'CleverReach_CleverReachIntegration/template/form/element/newsletter-subscribe.html',
      'text!Tigren_AdvancedCheckout/template/billing-address/form.html': 'Tigren_AdvancedCheckout/template/billing-address/form.html',
      'mage/dropdowns': 'mage/dropdowns',
      'text!ui/template/form/element/input.html': 'Magento_Ui/templates/form/element/input.html',
      'text!ui/template/form/element/select.html': 'Magento_Ui/templates/form/element/select.html',
      'text!Magento_Checkout/template/shipping-address/shipping-method-list.html': 'Magento_Checkout/template/shipping-address/shipping-method-list.html',
      'text!Magento_Checkout/template/shipping-address/shipping-method-item.html': 'Magento_Checkout/template/shipping-address/shipping-method-item.html',
      'text!Magento_Tax/template/checkout/shipping_method/price.html': 'Magento_Tax/template/checkout/shipping_method/price.html'
    }
  }
]